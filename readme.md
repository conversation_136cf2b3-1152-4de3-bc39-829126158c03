# Sage Theme in Docker

This is a Docker image for Sage theme development.

## Prerequisites

1. Docker
2. Docker Compose
3. Make
4. Composer
5. Node.js
6. NPM

## Installation

1. Clone this repository
2. Create `.env.local` file in the root directory, if not created
    1. Add the following line to the file: `LOCAL_PORT=8005`
    2. Add optional line to the file: `WP_DEBUG=true`
3. Go into `src` directory
4. Do `composer install`
5. Do `npm ci`
6. Do `npm run build` for the first time (it will create `public` directory)

## Development
1. Run `make start` to start the container
2. Now you can access the site at `http://localhost:8005`
3. For hot reloading, run `npm run dev` in the `src` directory

## Build
1. Run `make all` to build the theme
2. The theme will be available in the `build` directory

---

## Features
- WordPress development with [Sage](https://roots.io/sage/) (Blade, Tailwind CSS, modern workflow)
- Dockerized local environment (WordPress, MySQL, Adminer, Nginx proxy)
- Hot reloading and asset building with Bud
- Easy build and deployment scripts
- Custom icon and hook/filter generators

## Directory Structure
```
├── src/                # Sage theme source code
│   ├── app/            # PHP theme logic (setup, filters, providers, etc.)
│   ├── resources/      # Blade views, styles, scripts, images
│   ├── public/         # Built assets (after build)
│   ├── package.json    # Node dependencies
│   ├── composer.json   # PHP dependencies
│   └── ...
├── docker/             # Docker and Nginx configs
├── build/              # Output directory after build
├── bin/                # Helper scripts (build, icon/hook/filter generators)
├── Makefile            # Make commands for workflow
```

## Accessing WordPress Admin
- After running `make start`, complete the WordPress installation at `http://localhost:8005/wp-admin`.
- Set your own admin username and password during the setup wizard (no default credentials are set by the Docker config).
- Database credentials (for Adminer or manual config):
  - Host: `scd-db`
  - User: `wp`
  - Password: `pass`
  - Database: `wp`

## Stopping & Removing Containers
- To stop containers: `docker compose --file docker/docker-compose.yaml --env-file .env.local down`
- To remove all data/volumes: add `-v` to the above command

## Troubleshooting
- If you see errors about missing dependencies, ensure you ran all install steps in the `src` directory.
- For port conflicts, change `LOCAL_PORT` in `.env.local`.
- For Xdebug issues, check your IDE and the `uploads.ini`/Docker config.
- For more, see [Sage documentation](https://roots.io/sage/docs/).

## Useful Links
- [Sage Documentation](https://roots.io/sage/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [Bud.js](https://bud.js.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [Laravel Blade](https://laravel.com/docs/master/blade)
