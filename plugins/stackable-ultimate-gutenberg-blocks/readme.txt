=== Stackable - Page Builder Gutenberg Blocks ===
Contributors: bfintal, gambitph, freemius
Tags: blocks, gutenberg, gutenberg blocks, page builder, WordPress blocks
Requires at least: 6.5.5
Tested up to: 6.8
Requires PHP: 7.3
Stable tag: 3.15.3
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Custom Blocks that transform your WordPress Block Editor into a page builder

== Description ==

**The Ultimate Companion to the WordPress Block Editor**

[Stackable](https://wpstackable.com?utm_source=wp-repo&utm_campaign=readme&utm_medium=link) is the ultimate trusted block plugin you've been waiting for. Build dynamic websites with our powerful yet lightweight custom WordPress blocks, global design system, ready-made designs, UI Kits, global settings, and advanced customization options all whilst boasting speedy performance. Have the confidence to easily design professional websites that stand out using a new page building experience for Gutenberg - the WordPress Block Editor.

> [Try our live demo](https://wpstackable.com/demo/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link) | [Join the Facebook Community](https://www.facebook.com/groups/wpstackable/)

[youtube https://www.youtube.com/watch?v=P3xG3t-QYjw]

## Transform Gutenberg into a Page Builder.

Stackable is the all-in-one block plugin for creating stunning websites by transforming the WordPress Block Editor into a Page Builder. Stackable has helped thousands of bloggers, merchants, marketers, designers and web development professionals make the most out of the WordPress Block Editor through fast, powerful and intuitive features.

## Ready Made Templates, Block Designs and Wireframes

Enjoy an impressive Design Library to jumpstart your designing process. Never start from scratch and design like a pro with the help of our stunning and professionally-made designs.

- Dozens of UI Kit designs
- Hundreds of ready-made block designs
- Dozens of block layouts
- Various shape separator designs

## Powerful Custom Blocks

Get 42 flexible custom WordPress blocks that are feature-rich and look good out of the box, and provide a robust foundation for any website even without a single line of code.

You can choose which of these blocks you need, depending on your workflow. You can also disable blocks if you don't need them.

**Essential Blocks**

- Advanced Columns Block — [View Block](https://wpstackable.com/columns-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Advanced Heading Block - [View Block](https://wpstackable.com/advanced-heading-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Advanced Text Block - [View Block](https://wpstackable.com/advanced-text-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Advanced Image Block
- Icon List Block — [View Block](https://wpstackable.com/icon-list-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Button Block — [View Block](https://wpstackable.com/button-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Icon Button Block
- Icon Block — [View Block](https://wpstackable.com/icon-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

**Special Blocks**

- Carousel Block - [View Block](https://wpstackable.com/carousel-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Horizontal Scroller Block - [View Block](https://wpstackable.com/horizontal-scroller-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Tabs Block - [View Block](https://wpstackable.com/tabs-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Countdown Block - [View Block](https://wpstackable.com/countdown-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Timeline Block - [View Block](https://wpstackable.com/timeline-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Table of Contents Block - [View Block](https://wpstackable.com/table-of-contents-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Posts Block — [View Block](https://wpstackable.com/blog-posts-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Image Box Block — [View Block](https://wpstackable.com/image-box-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Video Popup Block — [View Block](https://wpstackable.com/video-popup-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Progress Circle Block — [View Block](https://wpstackable.com/progress-circle-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Progress Bar Block — [View Block](https://wpstackable.com/progress-bar-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Accordion Block — [View Block](https://wpstackable.com/accordion-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Map Block — [View Block](https://wpstackable.com/map-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Icon Label Block
- Social Buttons Block
- Card Block — [View Block](https://wpstackable.com/card-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Count Up Block — [View Block](https://wpstackable.com/count-up-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Number Box Block — [View Block](https://wpstackable.com/number-box-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Notification Block — [View Block](https://wpstackable.com/notification-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Expand / Show More Block — [View Block](https://wpstackable.com/expand-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Separator Block — [View Block](https://wpstackable.com/separator-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Subtitle Block
- Price Block
- Divider Block
- Spacer Block

**Section Blocks**

- Hero Block — [View Block](https://wpstackable.com/header-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Call to Action Block — [View Block](https://wpstackable.com/call-to-action-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Feature Block — [View Block](https://wpstackable.com/feature-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Feature Grid Block — [View Block](https://wpstackable.com/feature-grid-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Pricing Box Block — [View Block](https://wpstackable.com/pricing-table-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Icon Box Block
- Testimonial Block — [View Block](https://wpstackable.com/testimonial-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Team Members Block — [View Block](https://wpstackable.com/team-member-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Blockquote Block — [View Block](https://wpstackable.com/blockquote-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

## Page Builder-like Design Options

Turn the WordPress Block Editor into a page builder. Fine-tune your creations with a wide range of familiar web design options.

- Global Design System
- Multiple Block Layouts
- Flexbox Controls
- Image and Video Lightbox
- Save Block Defaults
- Customize block hover styles
- Block Background and Image Color Settings
- Block Typography Settings
- Image Shapes and Settings
- Advanced Gradient Color Picker
- Advanced Icon Options
- Advanced Column and Spacing Settings
- Global Colors & Typography Settings
- Responsiveness
    - Tablet and Mobile Column Arrangement
    - Live Responsive Editing
    - Ability to tweak designs for Tablet and Mobile views
    - Specify how Columns collapse in Tablet and Mobile
    - Hide / Show Specific Blocks on Desktop, Tablet or Mobile
    - Custom Tablet and Mobile breakpoints
- Custom `data-*` attributes

## Fast Page Loading Speed

Optimize your website’s performance, and get lightning fast page loading to make your site visitors stay. Have the chance to maximize your page speed insights and achieve high Core Web Vitals and higher SEO rankings.

- Loads the smallest file size possible of CSS and JS files in the frontend, ~ only 7.8kb total
- Adds almost no PHP server overhead for fast page loads
- Zero Bloat, no jQuery, no dependencies
- Optimized page loading with focus on Core Web Vitals
- Responsive image loading for faster browsing speeds in mobile devices
- Compatible with Optimization Plugins and use optimization techniques such as combining CSS and JS files and minification

## Integrations & Compatibility

Make your page building experience more well-rounded by using other popular tools. We've seamlessly integrated with these essential third-party plugins and tools:

- WPML
- Weglot
- Blocksy
- Toolset
- Font Awesome
- Google Fonts
- [see our full list of integrations and compatibility](https://wpstackable.com/compatibility/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

## Premium Features

Take web designing to the next level. Stackable Premium helps you unlock the full potential of the WordPress Block Editor with awesome advanced features that will give you the ability to create high-end websites.

### Dynamic Content (Premium)

Create dynamic WordPress websites that cater to the specific interests of visitors.

- Dynamic content
- Fully customize your query loops
- Site Custom Fields
- Conditionally display blocks

### More Integrations (Premium)

In premium, you get more integrations.

- WooCommerce
- ACF
- Metabox
- JetEngine

### Agency Tools (Premium)

Cater to more clients effectively and efficiently with our Agency Tools, which were specially made for web design professionals.

- Block CSS Customizer
- Role Manager

### Motion Effects (Premium)

Add animations that will bring your site to life and make it more visually engaging.

- Scroll Animations
- Entrance Animations
- Transform & Transition Effects

### More Blocks (Premium)

- Load More Block (for the Blog Posts Block)
- Pagination Block (for the Blog Posts Block)

> [Try our live demo](https://wpstackable.com/demo/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link) | [Learn more](https://wpstackable.com?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

### News Article Updates

- [v3.15 Global Design System](https://wpstackable.com/blog/introducing-the-new-stackable-global-design-system/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.14 Icon Library & Granular Plugin Settings](https://wpstackable.com/blog/icon-library-granular-plugin-settings/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.13 Improved Stackable Server Performance](https://wpstackable.com/blog/improved-server-performance/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.12 WooCommerce Integration and Enhanced Color, Gradient and Opacity Settings](https://wpstackable.com/blog/woocommerce-integration/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.11 New Timeline Block](https://wpstackable.com/blog/introducing-new-timeline-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.10 New Tabs Block](https://wpstackable.com/blog/introducing-new-tabs-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.9 New Carousel Block](https://wpstackable.com/blog/introducing-new-carousel-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.8 New Stackable UI](https://wpstackable.com/blog/introducing-new-stackable-ui/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.7 New Horizontal Scroller and Countdown Blocks](https://wpstackable.com/blog/introducing-new-horizontal-scroller-and-countdown-blocks/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.6 New Progress Circle and Progress Bar Blocks](https://wpstackable.com/blog/introducing-new-progress-circle-and-progress-bar-blocks/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.5 No Bloat & Improved Performance](https://wpstackable.com/blog/improved-performance/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.4 Map Block](https://wpstackable.com/blog/introducing-map-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.3 Saving Block Defaults](https://wpstackable.com/blog/saving-block-defaults/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.2 Table of Contents Block](https://wpstackable.com/blog/table-of-contents-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.1 Wireframes](https://wpstackable.com/blog/introducing-wireframes/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v3.0 How To Shift From Version 2 to Version 3](https://wpstackable.com/blog/how-to-shift-from-version-2-to-version-3/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.17 Custom Attributes and Optimization](https://wpstackable.com/blog/custom-attributes-and-optimization/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.16 Clickable Containers and Pagination](https://wpstackable.com/blog/clickable-containers-and-pagination/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.15 Introducing Dynamic Content](https://wpstackable.com/blog/introducing-dynamic-content/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.14 Copy and Paste Block Styles](https://wpstackable.com/blog/copy-and-paste-block-styles/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.13 UI Kits, Borders & Improved Performance](https://wpstackable.com/blog/ui-kits-borders/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.12 Better User Interface and User Experience](https://wpstackable.com/blog/better-user-interface-and-user-experience/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.11 Introducing Global Colors and Global Typography](https://wpstackable.com/blog/global-colors-and-global-typography/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.10 Better Responsive Controls, Better List Block Icons and Low-Highlight Effect](https://wpstackable.com/blog/better-responsive-controls-better-list-block-icons-low-highlight-effect/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.9 Live Responsive Editing](https://wpstackable.com/blog/introducing-live-responsive-editing/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.8 Role Manager for Gutenberg](https://wpstackable.com/blog/introducing-role-manager-for-gutenberg/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.7 New Icon Block, Amazing Icons and Font Awesome Pro](https://wpstackable.com/blog/new-icon-block-amazing-icons-and-font-awesome-pro/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.6 New Advanced Blocks and Load More Blog Posts Button](https://wpstackable.com/blog/new-advanced-blocks-and-load-more-blog-posts-button/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.5 Better Onboarding for First Time Users](https://wpstackable.com/blog/better-onboarding-for-first-time-users/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.4 Introducing the Advanced Columns & Grid Block](https://wpstackable.com/blog/introducing-the-advanced-columns-and-grid-block/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.3 Introducing the Design Library and New Block Designs](https://wpstackable.com/blog/introducing-the-design-library-and-new-block-designs/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.2 Welcome Video, Better Separators and Auto-Block Recovery](https://wpstackable.com/blog/welcome-video-better-separators-and-auto-block-recovery/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.1 Help Video Snippets and Auto-Expand Settings](https://wpstackable.com/blog/help-video-snippets-auto-expand-settings/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- [v2.0 Version 2 is Out!](https://wpstackable.com/blog/version-2-is-out/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

### Learn More About Stackable - Page Builder Gutenberg Blocks & Designs

- Read our [documentation and tutorials](https://docs.wpstackable.com?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Visit our [site wpstackable.com](https://wpstackable.com?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)
- Subscribe to our [newsletter](http://eepurl.com/dJY9xI)

### Enjoying Stackable Blocks?

- Join the [Stackable Community in Facebook](https://www.facebook.com/groups/wpstackable/)
- Follow us in [Twitter @wpstackable](https://twitter.com/wpstackable)
- [Leave us a rating](https://wordpress.org/support/plugin/stackable-ultimate-gutenberg-blocks/reviews/?rate=5#new-post)

> ** Those marked with asterisks are part of Stackable Premium

== Installation ==

= Minimum Requirements =

You'll need WordPress version 6.3 or higher for this to work.

== Frequently Asked Questions ==

**Did you just upgrade? Learn here about the New Inspector UI in v3.8**

[youtube https://www.youtube.com/watch?v=RukT93hvzn8]

**Is Stackable Free?**

Yes, Stackable is free forever.

We have a premium version that adds more designs and advanced features. You may want to check [Stackable Premium here](https://wpstackable.com/premium/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link).

**Do I need to know how to code to use Stackable?**

No, you don't need to know a single line of code when using Stackable

**Do you have a live demo?**

Yes, we have [live demo that you can check out here](https://wpstackable.com/demo/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link).

**What are Blocks?**

Blocks are the new shortcodes in WordPress 5.0 / Gutenberg. They're the basic elements that you add into your content to build your pages, like buttons, cards, videos, etc.

Stackable gives you an extensive collection of blocks that allows you to flexibly create any kind of professional landing page and front page.

**What are UI Kits?**

UI kits are a beautiful and cohesive collection of our block designs for budiling full sites easily. These kits offer a wide variety of web design styles and categories that allow you to kick start your design process right away

**What themes can I use with Stackable?**

Stackable should work with any theme.

**Can I disable blocks that I do not use?**

Yes, you can manage your blocks and choose what blocks to enable/disable.

**Can I use this plugin with other block plugins?**

Yes! Stackable blocks play well with other blocks.

**Can I use this add-on for other page builders I'm using?**

Nope. Stackable only works with Gutenberg, the new WordPress editor.

== Screenshots ==

1. Responsive Gutenberg Blocks
2. Page Builder-like Block Options
3. Global Settings that affect your entire site
4. Powerful Custom Gutenberg Blocks
5. Ready-made Design Library

== Upgrade Notice ==

== Changelog ==

= 3.15.3 =
* Fixed: WordPress 6.8 compatibility
* Fixed: Disabling Show Global Color Schemes can cause block errors #3481
* Fixed: Block settings may not show up in the Theme Customizer #3484
* Fixed: Post block - sometimes the post date does not follow the timezone #3458

= 3.15.2 =
* Fixed: Hotfix new styles will not be applied now unless global color schemes and global styling are used

= 3.15.1 =
* Fixed: Hotfix background and container paddings having effects on hover
* Fixed: Hotfix remove theme compatibility for the time being for global color schemes since they're affecting colors

= 3.15.0 =
[Global Design System](https://wpstackable.com/blog/introducing-the-new-stackable-global-design-system/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

* New: Global Spacing and Layout - adjust spacing and layout across all blocks #3446
* New: Global Color Scheme - create color schemes and apply it to your entire site #3457 #371
* New: Global Typography Font Pairs - choose from preset font family pairs #3449 #370
* New: Added button text in Global Typography #3463
* New: Added modern font stacks to the font family option #3439
* Change: Google Fonts are now loaded asynchronously #df0adcc
* Fixed: Added placeholder text to the font family option #3440
* Fixed: Prevent multiple fetches in the admin settings page #3441
* Fixed: Google Fonts loaded with a delay in the editor #df0adcc
* Fixed: Icon Label Block - Icon Gap Resets to a Different Value When an Icon Size Is Set #3426

= 3.14.2 =
* New: Added support for Blocksy modern font stacks #3435
* New: Responsive breakpoint settings will now show Blocksy's breakpoints as the default value ce727ce
* Fixed: Responsive breakpoints - added better support for dynamically inserted content 6911ebe
* Fixed: Responsive breakpoints - now working properly in preview in some styles #3394
* Fixed: Number box - typography gradient color now works properly #3307
* Fixed: Icon list - typography gradient color now works properly #3309
* Fixed: Columns - nested columns may show up in different order on tablet or mobile if parent has different order #3398
* Fixed: Accordion block - may open upward if parent has different column arrangement #3406
* Fixed: Settings may appear blank because of browser caching #3419
* Fixed: Global settings - button does not appear in older WP versions #3425

= 3.14.1 =
* Fixed: Hotfix for EWWW Image Optimizer compatibility - freezing in the frontend if lazy loading and auto scaling are both enabled #3415

= 3.14.0 =
[Icon Library & Granular Plugin Settings](https://wpstackable.com/blog/icon-library-granular-plugin-settings/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

* New: Granular plugin settings - enhanced settings page for better control of plugin features #3354
* New: Added option to use an empty alt tag in images #3376
* Fixed: Image block - using small images no longer stretch #3239
* Fixed: Image block - switching between % and px width now doesn't cause the image to be too small or too big #3268
* Fixed: Dynamic Content - better compatibility with Blocksy Content Blocks #368
* Fixed: EWWW Image Optimizer compatibility - no longer causes blurry images #3296

= 3.13.13 =
* New: Icon Library (Premium feature) - you can now upload custom SVGs and re-use them for the Icon Block! #361 #3317
* Fixed: Text block: typing in the inspector will no longer escape html entities #3399
* Fixed: Typing in the middle of an option in the inspector will no longer move the cursor to the end #3396
* Fixed: Updated Freemius SDK to v2.11.0

= 3.13.12 =
* Fixed: Pasting text creates a new block instead of pasting on the current block #3371
* Fixed: Pasting on icon list block makes a new block and loses cursor placement #3382
* Fixed: Blocks inside a WooCommerce shop page can show css styles #3381
* Fixed: Clearing the icon from the icon picker can make the icon blank #3348
* Fixed: Sanitize titles for lightbox #3390
* Fixed: Enhanced text sanitazion #3391

= 3.13.11 =
* Fixed: Icon Label block: Error when missing an Icon block #3368
* Fixed: Disable Dynamic Content when inside the Customizer #3352
* Fixed: Allow pasting of blocks when focused on a Stackable Text Block #3330

= 3.13.10 =
* Fixed: Compatibility with WordPress 6.7 #3336
* Fixed: Better support for custom SVGs in the icon picker #3265
* Fixed: Cleanup of some code related to the previous performance update #3320
* Fixed: Accordion block: text can become unselectable #3350
* Fixed: Save default blocks: saving can be called multiple times and can cause the browser to hang #3355
* Fixed: Map block: prevent error when using a custom map marker #3362

= 3.13.9 =
* Fixed: Text blocks: text gradient color doesn't show for misspelled words #3305
* Fixed: Button block: adding a button block now places the cursor inside the button ##3324
* Fixed: Conditional display: inspector options do not re-render when changing values #3342
* Fixed: Optimized CSS: mobile styles can overwrite tablet styles sometimes (to fix, please re-save the page) #3345

= 3.13.8 =
* Fixed: Possible editor freezing when using deprecated icon lists inside patterns #3332
* Fixed: Timeline block: last timeline doesn't cut off in mobile view (part 2 of fix) #3292
* Fixed: Icon List block: can produce an error when migrating from an old version #3334
* Fixed: Prevent possible PHP error when calling kses too early

= 3.13.7 =
* Fixed: Stylesheets can sometimes not load in the frontend
* Fixed: SVG error when using custom SVGs with a use tag #3323
* Fixed: Carousel block: column alignment stops working for nested columns #3327
* Fixed: Global colors: color not copied over when pasting in another site #3329
* Fixed: Only do kses fixes when the user can edit posts

= 3.13.6 =
* New: Drastically improved performance of the Block Editor #3261
* New: Added option to enable Stackable Text block as the default editor block #3279
* New: Added `--stk-transition-default` CSS variable for easier transition customization #3266
* Change: Disable Inner Column block margins in Carousel blocks #3173
* Fixed: Posts block "<a" text may appear in post excerpts #3301
* Fixed: Improved performance of Stackable Global Colors #3299
* Fixed: Columns block: some settings may not work when used inside a Query Loop block #3109
* Fixed: Carousel block: slide orders change if used inside a Columns block with different responsive column orders #3113
* Fixed: Carousel block: fixed jumping and popup issues when infinite scrolling and autoplay are enabled #3287
* Fixed: Posts block: Load more can load the wrong data if you have multiple Posts block in a page #3189
* Fixed: Icon List block: transforming to this block can cause errors #3277
* Fixed: Horizontal Scroller block: images no longer dragged when dragging to scroll #3280
* Fixed: Timeline block: last timeline doesn't cut off in mobile view #3292

= 3.13.5 =
* Fixed: Otter Blocks compatibility #3276
* Fixed: Carousel block: Same-page navigation now works properly #3098
* Fixed: Some custom SVGs can interfere with othr uploaded custom SVGs #3265

= 3.13.4 =
* Fixed: Improved WordPress 6.6 compatibility and performance #357 #3258
* Fixed: Text block performance #3271
* Fixed: Carousel Block: Aria role errors when using infinite scrolling #3269
* Fixed: Media query shortcuts not working in Custom CSS #3214
* Fixed: Posts Block: Date now follows current website language setting #3241

= 3.13.3 =
* New: Enhanced WordPress 6.6 compatibility
* New: You can now select multiple adjacent same-type blocks and adjust their inspector settings at the same time #2711
* New: You can now pick a font family from the available ones in the Block Theme fonts or uploaded fonts #3195 #3244
* New: Faster pattern rendering in the editor #3229 #3225
* New: Added admin warning when settings page REST API fails #2882
* Change: Icon Label block: reworked Icon Gap option, now properly measures gap between icon and text #3126
* Fixed: Dynamic Content: using custom date format now works correctly #2570
* Fixed: Aspect Ratio tablet & mobile settings now working properly #3232
* Fixed: Hover transitions not smoothly transitioning #3207
* Fixed: Shadow color support for theme colors #3176
* Fixed: Bottom separator can sometimes not go behind the content #3061
* Fixed: Team Member block: fixed cover layout issue #2985

= 3.13.2 =
* New: Added new Aspect Ratio option to Image block and relevant blocks #3200
* New: Added filter stackable_force_css_load - when returning true, Stackable css files will always be loaded in the frontend even if no blocks are present #3218
* New: Accordion block - accordions show open when printing the webpage #3186
* Fixed: Posts block - empty excerpts may show some excerpt markup #3185
* Fixed: Posts block - excerpt length not being followed #3210
* Fixed: Load more block - clicking rapidly can load the same posts #2280
* Fixed: Accordion block - prevent highlighting text when toggling #3096
* Fixed: Accordion block - some icons in the content rotate when opening the accordion #3211
* Fixed: Table of Contents block - excluding headings can affect another TOC block in the same page #3197
* Fixed: Inner column block - top and bottom margins looks different in Firefox #3193
* Fixed: Some CSS validation errors #3087
* Fixed: Some custom SVG icons may not display properly #3103
* Fixed: Image blob shapes sometimes look different in Firefox #3042
* Fixed: Nested selectors in Custom CSS now work properly #3164
* Fixed: Security improvements

= 3.13.1 =
* Fixed: (Hotfix update): Style codes can show on some instances
* Fixed: Copy and paste styles now work correctly #350

= 3.13.0 =
[Improved Stackable Server Performance](https://wpstackable.com/blog/improved-server-performance/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

* New: x2-x3 plugin performance - improved plugin execution times, reduced memory usage, and faster loading times #340 #3169 #347 #3178
* New: Performance improvements - options are now autoloaded correctly for better database performance #343 #3175
* New: Role Manager - when in content editing mode, a console warning will appear #2911
* Fixed: Dynamic Content - detected custom fields now show up in the Site Editor ##344
* Fixed: Carousel block - infinite scrolling also works for the previous button #3143
* Fixed: Full width buttons and flex wrapping now work correctly #3181
* Fixed: Button text now doesn't wrap - they look more like what you get in the editor #3181
* Fixed: Button group vertical alignment now works correctly on multiple buttons #3181
* Fixed: Copy and paste styles now work correctly if native blocks are used #341
* Fixed: ACF Dynamic Content line breaks in wysiwyg fields now do not get added #3110
* Fixed: Block preview error when using Stackable blocks in block patterns #3191

= 3.12.17 =
* Fixed: (Hotfix update): Display Conditions not working in some cases

= 3.12.16 =
* Fixed: Improved database performance of dynamic content when dealing with auto-detected meta fields #337
* Fixed: Transforming blocks to columns can produce block errors #3151
* Fixed: Dynamic content - possible Metabox error when using some fields #3156
* Fixed: Dynamic content - warning when using a featured image that has already been deleted #3168
* Fixed: Security improvements

= 3.12.15 =
* New: Tabs Block - added new option to set anchor links per tab that you can use to open each tab #3124
* New: Accordion Block - added support for anchor links that you can use to open each accordion block #3136
* Fixed: Accordion Block - now shows gradient colors with the correct z-index #3138
* Fixed: Accordion Block - removed video background option since it's not supported by browsers #3138
* Fixed: Metabox Settings - now shows properly other options registered in settings pages #335
* Fixed: Video Popup Block - prevent theme from adding button styles to the video popup #3121
* Fixed: Carousel Block - no longer clones slides when the screen is resized #3132
* Fixed: Carousel Block - DOM events now work correctly in slides when infinite scrolling is enabled #3137
* Fixed: Columns Block - block error when deleting a block in Firefox #3148

= 3.12.14 =
* Fixed: Table of Contents Block - WordPress 6.5 compatibility in the Site Editor #3133
* Fixed: Table of Contents Block - Auto-generate anchors now work after the editor refreshes #3133
* Fixed: (Hotfix) Rolled back separator fix since it was producing errors #3131
* Fixed: Some blocks may produce errors when editing in multisite when editing as a non-super user #3130
* Fixed: In multisite, prevent parts of custom icons from being stripped #3130
* Fixed: Buttons block - full width now occupies the entire full width correctly #2991

= 3.12.13 =
* Fixed: (Hotfix update) Rolled back some optimizations that caused styling issues in the frontend after saving.

= 3.12.12 =
* New: WordPress 6.5 compatibility #3115 #3116 #333
* New: Improved editor and saving performance #3119 #3114
* New: Added support for Metabox settings #330
* New: Columns Block - you can now wrap columns in desktop view #3072
* New: Carousel Block - new infinite scroll option #3060
* New: Added thumbnail option for video backgrounds #3040
* New: Border color picker now has the new opacity slider #3074
* New: Design Library - added error logger in the network tab for easy checking of errors #3112
* New: WooCommerce - dynamic content product description now has option to not strip HTML tags #331
* Change: Removed Navigation Panel feature - it was already disabled by default back in 3.10.2 #3119
* Fixed: Prevent bottom separator from overlapping content #3081
* Fixed: Card block - fixed possible block error when reseting layouts #3080
* Fixed: Posts block - better display for horizontal layouts with large featured image #3108
* Fixed: Tabs block - changing styles now updates the border radius correctly #3106
* Fixed: Lightbox - prevent possible PHP error #3083
* Fixed: Security improvements

= 3.12.11 =
* New: You can now use YouTube shorts and unlisted Vimeo videos in the Video Popup block
* Fixed: Ordered Icon List block numerals no longer wrap to the next line
* Fixed: Native list block behaviors no longer create Stackable Icon List Item blocks
* Fixed: Clicking WooCommerce add to cart button no longer changes the Tabs block to the first tab
* Fixed: No longer need to have v2 compatibility enabled for the random order option to work in the Posts block
* Fixed: Carousel block default icon color can now be changed correctly
* Fixed: Pasting a bullet list no longer converts it to a Stackable Icon List block
* Fixed: Frontend scripts now will not initialize more than once
* Fixed: Updated .pot file to allow manual JS strings translations

= 3.12.10 =
* Fixed: (Hotfix update) Some themes can add bullets or numbers on the Icon List block

= 3.12.9 =
* New: Revamp of Icon List Block (please expect some slight changes in the block's appearance after the block migrates)
* New: New Icon List Block options: item borders, vertical icon alignment, and more
* New: Cleaner output: inlined script for animations is no longer added if there are no animations
* New: Dynamic Content: you can now search for a post ID in the post search field
* Fixed: Block error when adding an anchor on a Button block
* Fixed: Clicking once on links inside the Carousel block now works in iOS
* Fixed: Anchors are not anymore copied with our copy and paste style feature
* Fixed: Error when using a category that's shown as a link
* Fixed: Compatibility with Thrive themes global header
* Fixed: Compatibility with Core Framework: inspector panels now open properly

= 3.12.8 =
* New: (Hotfix update) Added new option "Lazy Load Images within Carousels" that fixes carousel spacing issues with some image lazy loading solutions
* Fixed: (Hotfix update) Turned carousel image lazy loading fix into an option instead of a default
* Fixed: Dropdown arrows in the block resizers shows up as a square / no icon
* Fixed: Accessibility warning in lightboxes that aria-* attributes are misspelled

= 3.12.7 =
* New: Better multisite support
* New: Multisite network license key activation is now possible
* New: Font Awesome 6 support, you can now choose between Font Awesome 5 and 6 icon libraries from the settings
* Fixed: You can now use text formatting on Image block captions
* Fixed: Dynamic Content show different post data when used with JetEngine loops and other query builders
* Fixed: Better scrollbar styling support with Firefix
* Fixed: Column size indicator sometimes shows up as a square / no icon and without a label
* Fixed: Dynamic breakpoints not producing correct breakpoints when used with specific values
* Fixed: Conditional Display can produce a PHP error if post meta is an array
* Fixed: Inner block gap option sometimes resets to 0
* Fixed: Accordion block now opens correctly when editing in the theme Customizer
* Fixed: Carousel & Horizontal scroller height issues when images are lazy loaded
* Fixed: Corrected account and contact us links in the settings when network activated
* Fixed: Network activating will no longer redirect to a 404 page

= 3.12.6 =
* Fixed: Block error for blocks with containers and video backgrounds #2967

= 3.12.5 =
* Fixed: Edge case PHP error with unique id checking #2961
* Fixed: Resolved PHP warning with Metabox integration #317

= 3.12.4 =
* New: WordPress 6.4 compatibility
* New: Tabs block option: Equal tab height - to toggle on/off equal tab heights #2844
* New: Dynamic Content: Added "show as link" option for author name and taxonomies #2878
* New: Image block now has a built-in caption #2875
* New: You can now use a URL for images and background images #2873
* Change: Tabs block now by default will now have non-equal tab heights
* Change: Added note in fixed background option that it only works in desktop and Android mobile #2917
* Fixed: Carousel block is now RTL compatible #2910
* Fixed: Duplicated blocks that are inserted dynamically will now style correctly #2890
* Fixed: Margin bottom draggable control now works correctly when using percentage #2904
* Fixed: Added border radius corner icons #2914
* Fixed: Timeline block lines now connect properly in Firefox #2872
* Fixed: Timeline block in Safari mobile workaround: now always filled with accent color #2912
* Fixed: Progress bar and circle blocks now show correctly dynamic content if edited in widgets #2952
* Fixed: Dragging margin bottom control is now smoother #2904

= 3.12.3 =
* Fixed: When updating a container, it has a chance of incorrectly enabling the container
* Fixed: Resolved PHP warning about global styles variable
* Fixed: Accordion block produces a PHP error on saving when structure is malformed
* Fixed: Prevent new border radius control from overflowing the corners

= 3.12.2 =
* Fixed: (Hotfix update) PHP error if using PHP 7.2 and below

= 3.12.1 =
* Fixed: (Hotfix update) Possible PHP error with Dynamic Content

= 3.12.0 =
[WooCommerce Integration and Enhanced Color, Gradient and Opacity Settings](https://wpstackable.com/blog/woocommerce-integration/?utm_source=wp-repo&utm_campaign=readme&utm_medium=link)

* New: WooCommerce integration: you can now use WooCommerce product fields in Dynamic Content and inside the native Query Loop block (premium) #2907
* New: WooCommerce integration: you can now use WooCommerce product fields as Display Conditions (premium) #2907
* New: Enhanced color picker: now includes opacity #2740
* New: Enhanced gradient color picker: multiple color stops, type and angle. Also supports theme gradient colors. #2740
* New: Accordion FAQ Schema option #2874
* New: You can now adjust border radius corners individually #2877
* Change: Global colors can no longer be used when picking gradient colors (backward compatible, if you used them before, they will still work unless the gradient is edited)
* Fixed: Global settings get removed when switching between code view and visual view #2855
* Fixed: Posts block, added missing note about extra options #2879
* Fixed: Global typography preview now reflects the theme's default font family #2858
