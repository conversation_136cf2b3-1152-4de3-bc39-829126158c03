<?php
// This is a generated file by gulp generate-block-design-system-php
// Use block-design-system.json if you want to edit this file.

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! class_exists( 'Stackable_Block_Design_System') ) {
	class Stackable_Block_Design_System {

		function __construct() {
		}

		public static function get_block_design_system() {
			$block_design_system = array(
				"block-margin-bottom" => array(
					"desktop" => 24,
					"mobile" => 16
				),
				"column-margin" => array(
					"desktop" => 12,
					"mobile" => 8
				),
				"columns-column-gap" => array(
					"desktop" => 0
				),
				"columns-row-gap" => array(
					"desktop" => 0
				),
				"container-padding" => array(
					"desktop" => array(
						"top" => 32,
						"right" => 32,
						"bottom" => 32,
						"left" => 32
					),
					"mobile" => array(
						"top" => 24,
						"right" => 24,
						"bottom" => 24,
						"left" => 24
					)
				),
				"container-padding-large" => array(
					"desktop" => array(
						"top" => 64,
						"right" => 80,
						"bottom" => 64,
						"left" => 80
					),
					"mobile" => array(
						"top" => 32,
						"right" => 24,
						"bottom" => 32,
						"left" => 24
					)
				),
				"container-padding-small" => array(
					"desktop" => array(
						"top" => 16,
						"right" => 32,
						"bottom" => 16,
						"left" => 32
					),
					"mobile" => array(
						"top" => 8,
						"right" => 24,
						"bottom" => 8,
						"left" => 24
					)
				),
				"container-border-style" => array(
					"desktop" => ""
				),
				"container-border-width" => array(
					"desktop" => 1
				),
				"container-border-radius" => array(
					"desktop" => 0
				),
				"container-box-shadow" => array(
					"desktop" => "0px 4px 24px rgba(0, 0, 0, 0.04)"
				),
				"container-background-color" => array(
					"desktop" => "#fff"
				),
				"container-color" => array(
					"desktop" => "#1e1e1e"
				),
				"block-background-padding" => array(
					"desktop" => array(
						"top" => 24,
						"right" => 24,
						"bottom" => 24,
						"left" => 24
					),
					"mobile" => array(
						"top" => 16,
						"right" => 16,
						"bottom" => 16,
						"left" => 16
					)
				),
				"block-background-border-style" => array(
					"desktop" => ""
				),
				"block-background-border-width" => array(
					"desktop" => 1
				),
				"block-background-border-radius" => array(
					"desktop" => 0
				),
				"block-background-box-shadow" => array(
					"desktop" => ""
				),
				"block-background-color" => array(
					"desktop" => "#f1f1f1"
				),
				"image-border-radius" => array(
					"desktop" => 0
				),
				"image-drop-shadow" => array(
					"desktop" => ""
				),
				"button-min-height" => array(
					"desktop" => 0
				),
				"button-padding" => array(
					"desktop" => array(
						"top" => 12,
						"right" => 16,
						"bottom" => 12,
						"left" => 16
					)
				),
				"button-border-style" => array(
					"desktop" => ""
				),
				"button-border-width" => array(
					"desktop" => 1
				),
				"button-ghost-border-width" => array(
					"desktop" => 2
				),
				"button-border-radius" => array(
					"desktop" => 0
				),
				"button-box-shadow" => array(
					"desktop" => ""
				),
				"button-icon-size" => array(
					"desktop" => 24
				),
				"button-icon-gap" => array(
					"desktop" => 8
				),
				"button-column-gap" => array(
					"desktop" => 12
				),
				"button-row-gap" => array(
					"desktop" => 12
				),
				"button-background-color" => array(
					"desktop" => "#008de4"
				),
				"button-text-color" => array(
					"desktop" => "#fff"
				),
				"icon-button-padding" => array(
					"desktop" => array(
						"top" => 12,
						"right" => 12,
						"bottom" => 12,
						"left" => 12
					)
				),
				"icon-list-row-gap" => array(
					"desktop" => 0
				),
				"icon-list-icon-gap" => array(
					"desktop" => 8
				),
				"icon-list-indentation" => array(
					"desktop" => 0
				),
				"icon-size" => array(
					"desktop" => 36
				),
				"icon-color" => array(
					"desktop" => "#a6a6a6"
				),
				"icon-shape-color" => array(
					"desktop" => "#ddd"
				),
				"subtitle-size" => array(
					"desktop" => 16
				),
				"subtitle-color" => array(
					"desktop" => "#39414d"
				),
				"tab-accent-color" => array(
					"desktop" => "#008de4"
				)
			);

			return $block_design_system;
		}
	}

	new Stackable_Block_Design_System();
}
?>
