{"apiVersion": 3, "name": "stackable/icon-button", "title": "Icon <PERSON>", "description": "Add a customizable button.", "category": "stackable", "usesContext": ["postId", "postType", "queryId", "stackable/innerBlockOrientation"], "parent": ["stackable/button-group"], "keywords": ["Link"], "textdomain": "stackable-ultimate-gutenberg-blocks", "stk-type": "hidden", "stk-demo": "https://wpstackable.com/icon-button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink", "stk-block-dependency": "stackable/button-group|icon-button"}