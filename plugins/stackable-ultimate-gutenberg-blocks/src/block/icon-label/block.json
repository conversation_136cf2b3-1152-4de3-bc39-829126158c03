{"apiVersion": 3, "name": "stackable/icon-label", "title": "Icon Label", "description": "An Icon and <PERSON><PERSON> paired together.", "category": "stackable", "usesContext": ["postId", "postType", "queryId", "stackable/innerBlockOrientation"], "keywords": ["SVG"], "textdomain": "stackable-ultimate-gutenberg-blocks", "stk-type": "special", "stk-demo": "https://wpstackable.com/icon-label-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink", "stk-required-blocks": ["stackable/icon", "stackable/heading"]}