{"apiVersion": 3, "name": "stackable/button-group", "title": "Button Group", "description": "Add a customizable button.", "category": "stackable", "usesContext": ["postId", "postType", "queryId", "stackable/innerBlockOrientation"], "keywords": ["Link"], "stk-variants": [{"name": "icon-button", "title": "Icon <PERSON>", "description": "Add a customizable button.", "category": "stackable", "stk-type": "essential", "stk-demo": "https://wpstackable.com/icon-button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}, {"name": "button", "title": "<PERSON><PERSON>", "description": "Add a customizable button.", "category": "stackable", "stk-type": "essential", "stk-demo": "https://wpstackable.com/button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}, {"name": "social-buttons", "title": "Social Buttons", "description": "Add social buttons.", "category": "stackable", "stk-type": "special", "stk-demo": "https://wpstackable.com/social-buttons-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink", "stk-required-blocks": ["stackable/button-group|icon-button"]}], "textdomain": "stackable-ultimate-gutenberg-blocks", "stk-type": "hidden"}