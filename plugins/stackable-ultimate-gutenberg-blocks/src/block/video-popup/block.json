{"apiVersion": 3, "name": "stackable/video-popup", "title": "Video Popup", "description": "Display a large thumbnail that your users can click to play a video full-screen. Great for introductory or tutorial videos.", "category": "stackable", "usesContext": ["postId", "postType", "queryId", "stackable/innerBlockOrientation"], "keywords": ["YouTube", "Vimeo", "Embed Mp4"], "textdomain": "stackable-ultimate-gutenberg-blocks", "stk-type": "special", "stk-demo": "https://wpstackable.com/video-popup-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink", "stk-required-blocks": ["stackable/icon", "stackable/image"]}