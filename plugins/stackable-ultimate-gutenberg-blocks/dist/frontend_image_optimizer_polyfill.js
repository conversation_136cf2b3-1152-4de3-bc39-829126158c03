var frontend_image_optimizer_polyfill;(()=>{"use strict";var t,e={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(e),window.ImageOptimizerPolyfill=new class{init=()=>{const t=document.querySelectorAll(".stk-block img");let e=0;const i=new MutationObserver((r=>{r.forEach((r=>{const n=r.target;if(n.hasAttribute("srcset")){let t=n.getAttribute("srcset");const e=/https?:\/\/[^\s,]+/g;t.match(e).forEach((e=>{const i=e.indexOf("&fit");if(-1!==i){const r=e.slice(0,i);t=t.replace(e,r)}})),n.setAttribute("srcset",t)}if(-1!==n.getAttribute("data-src").indexOf("&fit")){const t=n.getAttribute("data-src"),e=t.indexOf("&fit"),i=t.slice(0,e);n.setAttribute("data-src",i)}if(-1!==n.getAttribute("src").indexOf("&fit")){const t=n.getAttribute("src"),e=t.indexOf("&fit"),i=t.slice(0,e);n.setAttribute("src",i)}e++,e===t.length&&i.disconnect()}))}));t.forEach((t=>{i.observe(t,{attributeFilter:["src","srcset"]})}))}},t=window.ImageOptimizerPolyfill.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",t):t()),frontend_image_optimizer_polyfill=e})();