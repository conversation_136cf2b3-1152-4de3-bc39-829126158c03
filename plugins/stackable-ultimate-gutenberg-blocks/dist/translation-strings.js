/**
 * This translation file is automatically generated by gulp generate-translations-js.
 */

// This doesn't run anything, this is just for loading the strings since WP
// won't generate them in the json/js language packs if they're not present in
// the script.
if ( false ) {
__( 'Login Status', 'stackable-ultimate-gutenberg-blocks' )
__( 'Role', 'stackable-ultimate-gutenberg-blocks' )
__( 'Date & Time', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom PHP', 'stackable-ultimate-gutenberg-blocks' )
__( 'Conditional Tag', 'stackable-ultimate-gutenberg-blocks' )
__( 'Query String', 'stackable-ultimate-gutenberg-blocks' )
__( 'Post Meta', 'stackable-ultimate-gutenberg-blocks' )
__( 'Site Option', 'stackable-ultimate-gutenberg-blocks' )
__( 'Post IDs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Post Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Post Taxonomy', 'stackable-ultimate-gutenberg-blocks' )
__( 'WooCommerce', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Conditional Tag', 'stackable-ultimate-gutenberg-blocks' )
__( 'Home', 'stackable-ultimate-gutenberg-blocks' )
__( 'Front Page', 'stackable-ultimate-gutenberg-blocks' )
__( '404 Not Found Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Single Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Attachment', 'stackable-ultimate-gutenberg-blocks' )
__( 'Preview', 'stackable-ultimate-gutenberg-blocks' )
__( 'Any Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Privacy Policy Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Any Archive Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Category Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tag Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Taxonomy Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Author Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Date Archive Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Yearly Archive Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Search Result Page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Trackback', 'stackable-ultimate-gutenberg-blocks' )
__( 'Dynamic Sidebar', 'stackable-ultimate-gutenberg-blocks' )
__( 'RTL Reading', 'stackable-ultimate-gutenberg-blocks' )
__( 'Multisite', 'stackable-ultimate-gutenberg-blocks' )
__( 'Main Site', 'stackable-ultimate-gutenberg-blocks' )
__( 'Child Theme', 'stackable-ultimate-gutenberg-blocks' )
__( 'Customize Preview', 'stackable-ultimate-gutenberg-blocks' )
__( 'Multi-author Site', 'stackable-ultimate-gutenberg-blocks' )
__( 'Feed', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sticky Post', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hierarchical Post Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Archive Post Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Comments Open', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pings Open', 'stackable-ultimate-gutenberg-blocks' )
__( 'Has Excerpt', 'stackable-ultimate-gutenberg-blocks' )
__( 'Has Post Thumbnail', 'stackable-ultimate-gutenberg-blocks' )
__( 'Has Tags', 'stackable-ultimate-gutenberg-blocks' )
__( 'Has Terms', 'stackable-ultimate-gutenberg-blocks' )
__( 'Has Primary Nav Menu', 'stackable-ultimate-gutenberg-blocks' )
__( 'The Custom PHP allows you to configure the block’s visibility based on the expression entered. If the expression evaluates to true, the block will be displayed.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sample PHP code:', 'stackable-ultimate-gutenberg-blocks' )
__( 'If a syntax error is present, check your PHP code', 'stackable-ultimate-gutenberg-blocks' )
__( 'Reset', 'stackable-ultimate-gutenberg-blocks' )
__( 'Start Date', 'stackable-ultimate-gutenberg-blocks' )
__( 'Now', 'stackable-ultimate-gutenberg-blocks' )
__( 'End Date', 'stackable-ultimate-gutenberg-blocks' )
__( 'Never', 'stackable-ultimate-gutenberg-blocks' )
__( 'Days of the Week', 'stackable-ultimate-gutenberg-blocks' )
__( 'If set, the block will be displayed / hidden on selected days.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sunday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Monday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tuesday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Wednesday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Thursday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Friday', 'stackable-ultimate-gutenberg-blocks' )
__( 'Saturday', 'stackable-ultimate-gutenberg-blocks' )
__( 'None', 'stackable-ultimate-gutenberg-blocks' )
__( 'Logged-In Users', 'stackable-ultimate-gutenberg-blocks' )
__( 'Logged-Out Users', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Post IDs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Post Meta Key', 'stackable-ultimate-gutenberg-blocks' )
__( 'Operator', 'stackable-ultimate-gutenberg-blocks' )
__( 'True', 'stackable-ultimate-gutenberg-blocks' )
__( 'False', 'stackable-ultimate-gutenberg-blocks' )
__( 'Equal', 'stackable-ultimate-gutenberg-blocks' )
__( 'Not Equal', 'stackable-ultimate-gutenberg-blocks' )
__( 'Less Than', 'stackable-ultimate-gutenberg-blocks' )
__( 'Less Than & Equal To', 'stackable-ultimate-gutenberg-blocks' )
__( 'Greater Than', 'stackable-ultimate-gutenberg-blocks' )
__( 'Greater Than & Equal To', 'stackable-ultimate-gutenberg-blocks' )
__( 'Contains', 'stackable-ultimate-gutenberg-blocks' )
__( 'Does Not Contain', 'stackable-ultimate-gutenberg-blocks' )
__( 'Regular Expression', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Value', 'stackable-ultimate-gutenberg-blocks' )
__( 'Value to compare with the post meta value.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Post Types', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Queries', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter one query string per line. The block will be displayed / hidden if any of the query strings match.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enter Role', 'stackable-ultimate-gutenberg-blocks' )
__( 'Option Name', 'stackable-ultimate-gutenberg-blocks' )
__( 'Value to compare with the option value.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Current Post', 'stackable-ultimate-gutenberg-blocks' )
__( 'Choose Product', 'stackable-ultimate-gutenberg-blocks' )
__( 'Property', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sales', 'stackable-ultimate-gutenberg-blocks' )
__( 'Stock Quantity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Downloadable', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Featured', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is in Stock', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is on Backorder', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is on Sale', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Purchasable', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Shipping Taxable', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Sold Individually', 'stackable-ultimate-gutenberg-blocks' )
__( 'Is Taxable', 'stackable-ultimate-gutenberg-blocks' )
__( 'Value', 'stackable-ultimate-gutenberg-blocks' )
__( '%s Placeholder', 'stackable-ultimate-gutenberg-blocks' )
__( 'Placeholder', 'stackable-ultimate-gutenberg-blocks' )
__( 'Full Access', 'stackable-ultimate-gutenberg-blocks' )
__( 'Manager', 'stackable-ultimate-gutenberg-blocks' )
__( 'No Access', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom Fields', 'stackable-ultimate-gutenberg-blocks' )
__( 'Enabled', 'stackable-ultimate-gutenberg-blocks' )
__( 'Disabled', 'stackable-ultimate-gutenberg-blocks' )
__( 'Full editing mode', 'stackable-ultimate-gutenberg-blocks' )
__( 'Content only editing', 'stackable-ultimate-gutenberg-blocks' )
__( 'Let me enter my Font Awesome Pro Kit code', 'stackable-ultimate-gutenberg-blocks' )
__( 'Don\'t show me this again', 'stackable-ultimate-gutenberg-blocks' )
__( 'FontAwesome Pro Kit', 'stackable-ultimate-gutenberg-blocks' )
__( 'Paste your Kit code %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Verify', 'stackable-ultimate-gutenberg-blocks' )
__( 'Please make sure you have Pro icons selected in your kit. Edit your kit settings at: ', 'stackable-ultimate-gutenberg-blocks' )
__( 'Click here to check again', 'stackable-ultimate-gutenberg-blocks' )
__( 'Please enter a valid Font Awesome Pro Kit code.', 'stackable-ultimate-gutenberg-blocks' )
__( 'If you have Font Awesome Pro, you can use your Pro icons by inputting your Pro Kit code here.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Need help? Read our guide.', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Title for This Block', 'Heading placeholder', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Description for this block. Use this space for describing your block. Any text will do. Description for this block. You can use this space for describing your block.', 'Content placeholder', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Subtitle for This Block', 'Subtitle placeholder', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Button', 'Button placeholder', 'stackable-ultimate-gutenberg-blocks' )
__( 'Title', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Text for This Block', 'Text placeholder', 'stackable-ultimate-gutenberg-blocks' )
_x( 'Description for this block. Use this space for describing your block. Any text will do.', 'Content placeholder', 'stackable-ultimate-gutenberg-blocks' )
__( 'Load More', 'stackable-ultimate-gutenberg-blocks' )
__( 'General', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number of Items', 'stackable-ultimate-gutenberg-blocks' )
__( 'URL Query String', 'stackable-ultimate-gutenberg-blocks' )
__( 'This is the string appended to the URL when changing pages.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show Next & Previous Button', 'stackable-ultimate-gutenberg-blocks' )
__( 'Next Label', 'stackable-ultimate-gutenberg-blocks' )
__( 'Next', 'stackable-ultimate-gutenberg-blocks' )
__( 'Previous Label', 'stackable-ultimate-gutenberg-blocks' )
__( 'Previous', 'stackable-ultimate-gutenberg-blocks' )
__( 'Previous page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Page %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Next page', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sale', 'stackable-ultimate-gutenberg-blocks' )
__( 'one', 'stackable-ultimate-gutenberg-blocks' )
__( 'two', 'stackable-ultimate-gutenberg-blocks' )
__( 'three', 'stackable-ultimate-gutenberg-blocks' )
__( 'Package inclusion %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer Height', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer Width', 'stackable-ultimate-gutenberg-blocks' )
__( 'Flip Horizontally', 'stackable-ultimate-gutenberg-blocks' )
__( 'Opacity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer Opacity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Adjusts the transparency of the separator layer', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer Blend mode', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sets how the sepator layer is blended into the background', 'stackable-ultimate-gutenberg-blocks' )
__( 'Name', 'stackable-ultimate-gutenberg-blocks' )
__( 'Position', 'stackable-ultimate-gutenberg-blocks' )
__( 'Delete Condition', 'stackable-ultimate-gutenberg-blocks' )
__( 'Deleting will remove this condition for the block. Proceed?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Delete', 'stackable-ultimate-gutenberg-blocks' )
__( 'Cancel', 'stackable-ultimate-gutenberg-blocks' )
__( 'Condition Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Visibility', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show on condition match', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hide on condition match', 'stackable-ultimate-gutenberg-blocks' )
__( 'No conditions yet. Add your first condition.', 'stackable-ultimate-gutenberg-blocks' )
// translators: This is the separator between conditions: OR / AND.
__( 'OR', 'stackable-ultimate-gutenberg-blocks' )
// translators: This is the separator between conditions: OR / AND.
__( 'AND', 'stackable-ultimate-gutenberg-blocks' )
__( 'Trigger if ANY condition matches', 'stackable-ultimate-gutenberg-blocks' )
__( 'Trigger if ALL conditions match', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add New', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add New Condition', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn more about Conditional Display', 'stackable-ultimate-gutenberg-blocks' )
__( 'Offset', 'stackable-ultimate-gutenberg-blocks' )
__( 'Offset your posts by a specific number of items.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Exclude Post IDs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Excludes specific IDs from the display. Enter post IDs separated by a commas', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hide the current post', 'stackable-ultimate-gutenberg-blocks' )
__( 'Removes the current post from the posts list', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display Specific Post IDs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Limit display to only these specific IDs. Enter post IDs separated by a commas', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom CSS', 'stackable-ultimate-gutenberg-blocks' )
__( 'You can use this area to further customize your block. Any custom CSS added here will only affect this block.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Media queries are supported. Use the widths 1024px and 768px for tablet and mobile breakpoints.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Background Shape', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shape', 'stackable-ultimate-gutenberg-blocks' )
__( 'Change the shape of the image', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shape Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shape Opacity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shape Size', 'stackable-ultimate-gutenberg-blocks' )
__( 'Horizontal Offset', 'stackable-ultimate-gutenberg-blocks' )
__( 'Vertical Offset', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Color #%s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Gradient Direction (degrees)', 'stackable-ultimate-gutenberg-blocks' )
__( 'Gradient', 'stackable-ultimate-gutenberg-blocks' )
__( 'Multicolor', 'stackable-ultimate-gutenberg-blocks' )
__( 'Multicolor only works for custom uploaded icons that have multiple path elements or for Font Awesome Pro Duotones.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn more', 'stackable-ultimate-gutenberg-blocks' )
_x( '%s #%d', 'Panel title', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Opacity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Separator Layer %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Transition Duration (secs)', 'stackable-ultimate-gutenberg-blocks' )
__( 'Transition Function', 'stackable-ultimate-gutenberg-blocks' )
__( 'Default', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease Out', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Out', 'stackable-ultimate-gutenberg-blocks' )
__( 'Linear', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Quad', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease Out Quad', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Out Quad', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Expo', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease Out Expo', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Out Expo', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Back', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease Out Back', 'stackable-ultimate-gutenberg-blocks' )
__( 'Ease In Out Back', 'stackable-ultimate-gutenberg-blocks' )
__( 'Transform', 'stackable-ultimate-gutenberg-blocks' )
__( 'Transform Origin', 'stackable-ultimate-gutenberg-blocks' )
__( 'Top Left', 'stackable-ultimate-gutenberg-blocks' )
__( 'Top Center', 'stackable-ultimate-gutenberg-blocks' )
__( 'Top Right', 'stackable-ultimate-gutenberg-blocks' )
__( 'Center Left', 'stackable-ultimate-gutenberg-blocks' )
__( 'Center Center', 'stackable-ultimate-gutenberg-blocks' )
__( 'Center Right', 'stackable-ultimate-gutenberg-blocks' )
__( 'Bottom Left', 'stackable-ultimate-gutenberg-blocks' )
__( 'Bottom Center', 'stackable-ultimate-gutenberg-blocks' )
__( 'Bottom Right', 'stackable-ultimate-gutenberg-blocks' )
__( 'This block has Motion Effects assigned to it, applying transforms above may prevent the Motion Effects from working as expected.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Translate X', 'stackable-ultimate-gutenberg-blocks' )
__( 'Translate Y', 'stackable-ultimate-gutenberg-blocks' )
__( 'Rotate', 'stackable-ultimate-gutenberg-blocks' )
__( 'Scale', 'stackable-ultimate-gutenberg-blocks' )
__( 'No saved designs yet', 'stackable-ultimate-gutenberg-blocks' )
__( 'Click here to save your block\'s design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Error Getting Designs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Click here to retry fetching your saved designs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save as new block design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Manage saved designs', 'stackable-ultimate-gutenberg-blocks' )
__( '(default)', 'stackable-ultimate-gutenberg-blocks' )
__( 'Favorite', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save Changes', 'stackable-ultimate-gutenberg-blocks' )
__( 'Saved Block Designs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save designs to reuse them across your site. Note that using saved designs will override your current block settings.', 'stackable-ultimate-gutenberg-blocks' )
__( 'You have unsaved changes, discard them?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Manage Saved Designs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Design Name', 'stackable-ultimate-gutenberg-blocks' )
__( 'Set as a favorite design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Design name', 'stackable-ultimate-gutenberg-blocks' )
__( 'Set as default block design', 'stackable-ultimate-gutenberg-blocks' )
__( 'My Block Design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save as New Block Design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Set as favorite', 'stackable-ultimate-gutenberg-blocks' )
__( 'Place at the top of the list of saved designs', 'stackable-ultimate-gutenberg-blocks' )
__( 'New blocks created will use this design automatically', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add New Design', 'stackable-ultimate-gutenberg-blocks' )
__( 'Horizontal Position', 'stackable-ultimate-gutenberg-blocks' )
__( 'Vertical Position', 'stackable-ultimate-gutenberg-blocks' )
__( 'Effect', 'stackable-ultimate-gutenberg-blocks' )
__( 'Entrance Animation', 'stackable-ultimate-gutenberg-blocks' )
__( 'Scroll Animation', 'stackable-ultimate-gutenberg-blocks' )
__( 'Start Position', 'stackable-ultimate-gutenberg-blocks' )
__( 'Entrance Animation Speed', 'stackable-ultimate-gutenberg-blocks' )
__( 'Slow', 'stackable-ultimate-gutenberg-blocks' )
__( 'Normal', 'stackable-ultimate-gutenberg-blocks' )
__( 'Fast', 'stackable-ultimate-gutenberg-blocks' )
__( 'Entrance Animation Delay', 'stackable-ultimate-gutenberg-blocks' )
__( 'Smoothen Scroll Animation', 'stackable-ultimate-gutenberg-blocks' )
__( 'Use 3D Transforms', 'stackable-ultimate-gutenberg-blocks' )
__( 'Perspective', 'stackable-ultimate-gutenberg-blocks' )
__( 'Exit Animation', 'stackable-ultimate-gutenberg-blocks' )
__( 'This block has Transforms assigned to it, Motion Effects may not work as expected.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn more about Motion Effects', 'stackable-ultimate-gutenberg-blocks' )
__( 'TranslateY', 'stackable-ultimate-gutenberg-blocks' )
__( 'TranslateZ', 'stackable-ultimate-gutenberg-blocks' )
__( 'RotateX', 'stackable-ultimate-gutenberg-blocks' )
__( 'RotateY', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blur', 'stackable-ultimate-gutenberg-blocks' )
__( 'Skew X', 'stackable-ultimate-gutenberg-blocks' )
__( 'Skew Y', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show as link', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom Text', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom', 'stackable-ultimate-gutenberg-blocks' )
__( 'Date Format', 'stackable-ultimate-gutenberg-blocks' )
__( 'Custom Format', 'stackable-ultimate-gutenberg-blocks' )
__( 'Change the date format of your dynamic content.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn more about date formats', 'stackable-ultimate-gutenberg-blocks' )
__( 'Dynamic Fields', 'stackable-ultimate-gutenberg-blocks' )
__( 'Dynamic Source', 'stackable-ultimate-gutenberg-blocks' )
__( 'Field', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn how to use Dynamic Content', 'stackable-ultimate-gutenberg-blocks' )
__( 'Apply', 'stackable-ultimate-gutenberg-blocks' )
__( 'Remove', 'stackable-ultimate-gutenberg-blocks' )
__( 'Excerpt Length', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add Custom Format', 'stackable-ultimate-gutenberg-blocks' )
__( 'Content Format', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add your custom format by adding %s.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Thumbnail', 'stackable-ultimate-gutenberg-blocks' )
__( 'Medium', 'stackable-ultimate-gutenberg-blocks' )
__( 'Large', 'stackable-ultimate-gutenberg-blocks' )
__( 'Full', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image Quality', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image Size', 'stackable-ultimate-gutenberg-blocks' )
__( 'Text Field', 'stackable-ultimate-gutenberg-blocks' )
__( 'Open in new tab', 'stackable-ultimate-gutenberg-blocks' )
__( 'Taxonomy Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display Option', 'stackable-ultimate-gutenberg-blocks' )
__( 'All values', 'stackable-ultimate-gutenberg-blocks' )
__( '%s value', 'stackable-ultimate-gutenberg-blocks' )
__( '1st', 'stackable-ultimate-gutenberg-blocks' )
__( '2nd', 'stackable-ultimate-gutenberg-blocks' )
__( '3rd', 'stackable-ultimate-gutenberg-blocks' )
__( '4th', 'stackable-ultimate-gutenberg-blocks' )
__( '5th', 'stackable-ultimate-gutenberg-blocks' )
__( '6th', 'stackable-ultimate-gutenberg-blocks' )
__( '7th', 'stackable-ultimate-gutenberg-blocks' )
__( '8th', 'stackable-ultimate-gutenberg-blocks' )
__( '9th', 'stackable-ultimate-gutenberg-blocks' )
__( '10th', 'stackable-ultimate-gutenberg-blocks' )
__( 'Delimiter', 'stackable-ultimate-gutenberg-blocks' )
__( 'Strip HTML tags', 'stackable-ultimate-gutenberg-blocks' )
__( 'If this option is false, the content rendered in the editor will still be stripped to prevent an error from occuring', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display text when true', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display text when false', 'stackable-ultimate-gutenberg-blocks' )
__( 'Fields', 'stackable-ultimate-gutenberg-blocks' )
__( 'Attributes', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Color Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Learn more how multicolor works', 'stackable-ultimate-gutenberg-blocks' )
__( 'here', 'stackable-ultimate-gutenberg-blocks' )
__( 'Single', 'stackable-ultimate-gutenberg-blocks' )
__( 'Outline Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Shape Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blob %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'Default Block Colors', 'stackable-ultimate-gutenberg-blocks' )
__( 'Base Color Scheme', 'stackable-ultimate-gutenberg-blocks' )
__( 'Default color scheme to use for all blocks when no special options are enabled.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Background Mode Color Scheme', 'stackable-ultimate-gutenberg-blocks' )
__( 'Colors applied when the background option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Container Mode Color Scheme', 'stackable-ultimate-gutenberg-blocks' )
__( 'Colors applied when the container option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Color Scheme %s', 'stackable-ultimate-gutenberg-blocks' )
__( 'You have duplicated the color scheme. You are now editing the new one.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Deleting this color scheme would remove all colors linked to it. Any blocks that use this color scheme will revert to the default scheme. Delete this color scheme?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Style copied successfully!', 'stackable-ultimate-gutenberg-blocks' )
__( 'Style pasted successfully!', 'stackable-ultimate-gutenberg-blocks' )
__( 'Used to copy core/stackable block styles', 'stackable-ultimate-gutenberg-blocks' )
__( 'Used to paste core/stackable block styles', 'stackable-ultimate-gutenberg-blocks' )
__( 'Adv Copy Styles', 'stackable-ultimate-gutenberg-blocks' )
__( 'Adv Paste Styles', 'stackable-ultimate-gutenberg-blocks' )
__( 'Copy & paste styles', 'stackable-ultimate-gutenberg-blocks' )
__( 'Remove the selected block(s).', 'stackable-ultimate-gutenberg-blocks' )
__( 'Content Editing mode is enabled', 'stackable-ultimate-gutenberg-blocks' )
__( 'To unlock Full Editing mode, please contact your administrator.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Delete this icon?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Drop your SVG here', 'stackable-ultimate-gutenberg-blocks' )
__( 'Upload SVG', 'stackable-ultimate-gutenberg-blocks' )
__( 'Upload SVG icons to your library to use them in your blocks.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Library', 'stackable-ultimate-gutenberg-blocks' )
__( 'Do you also want to add this icon to your icon library for future use?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Field cannot be empty', 'stackable-ultimate-gutenberg-blocks' )
__( 'Slug contains invalid characters', 'stackable-ultimate-gutenberg-blocks' )
__( 'Slug must be unique', 'stackable-ultimate-gutenberg-blocks' )
__( 'https://', 'stackable-ultimate-gutenberg-blocks' )
__( 'Link title', 'stackable-ultimate-gutenberg-blocks' )
__( 'Do you want to delete this field?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add New Field', 'stackable-ultimate-gutenberg-blocks' )
__( 'Field Type', 'stackable-ultimate-gutenberg-blocks' )
__( 'Text', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number', 'stackable-ultimate-gutenberg-blocks' )
__( 'Date', 'stackable-ultimate-gutenberg-blocks' )
__( 'Time', 'stackable-ultimate-gutenberg-blocks' )
__( 'Url', 'stackable-ultimate-gutenberg-blocks' )
__( 'Field Name', 'stackable-ultimate-gutenberg-blocks' )
__( 'Field Slug', 'stackable-ultimate-gutenberg-blocks' )
__( 'A unique string that will be used to identify this field. Must contain only letters, numbers, underscores and dashes.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Description', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save field', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add a custom field now to start exploring the possibilities.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add field', 'stackable-ultimate-gutenberg-blocks' )
__( 'You don\'t have any custom fields.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Save changes', 'stackable-ultimate-gutenberg-blocks' )
__( 'You have unsaved changes', 'stackable-ultimate-gutenberg-blocks' )
__( 'Error in saving content', 'stackable-ultimate-gutenberg-blocks' )
__( 'Are you sure you want to delete this font pair preset?', 'stackable-ultimate-gutenberg-blocks' )
__( 'Editing Font Pair', 'stackable-ultimate-gutenberg-blocks' )
__( 'Dynamic Content', 'stackable-ultimate-gutenberg-blocks' )
__( 'Offset your posts by a specific number of items', 'stackable-ultimate-gutenberg-blocks' )
__( 'Next »', 'stackable-ultimate-gutenberg-blocks' )
__( '« Previous', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pagination', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show previous and next buttons', 'stackable-ultimate-gutenberg-blocks' )
__( 'Previous label', 'stackable-ultimate-gutenberg-blocks' )
__( 'Next label', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hover & Active Opacity', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hover & Active Colors', 'stackable-ultimate-gutenberg-blocks' )
__( 'Align', 'stackable-ultimate-gutenberg-blocks' )
__( 'Load More Button', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number of items', 'stackable-ultimate-gutenberg-blocks' )
__( 'Reverse columns', 'stackable-ultimate-gutenberg-blocks' )
__( 'Collapsed Row Gap', 'stackable-ultimate-gutenberg-blocks' )
__( 'Collapsed Col. Arrangement', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column Gap', 'stackable-ultimate-gutenberg-blocks' )
__( 'Row Gap', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column', 'stackable-ultimate-gutenberg-blocks' )
__( 'Override settings for column %d', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column Background', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image Height', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image Width', 'stackable-ultimate-gutenberg-blocks' )
__( 'Collapse image on Mobile', 'stackable-ultimate-gutenberg-blocks' )
__( 'Collapse image height', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tilt', 'stackable-ultimate-gutenberg-blocks' )
__( 'Zoom & Tilt', 'stackable-ultimate-gutenberg-blocks' )
__( 'Up', 'stackable-ultimate-gutenberg-blocks' )
__( 'Down', 'stackable-ultimate-gutenberg-blocks' )
__( 'Left', 'stackable-ultimate-gutenberg-blocks' )
__( 'Right', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blur In', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blur Out', 'stackable-ultimate-gutenberg-blocks' )
__( 'Grayscale In', 'stackable-ultimate-gutenberg-blocks' )
__( 'Grayscale Out', 'stackable-ultimate-gutenberg-blocks' )
__( 'Box Hover Effect', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number Background', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column Header', 'stackable-ultimate-gutenberg-blocks' )
_x( '%s #%d', 'Nth Title', 'stackable-ultimate-gutenberg-blocks' )
_x( '%s %d', 'Nth Title', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer', 'stackable-ultimate-gutenberg-blocks' )
__( 'Layer Color', 'stackable-ultimate-gutenberg-blocks' )
__( 'Color on Hover', 'stackable-ultimate-gutenberg-blocks' )
__( 'Border Radius', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shadow / Outline', 'stackable-ultimate-gutenberg-blocks' )
__( 'Container', 'stackable-ultimate-gutenberg-blocks' )
__( 'Bubble Background', 'stackable-ultimate-gutenberg-blocks' )
__( 'Shadow', 'stackable-ultimate-gutenberg-blocks' )
__( 'Lift', 'stackable-ultimate-gutenberg-blocks' )
__( '%s More', 'stackable-ultimate-gutenberg-blocks' )
__( 'Lift w/ shadow', 'stackable-ultimate-gutenberg-blocks' )
__( 'Staggered lift', 'stackable-ultimate-gutenberg-blocks' )
__( 'Staggered lift w/ shadow', 'stackable-ultimate-gutenberg-blocks' )
__( 'Scale w/ shadow', 'stackable-ultimate-gutenberg-blocks' )
__( 'Lower', 'stackable-ultimate-gutenberg-blocks' )
__( 'Grayscale Hover Effect', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hover Effect', 'stackable-ultimate-gutenberg-blocks' )
__( 'Effects', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column / Container Spacing', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column Vertical Align', 'stackable-ultimate-gutenberg-blocks' )
__( 'Min. Column Height', 'stackable-ultimate-gutenberg-blocks' )
__( 'Content Vertical Align', 'stackable-ultimate-gutenberg-blocks' )
__( 'Column Paddings', 'stackable-ultimate-gutenberg-blocks' )
__( 'Load more button for your Stackable Posts block', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pagination for your Stackable Posts block', 'stackable-ultimate-gutenberg-blocks' )
__( 'New Block', 'stackable-ultimate-gutenberg-blocks' )
__( 'A new block.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Keywords that are not in the title', 'stackable-ultimate-gutenberg-blocks' )
__( 'Accordion', 'stackable-ultimate-gutenberg-blocks' )
__( 'A title that your visitors can toggle to view more text. Use as FAQs or multiple ones for an Accordion.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Toggle', 'stackable-ultimate-gutenberg-blocks' )
__( 'Faq', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blockquote', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display a quote in style', 'stackable-ultimate-gutenberg-blocks' )
__( 'Button Group', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add a customizable button.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Link', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Button', 'stackable-ultimate-gutenberg-blocks' )
__( 'Button', 'stackable-ultimate-gutenberg-blocks' )
__( 'Social Buttons', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add social buttons.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Call to Action', 'stackable-ultimate-gutenberg-blocks' )
__( 'A small section you can use to call the attention of your visitors. Great for calling attention to your products or deals.', 'stackable-ultimate-gutenberg-blocks' )
__( 'CTA', 'stackable-ultimate-gutenberg-blocks' )
__( 'Card', 'stackable-ultimate-gutenberg-blocks' )
__( 'Describe a single subject in a small card. You can use this to describe your product, service or a person.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Carousel', 'stackable-ultimate-gutenberg-blocks' )
__( 'A carousel slider.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Slider', 'stackable-ultimate-gutenberg-blocks' )
__( 'Inner Column', 'stackable-ultimate-gutenberg-blocks' )
__( 'A single column with advanced layout options.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Section rows', 'stackable-ultimate-gutenberg-blocks' )
__( 'Columns', 'stackable-ultimate-gutenberg-blocks' )
__( 'Multiple columns with advanced layout options.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Count Up', 'stackable-ultimate-gutenberg-blocks' )
__( 'Showcase your stats. Display how many customers you have or the number of downloads of your app.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Countdown', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display a countdown timer on your website.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Timer', 'stackable-ultimate-gutenberg-blocks' )
__( 'Design Library', 'stackable-ultimate-gutenberg-blocks' )
__( 'Choose a layout or block from the Stackable Design Library.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Template', 'stackable-ultimate-gutenberg-blocks' )
__( 'Divider', 'stackable-ultimate-gutenberg-blocks' )
__( 'Add a pause between your content.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Horizontal Rule', 'stackable-ultimate-gutenberg-blocks' )
__( 'HR', 'stackable-ultimate-gutenberg-blocks' )
__( 'Expand / Show More', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display a small snippet of text. Your readers can toggle it to show more information.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hide', 'stackable-ultimate-gutenberg-blocks' )
__( 'Less', 'stackable-ultimate-gutenberg-blocks' )
__( 'Feature Grid', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display multiple product features or services. You can use Feature Grids one after another.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Feature', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display a product feature or a service in a large area.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Heading', 'stackable-ultimate-gutenberg-blocks' )
__( 'Introduce new sections of your content in style.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Hero', 'stackable-ultimate-gutenberg-blocks' )
__( 'A large hero area. Typically used at the very top of a page.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Header', 'stackable-ultimate-gutenberg-blocks' )
__( 'Horizontal Scroller', 'stackable-ultimate-gutenberg-blocks' )
__( 'A slider that scrolls horizontally.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Box', 'stackable-ultimate-gutenberg-blocks' )
__( 'A small text area with an icon that can be used to summarize features or services', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon Label', 'stackable-ultimate-gutenberg-blocks' )
__( 'An Icon and Heading paired together.', 'stackable-ultimate-gutenberg-blocks' )
__( 'SVG', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon List Item', 'stackable-ultimate-gutenberg-blocks' )
__( 'A single list entry in the Icon List block', 'stackable-ultimate-gutenberg-blocks' )
__( 'Icon List', 'stackable-ultimate-gutenberg-blocks' )
__( 'An unordered list with icons. You can use this as a list of features or benefits.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Checklist', 'stackable-ultimate-gutenberg-blocks' )
__( 'Bullets', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number list', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pick an icon or upload your own SVG icon to decorate your content.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Image Box', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display an image that shows more information when hovered on. Can be used as a fancy link to other pages.', 'stackable-ultimate-gutenberg-blocks' )
__( 'An image with advanced controls to make a visual statement.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Map', 'stackable-ultimate-gutenberg-blocks' )
__( 'Embedded Google Map with advanced controls.', 'stackable-ultimate-gutenberg-blocks' )
__( 'location', 'stackable-ultimate-gutenberg-blocks' )
__( 'address', 'stackable-ultimate-gutenberg-blocks' )
__( 'Notification', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show a notice to your readers. People can dismiss the notice to permanently hide it.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Notice', 'stackable-ultimate-gutenberg-blocks' )
__( 'Alert', 'stackable-ultimate-gutenberg-blocks' )
__( 'Number Box', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display steps or methods that your users will do in your service.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Steps', 'stackable-ultimate-gutenberg-blocks' )
__( 'Posts', 'stackable-ultimate-gutenberg-blocks' )
__( 'Your latest blog posts. Use this to showcase a few of your posts in your landing pages.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Blog Posts', 'stackable-ultimate-gutenberg-blocks' )
__( 'Lastest Posts', 'stackable-ultimate-gutenberg-blocks' )
__( 'Query Loop', 'stackable-ultimate-gutenberg-blocks' )
__( 'Price', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show a price of a product or service with currency and a suffix styled with different weights', 'stackable-ultimate-gutenberg-blocks' )
__( 'Currency', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pricing', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pricing Box', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display the different pricing tiers of your business.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Pricing Table', 'stackable-ultimate-gutenberg-blocks' )
__( 'Progress Bar', 'stackable-ultimate-gutenberg-blocks' )
__( 'Visualize a progress value or percentage in a bar.', 'stackable-ultimate-gutenberg-blocks' )
__( 'percentage status', 'stackable-ultimate-gutenberg-blocks' )
__( 'Progress Circle', 'stackable-ultimate-gutenberg-blocks' )
__( 'Visualize a progress value or percentage in a circle.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Separator', 'stackable-ultimate-gutenberg-blocks' )
__( 'A fancy separator to be placed between content.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Svg Divider', 'stackable-ultimate-gutenberg-blocks' )
__( 'Spacer', 'stackable-ultimate-gutenberg-blocks' )
__( 'Sometimes you just need some space.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Subtitle', 'stackable-ultimate-gutenberg-blocks' )
__( 'Subtitle text that you can add custom styling to from the global settings.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tab Content', 'stackable-ultimate-gutenberg-blocks' )
__( 'A wrapper for tab panels.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tab Labels', 'stackable-ultimate-gutenberg-blocks' )
__( 'Create interactive navigation within tabs.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Table of Contents', 'stackable-ultimate-gutenberg-blocks' )
__( 'Automatically generated table of contents based on Heading blocks.', 'stackable-ultimate-gutenberg-blocks' )
__( 'ToC', 'stackable-ultimate-gutenberg-blocks' )
__( 'Index', 'stackable-ultimate-gutenberg-blocks' )
__( 'Outline', 'stackable-ultimate-gutenberg-blocks' )
__( 'Tabs', 'stackable-ultimate-gutenberg-blocks' )
__( 'Organize and display content in multiple tabs.', 'stackable-ultimate-gutenberg-blocks' )
__( 'toggle', 'stackable-ultimate-gutenberg-blocks' )
__( 'Team Member', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display members of your team or your office. Use multiple Team Member blocks if you have a large team.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Testimonial', 'stackable-ultimate-gutenberg-blocks' )
__( 'Showcase what your users say about your product or service.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Start with the building block of all page layouts.', 'stackable-ultimate-gutenberg-blocks' )
__( 'Paragraph', 'stackable-ultimate-gutenberg-blocks' )
__( 'Timeline', 'stackable-ultimate-gutenberg-blocks' )
__( 'Show events in chronological order', 'stackable-ultimate-gutenberg-blocks' )
__( 'history', 'stackable-ultimate-gutenberg-blocks' )
__( 'milestone', 'stackable-ultimate-gutenberg-blocks' )
__( 'Video Popup', 'stackable-ultimate-gutenberg-blocks' )
__( 'Display a large thumbnail that your users can click to play a video full-screen. Great for introductory or tutorial videos.', 'stackable-ultimate-gutenberg-blocks' )
__( 'YouTube', 'stackable-ultimate-gutenberg-blocks' )
__( 'Vimeo', 'stackable-ultimate-gutenberg-blocks' )
__( 'Embed Mp4', 'stackable-ultimate-gutenberg-blocks' )

}
