var frontend_blocks;(()=>{var r={895:r=>{function o(r){var o=new Error("Cannot find module '"+r+"'");throw o.code="MODULE_NOT_FOUND",o}o.keys=()=>[],o.resolve=o,o.id=895,r.exports=o}},o={};function e(t){var n=o[t];if(void 0!==n)return n.exports;var s=o[t]={exports:{}};return r[t](s,s.exports,e),s.exports}e.o=(r,o)=>Object.prototype.hasOwnProperty.call(r,o),(()=>{const r=e(895);r.keys().forEach((o=>r(o)))})(),frontend_blocks={}})();