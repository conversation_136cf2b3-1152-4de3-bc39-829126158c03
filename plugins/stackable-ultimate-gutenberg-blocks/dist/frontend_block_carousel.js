var frontend_block_carousel;(()=>{"use strict";var e,t={};(e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})})(t);class s{constructor(e){this.el=e}init=()=>{this.currentSlide=1,this.currentZIndex=1,this.wrapper=this.el.querySelector(".stk-block-carousel__slider-wrapper"),this.type=this.el.classList.contains("stk--is-fade")?"fade":"slide",this.sliderEl=this.el.querySelector(".stk-block-carousel__slider"),this.slideEls=Array.from(this.sliderEl.children),this.isRTL="rtl"===document.documentElement?.getAttribute("dir")||"rtl"===document.body?.getAttribute("dir"),this.infiniteScroll=this.el.classList.contains("stk--infinite-scroll"),this.isiOS=function(){const e=navigator?.userAgent;return e&&(-1===e.indexOf("iPhone")||e.indexOf("iPad"))}(),this.hasTouched=!1;const e=this.el.querySelectorAll(".stk-block-carousel__dots"),t=this.el.querySelectorAll(".stk-block-carousel__button__prev"),s=this.el.querySelectorAll(".stk-block-carousel__button__next");this.dotsEl=e[e.length-1],this.prevEl=t[t.length-1],this.nextEl=s[s.length-1],this.liveregion=document.createElement("div"),this.liveregion.setAttribute("aria-live","polite"),this.liveregion.setAttribute("aria-atomic","true"),this.liveregion.setAttribute("class","liveregion stk--hidden"),this.wrapper.appendChild(this.liveregion),this.fixChildrenAccessibility(),this.initProperties(),this.addEventListeners(),this.fixAccessibility(this.currentSlide),this.setDotActive(this.currentSlide),this.fixInlineScrollNavigation(),this.slideEls[this.currentSlide-1].classList.add("stk-block-carousel__slide--active"),this.unpauseAutoplay()};initProperties=()=>{this.slidesToShow="slide"===this.type?parseInt(getComputedStyle(this.el).getPropertyValue("--slides-to-show"),10):1,this.autoplay=this.sliderEl.dataset.autoplay,this.slideOffset=this.isRTL&&"slide"===this.type?this.slidesToShow:1,this.isRTL&&(this.updateDefaultIcon(),this.slidesToShow>1&&(this.currentSlide=this.slidesToShow,this.setDotActive(this.currentSlide))),this.infiniteScroll&&!this.el._StackableHasInitCarousel&&(this.clones=this.slideEls.map((e=>e.cloneNode(!0))),this.clones.map(((e,t)=>(e.classList.add(`stk-slide-clone-${t+1}`),t===this.clones.length-1?this.sliderEl.insertBefore(e,this.slideEls[0]):this.sliderEl.appendChild(e)))),this.sliderEl.style.scrollBehavior="unset",this.sliderEl.scrollLeft=this.slideEls[0].offsetLeft,this.sliderEl.style.scrollBehavior="",this.currentSlide=1,this.swappedSlides=0),this.updateDots()};updateDots=()=>{if(!this.dotsEl)return;this.dotEls=[],this.dotsEl.innerHTML="";const e=this.dotsEl.dataset.label;this.slideEls.forEach(((t,s)=>{if(!this.infiniteScroll&&!this.isRTL&&s>=this.slideEls.length-this.slidesToShow+1)return;const i=document.createElement("div"),l=document.createElement("button");i.setAttribute("role","listitem"),l.classList.add("stk-block-carousel__dot"),l.setAttribute("aria-label",e.replace(/%+d/,s+1)),this.currentSlide===s+1&&l.classList.add("stk-block-carousel__dot--active"),i.appendChild(l),s>=this.slideOffset-1&&this.dotsEl.appendChild(i),l.addEventListener("click",(()=>{this.pauseAutoplay(),this.goToSlide(s+1),this.unpauseAutoplay()})),this.dotEls.push(l)}))};updateDefaultIcon=()=>{const e=this.el.querySelector(".stk-block-carousel__button.stk-block-carousel__button__prev .fa-chevron-left"),t=this.el.querySelector(".stk-block-carousel__button.stk-block-carousel__button__next .fa-chevron-right");e&&(e.style.transform="rotate(180deg)"),t&&(t.style.transform="rotate(180deg)")};addEventListeners=()=>{window&&window.addEventListener("resize",this.initProperties),this.sliderEl.addEventListener("wheel",this.onWheel,{passive:!0}),this.sliderEl.addEventListener("mousedown",this.dragMouseDown),this.sliderEl.addEventListener("touchstart",this.dragTouchStart,{passive:!0}),this.sliderEl.addEventListener("scroll",this.onScroll),this.autoplay&&(this.el.addEventListener("mouseenter",this.pauseAutoplay),this.el.addEventListener("mouseleave",this.unpauseAutoplay)),this.prevEl&&this.prevEl.addEventListener("click",this.prevSlide),this.nextEl&&this.nextEl.addEventListener("click",this.nextSlide),this.isiOS&&document.body.addEventListener("touchend",this.onTouchEndIOS)};maxSlides=()=>{let e=this.slideEls.length;return"slide"!==this.type||this.isRTL||(e-=this.slidesToShow-1),e};needToSwapCount=e=>this.slidesToShow-(this.slideEls.length-e+1);nextSlide=()=>{let e=this.currentSlide+1;this.infiniteScroll&&e>this.maxSlides()?this.swapSlides(e,"N"):(!this.infiniteScroll&&e>this.maxSlides()&&(e=this.slideOffset),this.goToSlide(e))};prevSlide=()=>{let e=this.currentSlide-1;this.infiniteScroll&&(e<this.slideOffset||this.needToSwapCount(e)>=0)?this.swapSlides(e,"P"):(!this.infiniteScroll&&e<this.slideOffset&&(e=this.maxSlides()),this.goToSlide(e))};swapSlides=(e,t)=>{let s=!1;this.slidesToShow===this.slideEls.length&&(s=!0),"N"===t&&e>this.slideEls.length?(e=this.slideOffset,s=!0):"P"===t&&e<this.slideOffset&&(e=this.slideEls.length,s=!0);const i=this.needToSwapCount(e);if(i>0&&this.swappedSlides<i){const e=[...this.slideEls.slice(this.swappedSlides,i)],s=[...this.clones.slice(this.swappedSlides,i)];if(e.map((e=>this.sliderEl.insertBefore(e,this.clones[i]))),s.map((e=>this.sliderEl.insertBefore(e,this.slideEls[i]))),this.slidesToShow===this.slideEls.length&&"N"===t){const e=this.sliderEl.children;this.sliderEl.append(e[0])}else this.slidesToShow===this.slideEls.length&&"P"===t&&[...Array.from(this.sliderEl.children).slice(-2)].reverse().map((e=>this.sliderEl.insertBefore(e,this.sliderEl.children[0])));this.swappedSlides=i}else if(this.swappedSlides>i){const e=i>0?i:0,t=[...this.slideEls.slice(e,this.swappedSlides)],s=[...this.clones.slice(e,this.swappedSlides)];if(t.map((e=>this.sliderEl.insertBefore(e,this.slideEls[this.swappedSlides]))),s.map((e=>this.sliderEl.insertBefore(e,this.clones[this.swappedSlides]))),this.swappedSlides=e,this.slidesToShow===this.slideEls.length){const e=this.sliderEl.children;this.sliderEl.insertBefore(e[e.length-1],e[0])}}s&&(this.sliderEl.style.scrollBehavior="unset",this.sliderEl.scrollLeft="N"===t||1===this.slidesToShow?this.clones[this.currentSlide-1].offsetLeft:this.slideEls[this.currentSlide-1].offsetLeft,this.sliderEl.style.scrollBehavior=""),setTimeout((()=>{this.goToSlide(e)}),1)};goToSlide=(e,t=!1)=>{if(e!==this.currentSlide||t){if(this.slideEls[this.currentSlide-1].classList.remove("stk-block-carousel__slide--active"),this.slideEls[e-1].classList.add("stk-block-carousel__slide--active"),"slide"===this.type)this.sliderEl.scrollLeft=this.slideEls[e-1].offsetLeft;else if("fade"===this.type){this.slideEls[this.currentSlide-1].style.opacity=0;const t=this.slideEls[e-1];t.style.zIndex=++this.currentZIndex,t.style.transition="none",t.style.opacity=0,t.style.visibility="visible",t.style.left=`${this.isRTL?"":"-"}${100*(e-1)}%`,setTimeout((()=>{t.style.transition="",t.style.opacity=1}),1)}this.fixAccessibility(e),this.setDotActive(e),this.currentSlide=e;try{this.liveregion.textContent=this.sliderEl.dataset.labelSlideOf.replace(/%+d/,e).replace(/%+d/,this.maxSlides())}catch(e){console.error("Carousel Slide N of N accessibility label is of invalid format")}clearTimeout(this.tempDisableOnScroll),this.tempDisableOnScroll=setTimeout((()=>{this.tempDisableOnScroll=null}),500)}};fixAccessibility=e=>{if("slide"===this.type)for(let t=1;t<=this.slideEls.length;t++)e<=t&&t<=e+this.slidesToShow-1?this.setSlideToVisible(t):this.setSlideToHide(t);else for(let t=1;t<=this.slideEls.length;t++)t===e?this.setSlideToVisible(t):this.setSlideToHide(t)};fixChildrenAccessibility=()=>{this.slideEls.forEach((e=>{e.setAttribute("role","listitem")}))};setDotActive=e=>{this.dotEls?.forEach(((t,s)=>{e===s+1||this.infiniteScroll&&0===e&&s===this.dotEls.length-1?t.classList.add("stk-block-carousel__dot--active"):t.classList.remove("stk-block-carousel__dot--active")}))};setSlideToVisible=e=>{const t=this.slideEls[e-1];t.querySelectorAll('button, a, input, [role="button"]').forEach((e=>{e.removeAttribute("tabindex")})),t.setAttribute("aria-hidden","false")};setSlideToHide=e=>{const t=this.slideEls[e-1];t.querySelectorAll('button, a, input, [role="button"]').forEach((e=>{e.setAttribute("tabindex","-1")})),t.setAttribute("aria-hidden","true")};pauseAutoplay=()=>{setTimeout((()=>{clearInterval(this.autoplayInterval)}))};unpauseAutoplay=()=>{this.autoplay&&(clearInterval(this.autoplayInterval),this.autoplayInterval=setInterval(this.nextSlide,this.autoplay))};onWheel=e=>{if("fade"===this.type){if(this.wheelTimeout)return;e.deltaX>=15?(this.nextSlide(),this.wheelTimeout=setTimeout((()=>{this.wheelTimeout=null}),500)):e.deltaX<=-15&&(this.prevSlide(),this.wheelTimeout=setTimeout((()=>{this.wheelTimeout=null}),500))}else this.infiniteScroll&&e.deltaX<=-1&&0===this.sliderEl.scrollLeft?(this.sliderEl.style.scrollBehavior="unset",this.sliderEl.scrollLeft=this.slideEls[this.slideEls.length-1].offsetLeft,this.sliderEl.style.scrollBehavior=""):this.infiniteScroll&&e.deltaX>=1&&this.sliderEl.scrollLeft>=this.clones[0].offsetLeft&&this.clones.every(((e,t)=>this.sliderEl.scrollLeft!==e.offsetLeft||(this.sliderEl.style.scrollBehavior="unset",this.sliderEl.scrollLeft=this.slideEls[t].offsetLeft,this.sliderEl.style.scrollBehavior="",!1)))};dragMouseDown=e=>{this.initialClientX=0,this.sliderEl.style.cursor="grabbing",clearTimeout(this.dragTimeout),this.sliderEl.classList.add("stk--snapping-deactivated"),this.initialScrollLeft=this.sliderEl.scrollLeft,this.initialClientX=e.clientX,document.body.addEventListener("mousemove",this.dragMouseMove),document.body.addEventListener("mouseup",this.dragMouseUp),this.pauseAutoplay()};dragMouseMove=e=>{let t=e.clientX-this.initialClientX;"slide"===this.type?(this.infiniteScroll&&0===this.sliderEl.scrollLeft&&t>0?(this.initialScrollLeft=this.slideEls[this.slideEls.length-1].offsetLeft,this.initialClientX=e.clientX,t=0):this.infiniteScroll&&this.sliderEl.scrollLeft>=this.clones[0].offsetLeft&&t<0&&(this.initialScrollLeft=this.slideEls[0].offsetLeft,this.initialClientX=e.clientX,t=0),this.sliderEl.scrollTo({left:this.initialScrollLeft-t})):"fade"===this.type&&(t<-40?(this.nextSlide(),this.dragMouseUp()):t>40&&(this.prevSlide(),this.dragMouseUp())),e.preventDefault(),this.pauseAutoplay()};dragMouseUp=()=>{if(document.body.removeEventListener("mousemove",this.dragMouseMove),document.body.removeEventListener("mouseup",this.dragMouseUp),this.sliderEl.style.cursor="","slide"===this.type){const e=this.sliderEl.scrollLeft;this.sliderEl.classList.remove("stk--snapping-deactivated");const t=this.sliderEl.scrollLeft;this.sliderEl.classList.add("stk--snapping-deactivated"),this.sliderEl.scrollLeft=e,this.sliderEl.scrollTo({left:t,behavior:"smooth"});const{slide:s}=this.slideEls.reduce(((e,s,i)=>{const l=i+1,o=Math.abs(s.offsetLeft-t);return o<=e.offsetDiff?{slide:l,offsetDiff:o}:e}),{slide:1,offsetDiff:1e3});this.currentSlide=s,this.setDotActive(s)}clearTimeout(this.dragTimeout),this.dragTimeout=setTimeout((()=>{this.sliderEl.classList.remove("stk--snapping-deactivated")}),500),this.unpauseAutoplay()};dragTouchStart=e=>{this.isiOS&&(this.hasTouched=!0),this.sliderEl.addEventListener("touchend",this.dragTouchEnd),this.sliderEl.addEventListener("touchmove",this.dragTouchMove),this.initialClientX=e.targetTouches[0].clientX,this.pauseAutoplay()};dragTouchEnd=()=>{this.sliderEl.removeEventListener("touchend",this.dragTouchEnd),this.sliderEl.removeEventListener("touchmove",this.dragTouchMove),this.unpauseAutoplay()};dragTouchMove=e=>{if("fade"===this.type){const t=e.targetTouches[0].clientX-this.initialClientX;t<=-40?(this.nextSlide(),this.dragTouchEnd()):t>=40&&(this.prevSlide(),this.dragTouchEnd())}};onScroll=()=>{if("slide"===this.type){if(this.tempDisableOnScroll)return;const e=this.sliderEl.scrollLeft,t=this.infiniteScroll?[...this.slideEls,...this.clones]:this.slideEls;let{slide:s}=t.reduce(((t,s,i)=>{const l=i+1,o=Math.abs(s.offsetLeft-e);return o<=t.offsetDiff?{slide:l,offsetDiff:o}:t}),{slide:1,offsetDiff:1e3});this.infiniteScroll&&s>this.slideEls.length&&(s-=this.slideEls.length),this.currentSlide=s,this.setDotActive(s)}};onTouchEndIOS=e=>{if(!this.hasTouched)return;const t=e.target;if(!t.closest(".stk-block-carousel__slider")&&("A"===t.tagName||"BUTTON"===t.tagName||t.closest("a")||t.closest("button"))){const s=new MouseEvent("click",{bubbles:!0,cancelable:!0});t.dispatchEvent(s),e.preventDefault()}this.hasTouched=!1};fixInlineScrollNavigation=()=>{let e=null;const t=()=>{this.unpauseAutoplay(),document.removeEventListener("scroll",s)},s=()=>{clearTimeout(e),e=setTimeout(t,200)};document.addEventListener("click",(i=>{i.target.closest('[href^="#"]')&&(this.pauseAutoplay(),document.addEventListener("scroll",s,{passive:!0}),clearTimeout(e),e=setTimeout(t,200))}),{passive:!0})}}window.stackableCarousel=new class{init=()=>{document.querySelectorAll(".stk-block-carousel").forEach((e=>{if(!e._StackableHasInitCarousel){const t=new s(e);e.carousel=t,t.init(),e._StackableHasInitCarousel=!0}}))}},e=window.stackableCarousel.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",e):e()),frontend_block_carousel=t})();