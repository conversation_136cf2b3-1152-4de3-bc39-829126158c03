var frontend_block_expand;(()=>{"use strict";var t,e={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(e),window.stackableExpand=new class{init=()=>{const t=document.querySelectorAll(".stk-block-expand .stk-button"),e=document.querySelectorAll(".stk-block-expand"),a=t=>{const e=t.target.closest(".stk-block-expand"),a=e.querySelectorAll('[aria-hidden="false"]');e.querySelectorAll('[aria-hidden="true"]').forEach((t=>t.setAttribute("aria-hidden","false"))),a.forEach((t=>t.setAttribute("aria-hidden","true"))),e.querySelector('.stk-button[aria-hidden="false"]').focus({preventScroll:!0}),t.preventDefault()};t.forEach((t=>{t._StackableHasInitExpand||(t.addEventListener("click",a),t._StackableHasInitExpand=!0)})),e.forEach((t=>{var e;t._StackableHasInitExpandFix||((e=t).hasAttribute("aria-expanded")&&e.removeAttribute("aria-expanded"),(t=>{const e=t.querySelector(".stk-block-expand__short-text"),a=t.querySelector(".stk-block-expand__show-button > .stk-button"),r=t.querySelector(".stk-block-expand__more-text"),o=t.querySelector(".stk-block-expand__hide-button > .stk-button");e.setAttribute("id",e.getAttribute("data-block-id")),r.setAttribute("id",r.getAttribute("data-block-id")),a.setAttribute("aria-controls",e.getAttribute("data-block-id")),o.setAttribute("aria-controls",r.getAttribute("data-block-id"))})(t),t._StackableHasInitExpandFix=!0)}))}},t=window.stackableExpand.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",t):t()),frontend_block_expand=e})();