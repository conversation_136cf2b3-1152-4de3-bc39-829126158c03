var frontend_block_countdown;(()=>{"use strict";var t={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(t);const e=3600,o=86400;class i{constructor(t){this.el=t,this.blockId=t.getAttribute("data-block-id"),this.countdownInterval,this.date=Date.parse(t.getAttribute("data-stk-countdown-date")),this.countdownType=t.getAttribute("data-stk-countdown-type"),this.duration=parseInt(t.getAttribute("data-stk-countdown-duration"),10),this.restartInterval=parseFloat(t.getAttribute("data-stk-countdown-restart-interval"))||0,this.action=t.getAttribute("data-stk-countdown-action"),this.timezone=t.getAttribute("data-stk-countdown-timezone")?{timeZone:t.getAttribute("data-stk-countdown-timezone")}:{},this.isDoubleDigit="true"===t.getAttribute("data-stk-countdown-is-double-digit"),this.tempDate=void 0,this.counter=!1,this.day=t.querySelector(".stk-block-countdown__digit-day"),this.hour=t.querySelector(".stk-block-countdown__digit-hour"),this.minute=t.querySelector(".stk-block-countdown__digit-minute"),this.second=t.querySelector(".stk-block-countdown__digit-second"),this.timer=this.el.querySelector(".stk-block-countdown__container"),this.message=this.el.querySelector(".stk-block-countdown__message")}clearTimer=()=>{clearInterval(this.countdownInterval)};addZero=t=>{if(this.isDoubleDigit){const e=t<=0?0:t;return t<10?"0"+e:t}return t<=0?0:t};saveTimeRemaining=t=>{sessionStorage.setItem(this.blockId,t)};preCountDown=()=>{const t=sessionStorage.getItem(this.blockId),i=Math.floor(t/o),n=Math.floor(t%o/e),s=Math.floor(t%e/60),a=Math.floor(t%60/1);this.day&&(this.day.textContent=this.addZero(i)),this.hour&&(this.hour.textContent=this.addZero(n)),this.minute&&(this.minute.textContent=this.addZero(s)),this.second&&(this.second.textContent=this.addZero(a))};countDown=()=>{let t=Math.floor(this.date/1e3)-Math.floor(Date.parse((new Date).toLocaleString("en-US",this.timezone))/1e3),i=!1,n=0;t:if("recurring"===this.countdownType){const o=Date.parse((new Date).toLocaleString("en-US",this.timezone))>this.date;if(this.tempDate-Date.now()-Math.floor(this.restartInterval*e*1e3)<=0){this.counter=!0,t=0;break t}if(o){const o=this.duration+Math.floor(this.restartInterval*e),s=Math.floor((Date.now()/1e3-this.date/1e3)/o)+1,a=s*o+Math.floor(this.date/1e3)-Math.floor(Date.now()/1e3);this.tempDate=this.date+s*o*1e3,a>this.restartInterval*e?t=a-Math.floor(this.restartInterval*e):(i=!0,n=a,t=0,this.counter=!0)}else t=o?this.duration+Math.floor(this.date/1e3)-Math.floor(Date.now()/1e3):this.duration}this.saveTimeRemaining(t);const s=Math.floor(t/o),a=Math.floor(t%o/e),r=Math.floor(t%e/60),d=Math.floor(t%60/1);if(this.day&&(this.day.textContent=this.addZero(s)),this.hour&&(this.hour.textContent=this.addZero(a)),this.minute&&(this.minute.textContent=this.addZero(r)),this.second&&(this.second.textContent=this.addZero(d)),t<=0||this.counter){if(this.clearTimer(),"recurring"===this.countdownType){this.tempDate=void 0,this.counter=!1;const t=i?n:this.restartInterval*e;this.saveTimeRemaining(this.duration),setTimeout((()=>{this.preCountDown(),this.countdownInterval=setInterval(this.countDown.bind(this),1e3)}),1e3*t)}"hide"===this.action&&(this.el.style.display="none"),"showMessage"===this.action&&(this.timer.setAttribute("style","display:none !important"),this.message.setAttribute("style","display:block !important"))}};init=()=>{"dueDate"===this.countdownType&&this.preCountDown(),this.countdownInterval=setInterval(this.countDown.bind(this),1e3)}}var n;window.stackableCountdown=new class{init=()=>{document.querySelectorAll(".stk-block-countdown").forEach((t=>{new i(t).init()}))}},n=window.stackableCountdown.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",n):n()),frontend_block_countdown=t})();