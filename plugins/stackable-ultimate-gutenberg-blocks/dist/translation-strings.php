<?php
/**
 * Auto-generated translation file.
 */

// Exit if accessed
exit;

__( 'Scheme unavailable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Essential Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'All the necessary building blocks you need to design anything.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Special Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blocks with special functionality that will allow you to create distinctive designs.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Section Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use these blocks act as templates to help you build sections effortlessly.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Editor Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Nested Block Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Nested Wide Block Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable Text as Default Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Linking (Beta)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Toolbar Text Highlight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Toolbar Dynamic Content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Copy & Paste Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save as Default Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Don\'t show help video tooltips', 'stackable-ultimate-gutenberg-blocks' );
__( 'Auto-Collapse Panels', 'stackable-ultimate-gutenberg-blocks' );
__( 'Responsiveness', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tablet Breakpoint', 'stackable-ultimate-gutenberg-blocks' );
__( 'Mobile Breakpoint', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Optimization', 'stackable-ultimate-gutenberg-blocks' );
__( 'Optimize Inline CSS', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lazy Load Images within Carousels', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Force Typography Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Role Manager', 'stackable-ultimate-gutenberg-blocks' );
__( 'Administrator', 'stackable-ultimate-gutenberg-blocks' );
__( 'Editor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author', 'stackable-ultimate-gutenberg-blocks' );
__( 'Contributor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subscriber', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'Integration', 'stackable-ultimate-gutenberg-blocks' );
__( 'Google Maps API Key', 'stackable-ultimate-gutenberg-blocks' );
__( 'FontAwesome Pro Kit', 'stackable-ultimate-gutenberg-blocks' );
__( 'FontAwesome Icon Library Version', 'stackable-ultimate-gutenberg-blocks' );
__( 'Miscellaneous ', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Go premium notices', 'stackable-ultimate-gutenberg-blocks' );
__( 'Generate Global Colors for native blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load version 2 blocks in the editor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load version 2 blocks in the editor only when the page was using version 2 blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load version 2 frontend block stylesheet and scripts for backward compatibility', 'stackable-ultimate-gutenberg-blocks' );
__( 'V2 Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'See example', 'stackable-ultimate-gutenberg-blocks' );
__( 'Error getting Stackable settings. We got the following error. Please contact your administrator.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disable %s block?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable %s block?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disabling this block will also disable these blocks that require this block to function:', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enabling this block will also enable these blocks that are needed for this block to function:', 'stackable-ultimate-gutenberg-blocks' );
__( 'Cancel', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable', 'stackable-ultimate-gutenberg-blocks' );
__( 'There are unsaved changes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Saved Succesfully!', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save Changes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Search settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'No matching settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Widths', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjust the width of Stackable blocks here.', 'stackable-ultimate-gutenberg-blocks' );
__( 'The width used when a Columns block has its Content Width set to center. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula.', 'stackable-ultimate-gutenberg-blocks' );
__( 'The width used when a Columns block has its Content Width set to wide. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula.', 'stackable-ultimate-gutenberg-blocks' );
__( 'If enabled, Stackable Text blocks will be added by default instead of the native Paragraph Block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a button on the top of the editor which gives access to a collection of pre-made block designs. Note: You can still access the Design Library by adding the Design Library block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a button on the top of the editor which gives access to Stackable settings. Note: You won\'t be able to access Stackable settings when this is disabled.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Gives you the ability to link columns. Any changes you make on one column will automatically get applied on the other columns.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more', 'stackable-ultimate-gutenberg-blocks' );
__( 'Toolbar', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can disable some toolbar features here.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a toolbar button for highlighting text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a toolbar button for inserting and modifying dynamic content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a toolbar button for advanced copying and pasting block styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a toolbar button for resetting the layout of a stackble block back to the original', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adds a toolbar button for saving a block as the default block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inspector', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disables the help video tooltips that appear in the inspector.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapse other inspector panels when opening another, keeping only one open at a time.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dynamic Breakpoints', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blocks can be styles differently for tablet and mobile screens, and some styles adjust to make them fit better in smaller screens. You can change the widths when tablet and mobile views are triggered. ', 'stackable-ultimate-gutenberg-blocks' );
__( 'Here you can enable, hide and disable Stackable blocks. Hiding blocks will hide the block from the list of available blocks. Disabling blocks will prevent them from being registered at all. When using block variations or design library patterns, disabled blocks will be substituted with the relevant core blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disable All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enabled', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hidden', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disabled', 'stackable-ultimate-gutenberg-blocks' );
__( 'Optimizations', 'stackable-ultimate-gutenberg-blocks' );
__( 'Here you can adjust some optimization settings that are performed by Stackable.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Optimize inlined CSS styles. If this is enabled, similar selectors will be combined together, helpful if you changed Block Defaults.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Disable this if you encounter layout or spacing issues when using images inside carousel-type blocks because of image lazy loading.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Here you can tweak Global Settings that affect the styles across your entire site.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Not forced', 'stackable-ultimate-gutenberg-blocks' );
__( 'Force styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lock the Block Editor\'s inspector for different user roles, and give clients edit access to only images and content. Content Editing Mode affects all blocks. ', 'stackable-ultimate-gutenberg-blocks' );
__( 'This is only available in Stackable Premium. ', 'stackable-ultimate-gutenberg-blocks' );
__( 'Go Premium', 'stackable-ultimate-gutenberg-blocks' );
__( 'Create Custom Fields that you can reference across your entire site. You can assign which roles can manage your Custom Fields. ', 'stackable-ultimate-gutenberg-blocks' );
__( 'Integrations', 'stackable-ultimate-gutenberg-blocks' );
__( 'Here are settings for the different integrations available in Stackable.', 'stackable-ultimate-gutenberg-blocks' );
__( 'You are using the version set in your Font Awesome Pro Kit.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Miscellaneous', 'stackable-ultimate-gutenberg-blocks' );
__( 'Below are other minor settings. Some may be useful when upgrading from older versions of Stackable.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show "Go premium" notices', 'stackable-ultimate-gutenberg-blocks' );
__( 'Migration Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'After enabling the version 2 blocks, please refresh the page to re-fetch the blocks from the server.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Migrating from version 2 to version 3?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about migration and the settings below', 'stackable-ultimate-gutenberg-blocks' );
__( '🏃‍♂️ Optimization Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Here are some settings that you can tweak to optimize Stackable.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more.', 'stackable-ultimate-gutenberg-blocks' );
__( 'This only works for version 2 blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable & Disable Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tutorials', 'stackable-ultimate-gutenberg-blocks' );
__( 'Get to know the plugin and start your journey with our brand new Stackable Courses.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Documentation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Visit our knowledge base for troubleshooting, guides, FAQs and updates.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Community', 'stackable-ultimate-gutenberg-blocks' );
__( 'Join our very active Stackable Community on Facebook.', 'stackable-ultimate-gutenberg-blocks' );
__( 'The Basics of Stackable Blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn how to personalize and tailor Stackable Blocks to match your website\'s unique style and design.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flexbox Controls', 'stackable-ultimate-gutenberg-blocks' );
__( 'Explore the powerful Flexbox controls in Stackable that allow you to effortlessly create dynamic and responsive layouts.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Introduction to Hover Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Discover the exciting world of hover styles in Stackable and learn how to add interactive and engaging effects to your website.', 'stackable-ultimate-gutenberg-blocks' );
__( 'How to Use Image Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Master the art of optimizing and enhancing images using Stackable\'s intuitive image settings for a visually captivating website.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Introduction to the Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dive into the fundamentals of the Stackable Design Library and learn how to streamline your website development process with ease.', 'stackable-ultimate-gutenberg-blocks' );
__( 'All About Typography in Stackable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unlock the full potential of typography in Stackable and gain insights on how to create visually stunning and impactful text designs.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unleash the Full Potential of the WordPress Block Editor by Turning It into a Page Builder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn the essentials in just a few minutes by watching this video. Scroll down to see more quick tutorials.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Getting Started', 'stackable-ultimate-gutenberg-blocks' );
__( 'Create a new page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn the essentials', 'stackable-ultimate-gutenberg-blocks' );
__( 'Check out our library of tutorials and guides', 'stackable-ultimate-gutenberg-blocks' );
__( 'Go to Stackable Learn', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sans-Serif', 'stackable-ultimate-gutenberg-blocks' );
__( 'Serif', 'stackable-ultimate-gutenberg-blocks' );
__( 'Serif Alternative', 'stackable-ultimate-gutenberg-blocks' );
__( 'Monospace', 'stackable-ultimate-gutenberg-blocks' );
__( 'System UI', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transitional', 'stackable-ultimate-gutenberg-blocks' );
__( 'Old Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Humanist', 'stackable-ultimate-gutenberg-blocks' );
__( 'Geometric Humanist', 'stackable-ultimate-gutenberg-blocks' );
__( 'Classical Humanist', 'stackable-ultimate-gutenberg-blocks' );
__( 'Neo Grotesque', 'stackable-ultimate-gutenberg-blocks' );
__( 'Monospace Slab Serif', 'stackable-ultimate-gutenberg-blocks' );
__( 'Monospace Code', 'stackable-ultimate-gutenberg-blocks' );
__( 'Industrial', 'stackable-ultimate-gutenberg-blocks' );
__( 'Rounded Sans', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slab Serif', 'stackable-ultimate-gutenberg-blocks' );
__( 'Antique', 'stackable-ultimate-gutenberg-blocks' );
__( 'Didone', 'stackable-ultimate-gutenberg-blocks' );
__( 'Handwritten', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description for this block. You can use this space for describing your block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description for this block. Use this space for describing your block. Any text will do.', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Address', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Article', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Aside', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Blockquote', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Div', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Details', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Footer', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Header', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Hgroup', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Main', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Nav', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Section', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Summary', 'HTML Tag', 'stackable-ultimate-gutenberg-blocks' );
__( 'General', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s HTML Tag', 'component', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overflow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Auto', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scroll', 'stackable-ultimate-gutenberg-blocks' );
__( 'Visible', 'stackable-ultimate-gutenberg-blocks' );
__( 'Clear', 'stackable-ultimate-gutenberg-blocks' );
__( 'Left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Both', 'stackable-ultimate-gutenberg-blocks' );
__( 'None', 'stackable-ultimate-gutenberg-blocks' );
__( 'Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the transparency of the entire block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Z-Index', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the stack order of different blocks to make one appear in front of another. A block with a higher z-index will show up on top of another block with a lower z-index.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sticky position may not work across all themes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Static', 'stackable-ultimate-gutenberg-blocks' );
__( 'Relative', 'stackable-ultimate-gutenberg-blocks' );
__( 'Absolute', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sticky', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Size & Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Borders & Shadows', 'stackable-ultimate-gutenberg-blocks' );
__( 'Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Supports links to images, videos, YouTube, Vimeo, and web pages that allow embedding. Opens inner image block if no link is provided', 'stackable-ultimate-gutenberg-blocks' );
__( 'Darken', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lift', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scale', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lift & Scale', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lift More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scale More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lift & Scale More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Triggers animation or effects when you mouse over', 'stackable-ultimate-gutenberg-blocks' );
__( 'Single', 'stackable-ultimate-gutenberg-blocks' );
__( 'Gradient', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Min. Button Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the space between the button text and button borders', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Size & Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Borders & Shadows', 'stackable-ultimate-gutenberg-blocks' );
__( 'When enabled, the last column will be cloned instead of adding a blank column.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Columns', 'stackable-ultimate-gutenberg-blocks' );
__( 'Allow Column Wrapping', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inner Column Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets column paddings, the space inside the block between the block elements and the column container border', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the distance between two or more columns', 'stackable-ultimate-gutenberg-blocks' );
__( 'Row Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Conditional Display', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Min. Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Max Container Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Horizontal Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Vertical Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Trigger hover state on nested blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Borders & Shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Wide', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Full', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set the content to be smaller than the block width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom CSS', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Justified', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Min. Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Max Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Horizontal Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the placement of the column container to left, center or right. Not available when block width is set to full width.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the placement of all content in the block to align left, center or right', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Justify', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the horizontal position and spacing of the inner columns.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column contents need to be narrow for effect to show.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the vertical position of the inner columns relative to the columns block.', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Direction', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inner Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the horizontal/vertical position and spacing of the inner blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Aligns the horizontal/vertical position of the inner blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set Content Min. Height for alignment to display properly', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inner Block Wrapping', 'stackable-ultimate-gutenberg-blocks' );
__( 'No Wrap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wrap', 'stackable-ultimate-gutenberg-blocks' );
__( '%s %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the gap between the columns of inner blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the gap between inner blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the gap between the rows of inner blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Motion Effects', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Rotation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Border Radius', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Outline Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Outline Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Shape', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Shape', 'stackable-ultimate-gutenberg-blocks' );
__( 'Select Image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Url', 'stackable-ultimate-gutenberg-blocks' );
__( 'Aspect Ratio', 'stackable-ultimate-gutenberg-blocks' );
__( 'Original', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square 1:1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Standard 4:3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Classic 3:2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wide 16:9', 'stackable-ultimate-gutenberg-blocks' );
__( 'Cinematic 2:1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ultra Wide 3:1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Panoramic 4:1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Portrait 3:4', 'stackable-ultimate-gutenberg-blocks' );
__( 'Classic Portrait 2:3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tall 9:16', 'stackable-ultimate-gutenberg-blocks' );
__( 'Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the image width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the image height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open Image in Lightbox', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Alt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Empty Alt Attribute', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zoom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the intensity of the image shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the image display size to thumbnail, medium, large or full size. A smaller image size will also load faster.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Border Radius', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the radius of image corners to make them more rounded', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Blend Mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets how the overlay color blends with the image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Focal point', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Fit', 'stackable-ultimate-gutenberg-blocks' );
__( 'Contain', 'stackable-ultimate-gutenberg-blocks' );
__( 'Cover', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fill', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scale Down', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the fit to default, contain, cover, fill, none, and scale down. Image fit specifies how an image resizes in a container.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Shape', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change the shape of the image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flip Shape Horizontally', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flip Shape Vertically', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stretch Shape Mask', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Filter', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Attributes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Responsive', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide on Desktop', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide on Tablet', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide on Mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress Circle', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress Bar', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress', 'stackable-ultimate-gutenberg-blocks' );
__( 'Maximum Progress', 'stackable-ultimate-gutenberg-blocks' );
__( 'Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Apply border radius to bar', 'stackable-ultimate-gutenberg-blocks' );
__( 'Thickness', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bar Color #%s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bar Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Gradient Direction (degrees)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adv. Gradient Color Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Gradient Direction', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the direction (in degrees) of the colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color %d Location', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the placement of each color in relation to the other color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Gradient Blend Mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets how this background gradient/image blends into the other background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Rounded', 'stackable-ultimate-gutenberg-blocks' );
__( 'Animate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Accessibility Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress Bar Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress Prefix', 'stackable-ultimate-gutenberg-blocks' );
__( 'Progress Suffix', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s %d', 'Nth Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wave', 'stackable-ultimate-gutenberg-blocks' );
__( 'Straight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slant', 'stackable-ultimate-gutenberg-blocks' );
__( 'Curve', 'stackable-ultimate-gutenberg-blocks' );
__( 'Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the height of the separator to stretch or compress vertically', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow / Outline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the intensity of the separator shadow and makes the separator more prominent', 'stackable-ultimate-gutenberg-blocks' );
__( 'Invert Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flip Horizontally', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flip Vertically', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bring to Front', 'stackable-ultimate-gutenberg-blocks' );
__( 'Brings the separator layer in front of other block elements', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Separator', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom Separator', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transform & Transition', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use theme heading margins', 'stackable-ultimate-gutenberg-blocks' );
__( 'Typography', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font Family', 'stackable-ultimate-gutenberg-blocks' );
__( 'Theme Default', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the font set to be used for the element', 'stackable-ultimate-gutenberg-blocks' );
__( 'Weight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Normal', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bold', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font weight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the thinness or thickness of text characters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transform', 'stackable-ultimate-gutenberg-blocks' );
__( 'Uppercase', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lowercase', 'stackable-ultimate-gutenberg-blocks' );
__( 'Capitalize', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the usage of upper or lower case', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Italic', 'stackable-ultimate-gutenberg-blocks' );
__( 'Oblique', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line-Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the vertical distance between lines of text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Letter Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Letter spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the distance or space between letters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the size of text characters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open at the start', 'stackable-ultimate-gutenberg-blocks' );
__( 'Close adjacent on open', 'stackable-ultimate-gutenberg-blocks' );
__( 'Automatically closes adjacent accordion panels when clicked.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable FAQ Schema', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open Icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'The open icon will appear when the accordion is opened', 'stackable-ultimate-gutenberg-blocks' );
__( 'Title for This Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description for this block. Use this space for describing your block. Any text will do. Description for this block. You can use this space for describing your block.', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s Layout', 'Block layout name', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Title for This Block', 'Heading placeholder', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Description for this block. Use this space for describing your block. Any text will do. Description for this block. You can use this space for describing your block.', 'Content placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Plain', 'stackable-ultimate-gutenberg-blocks' );
__( 'Colored', 'stackable-ultimate-gutenberg-blocks' );
__( 'Plus', 'stackable-ultimate-gutenberg-blocks' );
__( 'Quotation Mark Icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pick another icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Round Thin', 'stackable-ultimate-gutenberg-blocks' );
__( 'Round', 'stackable-ultimate-gutenberg-blocks' );
__( 'Round Thick', 'stackable-ultimate-gutenberg-blocks' );
__( 'Round Fat', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square Thin', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square Simple', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square Modern', 'stackable-ultimate-gutenberg-blocks' );
__( 'Square Fat', 'stackable-ultimate-gutenberg-blocks' );
__( 'Simple', 'stackable-ultimate-gutenberg-blocks' );
__( 'Highlighted', 'stackable-ultimate-gutenberg-blocks' );
__( 'Huge', 'stackable-ultimate-gutenberg-blocks' );
__( 'Centered Quote', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ghost', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Call to action', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full Width Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Flex Wrap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wrap Reverse', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Social Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add social buttons.', 'stackable-ultimate-gutenberg-blocks' );
__( 'When linked, styling this block would also style other linked blocks in adjacent columns.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about linking', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal 3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Split Centered', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slides', 'stackable-ultimate-gutenberg-blocks' );
__( 'Carousel Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slide', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fade', 'stackable-ultimate-gutenberg-blocks' );
__( 'Infinite Scrolling', 'stackable-ultimate-gutenberg-blocks' );
__( 'Only visible in the frontend.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slides to Show', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slide Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fade duration', 'stackable-ultimate-gutenberg-blocks' );
__( 'Autoplay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Speed (ms)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Arrows', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous Slide Icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next Slide Icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Arrow Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inside', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outside', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Gap', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Color', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Width', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Height', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show %s on mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'arrows', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dots', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dot Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Solid', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dot', 'stackable-ultimate-gutenberg-blocks' );
__( 'Active Dot', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'To improve accessibility, the clickable area of the dots will not go below %s.', 'stackable-ultimate-gutenberg-blocks' );
__( 'dots', 'stackable-ultimate-gutenberg-blocks' );
__( 'Accessibility', 'stackable-ultimate-gutenberg-blocks' );
__( '%s label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous slide', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next slide', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use %%d to show the slide number.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slide N of N', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use two %%d to show the slide number and the total slides. e.g. Slide 1 of 3.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the paddings between the column content and the border.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Last Block to Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( '100', 'stackable-ultimate-gutenberg-blocks' );
__( 'One column', 'stackable-ultimate-gutenberg-blocks' );
__( '50 / 50', 'stackable-ultimate-gutenberg-blocks' );
__( 'Two columns; equal split', 'stackable-ultimate-gutenberg-blocks' );
__( '30 / 70', 'stackable-ultimate-gutenberg-blocks' );
__( 'Two columns; one-third, two-thirds split', 'stackable-ultimate-gutenberg-blocks' );
__( '70 / 30', 'stackable-ultimate-gutenberg-blocks' );
__( 'Two columns; two-thirds, one-third split', 'stackable-ultimate-gutenberg-blocks' );
__( '33 / 33 / 33', 'stackable-ultimate-gutenberg-blocks' );
__( 'Three columns; equal split', 'stackable-ultimate-gutenberg-blocks' );
__( '25 / 50 / 25', 'stackable-ultimate-gutenberg-blocks' );
__( 'Three columns; wide center column', 'stackable-ultimate-gutenberg-blocks' );
__( '25 / 25 / 25 / 25', 'stackable-ultimate-gutenberg-blocks' );
__( 'Four columns; equal split', 'stackable-ultimate-gutenberg-blocks' );
__( '1,234.56', 'stackable-ultimate-gutenberg-blocks' );
__( 'Counter', 'stackable-ultimate-gutenberg-blocks' );
__( 'Duration (ms)', 'stackable-ultimate-gutenberg-blocks' );
__( '1,234,567', 'stackable-ultimate-gutenberg-blocks' );
__( 'Colon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Due Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Recurring', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display Message Upon Expiration', 'stackable-ultimate-gutenberg-blocks' );
__( 'Days', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hours', 'stackable-ultimate-gutenberg-blocks' );
__( 'Minutes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Seconds', 'stackable-ultimate-gutenberg-blocks' );
__( 'End Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Start Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Timezone', 'stackable-ultimate-gutenberg-blocks' );
__( 'Action on Expiration', 'stackable-ultimate-gutenberg-blocks' );
__( 'Countdown Duration', 'stackable-ultimate-gutenberg-blocks' );
__( 'Restart Countdown After no. of Hours', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable Double Digit', 'stackable-ultimate-gutenberg-blocks' );
__( 'Box Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Label Top Margin', 'stackable-ultimate-gutenberg-blocks' );
__( 'Digits', 'stackable-ultimate-gutenberg-blocks' );
__( 'Labels', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Expired Message', 'stackable-ultimate-gutenberg-blocks' );
__( 'Days Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hours Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Minutes Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Seconds Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle for This Block', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Subtitle for This Block', 'Subtitle placeholder', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Button', 'Button placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full', 'stackable-ultimate-gutenberg-blocks' );
__( 'Faded', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open the Design Library and select a pre-designed block or layout.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bar', 'stackable-ultimate-gutenberg-blocks' );
__( 'Asterisks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Height / Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description for this block. Use this space for describing your block.', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Description for this block. Use this space for describing your block.', 'Content placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape 1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape 3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape 4', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape 5', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background 1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background 3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background 4', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background 5', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add heading text here', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Line', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Margin', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom Line', 'stackable-ultimate-gutenberg-blocks' );
__( 'Some short text that can be expanded to show more details.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show more', 'stackable-ultimate-gutenberg-blocks' );
__( 'Some long text that will be expanded when the show more button is clicked by the visitor.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show less', 'stackable-ultimate-gutenberg-blocks' );
__( 'Less text', 'stackable-ultimate-gutenberg-blocks' );
__( 'More text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Float', 'stackable-ultimate-gutenberg-blocks' );
__( 'Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Large Mid', 'stackable-ultimate-gutenberg-blocks' );
__( 'Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zizag', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zigzag', 'stackable-ultimate-gutenberg-blocks' );
__( 'Header Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Half Overlay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Center Overlay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Side Overlay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Half Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Half', 'stackable-ultimate-gutenberg-blocks' );
__( 'Snapping', 'stackable-ultimate-gutenberg-blocks' );
__( 'Snap to Start', 'stackable-ultimate-gutenberg-blocks' );
__( 'Snap to Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'No Snapping', 'stackable-ultimate-gutenberg-blocks' );
__( 'Item Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Left Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scrollbar', 'stackable-ultimate-gutenberg-blocks' );
__( 'Track Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Thumb Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Thumb Radius', 'stackable-ultimate-gutenberg-blocks' );
__( '123', 'stackable-ultimate-gutenberg-blocks' );
__( '456', 'stackable-ultimate-gutenberg-blocks' );
__( '789', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Box', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pill', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unordered List', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ordered List', 'stackable-ultimate-gutenberg-blocks' );
__( 'List', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grid', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number', 'stackable-ultimate-gutenberg-blocks' );
__( 'Padded Number', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lowercase Roman', 'stackable-ultimate-gutenberg-blocks' );
__( 'Uppercase Roman', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lowercase Letters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Uppercase Letters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dashed', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dotted', 'stackable-ultimate-gutenberg-blocks' );
__( 'List Item', 'stackable-ultimate-gutenberg-blocks' );
__( 'More noticeable when using wide layouts or list item borders', 'stackable-ultimate-gutenberg-blocks' );
__( 'List Display Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Indentation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icons & Numbers', 'stackable-ultimate-gutenberg-blocks' );
__( 'List Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'With Period', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Vertical Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'This is more visible if you have long text in your list.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Vertical Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon List Item Borders', 'stackable-ultimate-gutenberg-blocks' );
__( 'Borders', 'stackable-ultimate-gutenberg-blocks' );
__( 'Border Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Border Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 3', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 4', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 5', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line 6', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Text for This Block', 'Text placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Box', 'stackable-ultimate-gutenberg-blocks' );
__( 'Captioned', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Caption', 'stackable-ultimate-gutenberg-blocks' );
__( 'Caption', 'stackable-ultimate-gutenberg-blocks' );
__( 'Caption Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Map', 'stackable-ultimate-gutenberg-blocks' );
__( 'Embedded content from Google Map Platform.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Some map features require a Google API Key.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add API key here.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Location', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter an address or location', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enable Dragging', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full Screen Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Map Type Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Street View Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zoom Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Map Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Map Style (Paste JSON here)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn how to use Custom Map Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Map Marker', 'stackable-ultimate-gutenberg-blocks' );
__( 'Uploaded Icon and Icon Color settings are not fully compatible.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Icon Anchor Point', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Icon Anchor Point', 'stackable-ultimate-gutenberg-blocks' );
__( 'Map Example', 'Map Example Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Type in a pair of latitude longitude coordinates. You can also type in the name of the location if your API Key has Geocoding API and Places API enabled.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Silver', 'stackable-ultimate-gutenberg-blocks' );
__( 'Retro', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dark', 'stackable-ultimate-gutenberg-blocks' );
__( 'Night', 'stackable-ultimate-gutenberg-blocks' );
__( 'Aubergine', 'stackable-ultimate-gutenberg-blocks' );
__( 'Embedded content from Google Maps Platform.', 'stackable-ultimate-gutenberg-blocks' );
__( '01', 'stackable-ultimate-gutenberg-blocks' );
__( 'Notification Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Success', 'stackable-ultimate-gutenberg-blocks' );
__( 'Error', 'stackable-ultimate-gutenberg-blocks' );
__( 'Warning', 'stackable-ultimate-gutenberg-blocks' );
__( 'Information', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dismissible', 'stackable-ultimate-gutenberg-blocks' );
__( 'Side', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bordered', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outlined', 'stackable-ultimate-gutenberg-blocks' );
__( 'Large Icon', 'stackable-ultimate-gutenberg-blocks' );
__( '$', 'stackable-ultimate-gutenberg-blocks' );
__( '99', 'stackable-ultimate-gutenberg-blocks' );
__( '.00', 'stackable-ultimate-gutenberg-blocks' );
__( 'Posts', 'stackable-ultimate-gutenberg-blocks' );
__( 'No posts found.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Arrangement', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the order of the items displayed (category, title, meta, excerpt, read more button, image) for each post', 'stackable-ultimate-gutenberg-blocks' );
__( 'Query', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number of items', 'stackable-ultimate-gutenberg-blocks' );
__( 'Order by', 'stackable-ultimate-gutenberg-blocks' );
__( 'Newest to Oldest', 'stackable-ultimate-gutenberg-blocks' );
__( 'Oldest to Newest', 'stackable-ultimate-gutenberg-blocks' );
__( 'A → Z', 'stackable-ultimate-gutenberg-blocks' );
__( 'Z → A', 'stackable-ultimate-gutenberg-blocks' );
__( 'Last Modified to Oldest', 'stackable-ultimate-gutenberg-blocks' );
__( 'Oldest Modified to Last', 'stackable-ultimate-gutenberg-blocks' );
__( 'Menu Order', 'stackable-ultimate-gutenberg-blocks' );
__( 'Random', 'stackable-ultimate-gutenberg-blocks' );
__( 'Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Featured Image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Category', 'stackable-ultimate-gutenberg-blocks' );
__( 'Excerpt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Meta', 'stackable-ultimate-gutenberg-blocks' );
__( 'Read More Link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Apply hover effect when container is hovered', 'stackable-ultimate-gutenberg-blocks' );
__( 'Highlight Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Excerpt Length', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Author', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Comments', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default (Dot)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Space', 'stackable-ultimate-gutenberg-blocks' );
__( 'Comma', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dash', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pipe', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the separators between meta items (dot, space, comma, dash, pipe)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add post links to images', 'stackable-ultimate-gutenberg-blocks' );
__( 'Continue Reading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Read More Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'featured', 'stackable-ultimate-gutenberg-blocks' );
__( '(Untitled)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Card', 'stackable-ultimate-gutenberg-blocks' );
__( 'Portfolio', 'stackable-ultimate-gutenberg-blocks' );
__( 'Portfolio 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Card', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Card 2', 'stackable-ultimate-gutenberg-blocks' );
__( 'Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Package inclusion one', 'stackable-ultimate-gutenberg-blocks' );
__( 'Package inclusion two', 'stackable-ultimate-gutenberg-blocks' );
__( 'Package inclusion three', 'stackable-ultimate-gutenberg-blocks' );
__( 'one', 'stackable-ultimate-gutenberg-blocks' );
__( 'two', 'stackable-ultimate-gutenberg-blocks' );
__( 'three', 'stackable-ultimate-gutenberg-blocks' );
__( 'Package inclusion %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Compact', 'stackable-ultimate-gutenberg-blocks' );
__( 'Banner Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Banner', 'stackable-ultimate-gutenberg-blocks' );
__( 'Colored Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sectioned Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sectioned', 'stackable-ultimate-gutenberg-blocks' );
__( 'Spacer', 'stackable-ultimate-gutenberg-blocks' );
__( 'Type / to choose a block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle for this block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Classic', 'stackable-ultimate-gutenberg-blocks' );
__( 'Centered Pills', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Are you sure you want to delete this tab?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Move left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Move right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Duplicate tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Delete tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fixed Icon Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scrollable Tabs on Mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Active State', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change icons individually by clicking on each tab\'s icon.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Anchors', 'stackable-ultimate-gutenberg-blocks' );
__( "Assign unique anchor names to each tab so you'll be able to link directly and open each one.", 'stackable-ultimate-gutenberg-blocks' );
__( '%s Anchor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Anchor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unordered', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Include %s', '%s is a heading level, e.g. H1', 'stackable-ultimate-gutenberg-blocks' );
__( 'Auto-generate missing anchor ids', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scrolling', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use smooth scroll', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scroll Top Offset ', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Introduction', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Chapter 1: Abstract', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Chapter 2: History', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Chapter 3: Main Content', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Chapter 4: Additional Thoughts', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Conclusion', 'Table of Contents example text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Table of Contents', 'stackable-ultimate-gutenberg-blocks' );
__( 'Include heading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Exclude heading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Heading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab %d', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tabs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Initial Tab Open', 'stackable-ultimate-gutenberg-blocks' );
__( 'Equal tab height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Orientation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Panel Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tab Panel Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Immediate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bubble', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inverted Vertical', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text for this block. Use this space for describing your block. Any text will do. Text for this block. You can use this space for describing your block.', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Description for this block. Use this space for describing your block. Any text will do.', 'Content placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Timeline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Accent Anchor Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Succeeding timeline blocks will also use this value.', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Border Radius', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line Thickness', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Offset', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s #%d', 'option title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Timeline Accent Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Timeline Background Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Play Video', 'stackable-ultimate-gutenberg-blocks' );
__( 'Popup Option #1: Upload Video', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use .mp4 format for videos', 'stackable-ultimate-gutenberg-blocks' );
__( 'Popup Option #2: Video URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Paste a Youtube / Vimeo URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Allow fullscreen', 'stackable-ultimate-gutenberg-blocks' );
__( 'Allow download video', 'stackable-ultimate-gutenberg-blocks' );
__( 'Loop video', 'stackable-ultimate-gutenberg-blocks' );
__( 'Remove', 'stackable-ultimate-gutenberg-blocks' );
__( '#%s (no title)', 'stackable-ultimate-gutenberg-blocks' );
__( 'No posts found', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pick a post', 'stackable-ultimate-gutenberg-blocks' );
__( 'No matches found', 'stackable-ultimate-gutenberg-blocks' );
__( 'view demo', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Start', 'stackable-ultimate-gutenberg-blocks' );
__( 'Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'End', 'stackable-ultimate-gutenberg-blocks' );
__( 'Space Between', 'stackable-ultimate-gutenberg-blocks' );
__( 'Space Around', 'stackable-ultimate-gutenberg-blocks' );
__( 'Space Evenly', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stretch', 'stackable-ultimate-gutenberg-blocks' );
__( 'Baseline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unit', 'stackable-ultimate-gutenberg-blocks' );
__( 'Normal State', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hovered State', 'stackable-ultimate-gutenberg-blocks' );
__( 'Parent Container Hovered State', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapsed', 'stackable-ultimate-gutenberg-blocks' );
__( 'Parent Hovered', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add a Container Background to a parent block to enable this state.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Multiply', 'stackable-ultimate-gutenberg-blocks' );
__( 'Screen', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lighten', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Dodge', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Burn', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hard Light', 'stackable-ultimate-gutenberg-blocks' );
__( 'Soft Light', 'stackable-ultimate-gutenberg-blocks' );
__( 'Difference', 'stackable-ultimate-gutenberg-blocks' );
__( 'Exclusion', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hue', 'stackable-ultimate-gutenberg-blocks' );
__( 'Saturation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Luminosity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Initial', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inherit', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Mix Blend Mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Gradients', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Edit', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add Column', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjust the individual widths of each column', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Widths', 'stackable-ultimate-gutenberg-blocks' );
__( 'Group into Container', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ungroup from Container', 'stackable-ultimate-gutenberg-blocks' );
__( 'No designs found', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dynamic Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'System Fonts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Modern Font Stacks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Google Fonts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Theme Fonts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'auto', 'stackable-ultimate-gutenberg-blocks' );
__( 'Individual sides', 'stackable-ultimate-gutenberg-blocks' );
__( 'All sides', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top and Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Left and Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom Left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Paragraph', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stop showing tooltips', 'stackable-ultimate-gutenberg-blocks' );
__( 'Type to search icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Upload SVG', 'stackable-ultimate-gutenberg-blocks' );
__( 'Clear icon', 'stackable-ultimate-gutenberg-blocks' );
__( 'Drop your SVG here', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can type in custom HTML attributes for this block in the field above.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Example:', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Custom Attributes', 'stackable-ultimate-gutenberg-blocks' );
__( 'There is an error in your custom attribute', 'stackable-ultimate-gutenberg-blocks' );
__( 'Describe the purpose of the image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Leave empty if the image is purely decorative.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Alt Text (Alternative Text)', 'stackable-ultimate-gutenberg-blocks' );
__( 'preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Upload', 'stackable-ultimate-gutenberg-blocks' );
__( 'Replace', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blur', 'stackable-ultimate-gutenberg-blocks' );
__( 'Brightness', 'stackable-ultimate-gutenberg-blocks' );
__( 'Contrast', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grayscale', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hue Rotate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Invert', 'stackable-ultimate-gutenberg-blocks' );
__( 'Saturate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sepia', 'stackable-ultimate-gutenberg-blocks' );
__( 'Circle', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blob %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Click on any inner block in the editor to style it.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Back', 'stackable-ultimate-gutenberg-blocks' );
__( 'Duplicate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Delete', 'stackable-ultimate-gutenberg-blocks' );
__( 'Looking for other settings? They\'ve moved to the %s tab.', 'stackable-ultimate-gutenberg-blocks' );
__( 'style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dismiss hint', 'stackable-ultimate-gutenberg-blocks' );
__( 'layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'UI Kits', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wireframes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Latest Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'V2 Design Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'E.g. light, dark, red, minimalist…', 'stackable-ultimate-gutenberg-blocks' );
__( 'Select Multiple', 'stackable-ultimate-gutenberg-blocks' );
__( 'Select', 'stackable-ultimate-gutenberg-blocks' );
__( 'Deselect All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Refresh Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Large preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Medium preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Small preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pick a design to start from, this will override your block settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layout Tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Style Tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Advanced', 'stackable-ultimate-gutenberg-blocks' );
__( 'Advanced Tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Get More Image Shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Mask images with a variety of blob-like shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Choose from over 50 different shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enhances the overall aesthetic of images', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use Dynamic Content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add dynamic content from posts or post meta', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use third-party plugins as dynamic sources such as ACF, Meta Box, Toolset, and more', 'stackable-ultimate-gutenberg-blocks' );
__( 'Build custom loop design with the Native Query Loop', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator Layers', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add a second and third layer to separators', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change layer color, size and opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Greater creativity in designing separators', 'stackable-ultimate-gutenberg-blocks' );
__( 'Elevate Your Icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Liven up icons with gradient fills, multiple colors and background shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'More design options and customization for icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Choose from over 50 background shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Greater visual interest and variety for your icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Liven up icons with background shapes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjust timing of CSS transitions', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change X and Y position of blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scale or rotate blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Perfect for hover animations', 'stackable-ultimate-gutenberg-blocks' );
__( 'Entrance fade-ins and animations when scrolling to blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Smooth scroll animations based on scrolling position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Create a more visually engaging and interactive experience', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show or hide blocks based on conditions', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display blocks based on time, role, meta, custom PHP, option, taxonomy and more', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use multiple conditions', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show targeted content and personalization', 'stackable-ultimate-gutenberg-blocks' );
__( 'Greater control over the visibility of content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add custom CSS rules specific for each block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Support for media queries', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fine-tune styling on a per block basis', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjust Column Arrangement', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjust the arrangement of columns when collapsed on tablet and mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ensure that content remains organized and easily readable on mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'This is a Premium Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unlock access to the entire design library and set your website apart from the rest.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Get More Post Options', 'stackable-ultimate-gutenberg-blocks' );
__( 'New Custom Post Type option', 'stackable-ultimate-gutenberg-blocks' );
__( 'Offset, exclude, include specific posts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide the current post - great for synced patterns', 'stackable-ultimate-gutenberg-blocks' );
__( 'Unlock Your Icon Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add your custom SVG icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Easily access your custom icons in the icon picker', 'stackable-ultimate-gutenberg-blocks' );
__( 'Organize your custom icons in your library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Premium Typography', 'stackable-ultimate-gutenberg-blocks' );
__( 'Access to 90+ curated font pairs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Create your own custom font pairs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Premium Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Access to 50+ curated color scheme presets', 'stackable-ultimate-gutenberg-blocks' );
__( 'Create your own color schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set default color schemes for blocks and sections', 'stackable-ultimate-gutenberg-blocks' );
__( 'Streamline your design workflow', 'stackable-ultimate-gutenberg-blocks' );
__( 'This Is a Premium Feature', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn More', 'stackable-ultimate-gutenberg-blocks' );
__( 'View Demo', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can hide premium hints in the settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default Heading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default Body', 'stackable-ultimate-gutenberg-blocks' );
__( 'Desktop', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tablet', 'stackable-ultimate-gutenberg-blocks' );
__( 'Mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'There are no blocks in this group, please add one.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Inset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow Spread', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Advanced Shadow Options', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shadow/Outline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the intensity of the shadow/outline of the block and the appearance of the block border', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Arrangement', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Filter by Taxonomy', 'stackable-ultimate-gutenberg-blocks' );
__( 'Taxonomy Filter Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Included In', 'stackable-ultimate-gutenberg-blocks' );
__( 'Not In', 'stackable-ultimate-gutenberg-blocks' );
__( 'URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Opens in new tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Nofollow link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sponsored', 'stackable-ultimate-gutenberg-blocks' );
__( 'UGC', 'stackable-ultimate-gutenberg-blocks' );
__( 'Search or type url', 'stackable-ultimate-gutenberg-blocks' );
__( 'Upgrade to Premium to get more design variations.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset layout', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Library', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set global styles and settings for your Stackable blocks to create a consistent design across your site. All the settings below will apply globally.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Error saving block, please make sure you only have a %s block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default %s Block saved', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Block style saved', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default %s Block Saved!', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save as Default %s Block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default %s Block Deleted!', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset Default %s Block', 'stackable-ultimate-gutenberg-blocks' );
__( "Couldn't update block styles, got this error:", 'stackable-ultimate-gutenberg-blocks' );
__( 'Please refresh the page and try again.', 'stackable-ultimate-gutenberg-blocks' );
__( 'The page will now reload for the old blocks to load. Please reload it if it does not refresh automatically.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable V2 Block Detected', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hello! 👋', 'stackable-ultimate-gutenberg-blocks' );
__( 'We noticed that the page you are editing contains old Stackable version 2 blocks. Starting Stackable version 3, old v2 blocks are not anymore loaded in the editor. This means that you would not be able to edit the old v2 blocks in the editor and any old v2 block would show a block error in the editor.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Do you want to enable backward compatibility, so the old blocks will be loaded in the editor?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Yes, load V2 Blocks in the editor, but only when V2 blocks are present already', 'stackable-ultimate-gutenberg-blocks' );
__( 'Yes, always load V2 blocks in the editor', 'stackable-ultimate-gutenberg-blocks' );
__( 'No, don\'t load V2 blocks in the editor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color & Highlight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Highlight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Low', 'stackable-ultimate-gutenberg-blocks' );
__( 'Highlight Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Facebook', 'stackable-ultimate-gutenberg-blocks' );
__( 'Twitter', 'stackable-ultimate-gutenberg-blocks' );
__( 'Instagram', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pinterest', 'stackable-ultimate-gutenberg-blocks' );
__( 'LinkedIn', 'stackable-ultimate-gutenberg-blocks' );
__( 'YouTube', 'stackable-ultimate-gutenberg-blocks' );
__( 'Email', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image or Video', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image Url', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Media Tint Strength', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the intensity of the background media tint', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Video Thumbnail', 'stackable-ultimate-gutenberg-blocks' );
__( 'Note: Fixed Background works on Desktop and Android devices only.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fixed Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Keeps the background image fixed in place while scrolling', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adv. Background Image Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Top Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'Center Left', 'stackable-ultimate-gutenberg-blocks' );
__( 'Center Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'Center Right', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bottom Center', 'stackable-ultimate-gutenberg-blocks' );
__( 'Chooses which part of the background image will be the focal point', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Repeat', 'stackable-ultimate-gutenberg-blocks' );
__( 'No-Repeat', 'stackable-ultimate-gutenberg-blocks' );
__( 'Repeat', 'stackable-ultimate-gutenberg-blocks' );
__( 'Repeat-X', 'stackable-ultimate-gutenberg-blocks' );
__( 'Repeat-Y', 'stackable-ultimate-gutenberg-blocks' );
__( 'Covers the background with tiled images', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the display image size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Blend Mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the radius of block corners to make them more rounded', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link / URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open in new tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open Link in Lightbox', 'stackable-ultimate-gutenberg-blocks' );
__( 'Displaying a Google Map in a Lightbox? Use the embed iframe URL instead. Need help finding it?', 'stackable-ultimate-gutenberg-blocks' );
__( ' Check out our docs.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link rel', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link relationship keywords, e.g. nofollow noreferrer prefetch', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Also used for lightbox caption', 'stackable-ultimate-gutenberg-blocks' );
__( 'Anchor ID', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add an id attribute to the anchor tag.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Supports links to images, videos, YouTube, Vimeo, and web pages that allow embedding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Min. Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Max. Content Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Vertical Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the minimum allowable height of the block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the placement of the content to top, center or bottom. Available when the min. block height is set to higher than default.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the maximum allowable width of the block. The settings will depend on the block width you set on the toolbar.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the placement of the content to left, center or right. Not available when block width is set to full width.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Paddings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Margins', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the block paddings, i.e the space between the inner columns and the block border', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the block margin, i.e. the space outside the block between the block border and the next block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Frontend JS & CSS Files', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load across entire site', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load only in posts with Stackable blocks', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Accordion', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Advanced Text', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Advanced Heading', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Blockquote', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Blog Posts', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Button', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Call to Action', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Card', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Columns', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Container', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Count Up', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Design Library', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Divider', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Expand / Show More', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Feature Grid', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Feature', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Header', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Icon', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Icon List', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Image Box', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Notification', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Number Box', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Pricing Box', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Separator', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Spacer', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Team Member', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Testimonial', 'block title', 'stackable-ultimate-gutenberg-blocks' );
_x( 'Video Popup', 'block title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Defaults', 'stackable-ultimate-gutenberg-blocks' );
__( 'Manage how Stackable blocks look when they\'re inserted.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Block Defaults', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Buttons & Icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover States', 'stackable-ultimate-gutenberg-blocks' );
__( 'When editing block layouts in the hover states, select a block to view the applied styles.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Globally style buttons and icons across all our blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Global Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Are you sure you want to reset all %s styles to their default values?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset All', 'stackable-ultimate-gutenberg-blocks' );
__( 'Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to Button Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Ghost Border Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Additional settings that apply to Icon Button Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( ' Button Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon List', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to Icon List Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icons', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to Icon Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'The %s cannot be changed in any hover state when using a gradient.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Changing the %s is not allowed for %s state.', 'stackable-ultimate-gutenberg-blocks' );
__( 'parent-hover', 'stackable-ultimate-gutenberg-blocks' );
__( 'Heading Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'hover', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Accent Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Text Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Outline Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Are you sure you want to reset this color scheme to their default values?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Do you want to duplicate this color scheme?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Editing %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change the color scheme used for all blocks, and when the container option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change the color scheme applied when the background option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Editing this scheme will also change all blocks that currently use this color scheme.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Scheme Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Scheme Presets', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Scheme Colors in Color Pickers', 'stackable-ultimate-gutenberg-blocks' );
__( 'Note: This background color is used when the container option of the block is enabled.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Note: Background color is not used for Base Color Scheme.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'When editing color schemes in the hover states, select a block to view the applied colors.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color schemes are applied to all blocks and sections of your entire website.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Global Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Global Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Any blocks that use this color will become unlinked with this global color. Delete this color?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Color %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Color Palette', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change your color palette for all your blocks across your site.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Global Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Theme Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Default Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Site Editor Custom Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Spacing & Borders', 'stackable-ultimate-gutenberg-blocks' );
__( 'Globally style spacings and borders across all our blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to all our Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Margin Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets the block margin bottom, i.e. the space outside the block between the block border and the next block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to Inner Column blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( ' Inner Column Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to blocks that have the "Background" option enabled in the Style Tab.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Containers', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to blocks that have the "Container" option enabled in the Layout Tab.', 'stackable-ultimate-gutenberg-blocks' );
__( 'These styles are applied to all images in Stackable Blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Heading %d', 'stackable-ultimate-gutenberg-blocks' );
__( 'Body Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle', 'stackable-ultimate-gutenberg-blocks' );
__( "To apply this typography style, just add `%s` in your block\'s Additional CSS classes. Also make sure that `%s` tag is set to avoid conflict with other typography styles", 'stackable-ultimate-gutenberg-blocks' );
__( 'Picking a new font pair will overwrite the existing typography settings. Are you sure?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Global Typography', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change the typography of your headings for all your blocks in your site.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Global Typography', 'stackable-ultimate-gutenberg-blocks' );
__( 'Apply Typography Styles to', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable and native blocks only', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable blocks only', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable and all other blocks', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reset %s Global Typography Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Resetting this typography style will revert all typography to its original style. Proceed?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Paddings', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s #%d', 'Panel title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Color Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Color Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Open link in new tab', 'stackable-ultimate-gutenberg-blocks' );
__( 'Basic', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use social colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change to Normal Button on Hover', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tiny', 'stackable-ultimate-gutenberg-blocks' );
__( 'Small', 'stackable-ultimate-gutenberg-blocks' );
__( 'Medium', 'stackable-ultimate-gutenberg-blocks' );
__( 'Large', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adv. Icon Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Free', 'stackable-ultimate-gutenberg-blocks' );
__( 'Premium', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shaped', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Color Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Multicolor', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Shape / Outline Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Shape Border Radius', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline Border Radius', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Shape Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline Padding', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image Repeat', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Image Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Force square image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blend Mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'All Block Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Free Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Premium Designs', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s Link', 'name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Corporate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lined to Colored', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reverse arrow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Closed State Background Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Title Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Arrow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Border', 'stackable-ultimate-gutenberg-blocks' );
__( 'Spread', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grouped', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapse Buttons On', 'stackable-ultimate-gutenberg-blocks' );
__( 'Don\'t collapse', 'stackable-ultimate-gutenberg-blocks' );
__( 'Quotation Mark', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Card', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Order', 'stackable-ultimate-gutenberg-blocks' );
__( 'Posts Settings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Read More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pagination', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Customize Read More Link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load More Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Continue reading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next »', 'stackable-ultimate-gutenberg-blocks' );
__( '« Previous', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description', 'stackable-ultimate-gutenberg-blocks' );
__( 'Description Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle for this block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link Hover Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'The colors above might not apply to some nested blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Uneven', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tiled', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layouts', 'stackable-ultimate-gutenberg-blocks' );
__( 'No Paddings', 'stackable-ultimate-gutenberg-blocks' );
__( 'Half-screen height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full-screen height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Vertical Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Short', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tall', 'stackable-ultimate-gutenberg-blocks' );
__( 'Restrict to Content Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Abstract', 'stackable-ultimate-gutenberg-blocks' );
__( 'Boxed', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Margin', 'stackable-ultimate-gutenberg-blocks' );
__( 'Half Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Shape %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlap Background %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Column Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reverse Horizontally', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Offset', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'This affects other buttons in this block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display as a grid (left to right & evenly spaced)', 'stackable-ultimate-gutenberg-blocks' );
__( 'List Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can click on each icon in the Icon List block to change them individually.', 'stackable-ultimate-gutenberg-blocks' );
__( 'List Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text for this block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Line %d', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number of Icons / Columns', 'stackable-ultimate-gutenberg-blocks' );
__( 'Title on Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Color Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Hover Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Effects', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Hover Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zoom In', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zoom Out', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s %d %s', 'Nth Title with description', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number Shape', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Size', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number Background Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Border Thickness', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Location', 'stackable-ultimate-gutenberg-blocks' );
__( 'Price', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sub Price', 'stackable-ultimate-gutenberg-blocks' );
__( 'Price Prefix', 'stackable-ultimate-gutenberg-blocks' );
__( 'Price Suffix', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Padding Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Padding Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Margin Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use this to pull up/down the separator to the block above it', 'stackable-ultimate-gutenberg-blocks' );
__( 'Margin Bottom', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use this to pull up/down the separator to the block below it', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Inverse', 'stackable-ultimate-gutenberg-blocks' );
__( 'Testimonial', 'stackable-ultimate-gutenberg-blocks' );
__( 'Testimonial Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Name Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Side Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reverse Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Rule', 'stackable-ultimate-gutenberg-blocks' );
__( 'Vertical Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Subtitle on Top', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Play Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Button Style', 'stackable-ultimate-gutenberg-blocks' );
__( 'Normal Play Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Play Button with Circle', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline Play Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Overlay Simple', 'stackable-ultimate-gutenberg-blocks' );
__( 'Social', 'stackable-ultimate-gutenberg-blocks' );
__( 'Social Button Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Position Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pick a layout or design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Select a variation to start with.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Or pick from our Design Library.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Select a design from our library to start with.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Skip', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Spacing & More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Min. Block Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Margins', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Paddings', 'stackable-ultimate-gutenberg-blocks' );
__( 'You will not lose your block content when changing designs.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Align center', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wide width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change Alignment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link / URL #%d', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link %d Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Max Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Horizontal Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text Align', 'stackable-ultimate-gutenberg-blocks' );
__( 'Block Description', 'stackable-ultimate-gutenberg-blocks' );
__( 'Check', 'stackable-ultimate-gutenberg-blocks' );
__( 'Cross', 'stackable-ultimate-gutenberg-blocks' );
__( 'Star', 'stackable-ultimate-gutenberg-blocks' );
__( 'Login Status', 'stackable-ultimate-gutenberg-blocks' );
__( 'Role', 'stackable-ultimate-gutenberg-blocks' );
__( 'Date & Time', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom PHP', 'stackable-ultimate-gutenberg-blocks' );
__( 'Conditional Tag', 'stackable-ultimate-gutenberg-blocks' );
__( 'Query String', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Meta', 'stackable-ultimate-gutenberg-blocks' );
__( 'Site Option', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post IDs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Taxonomy', 'stackable-ultimate-gutenberg-blocks' );
__( 'WooCommerce', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Conditional Tag', 'stackable-ultimate-gutenberg-blocks' );
__( 'Home', 'stackable-ultimate-gutenberg-blocks' );
__( 'Front Page', 'stackable-ultimate-gutenberg-blocks' );
__( '404 Not Found Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Single Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Attachment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Any Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Privacy Policy Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Any Archive Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Category Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tag Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Taxonomy Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Date Archive Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Yearly Archive Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Search Result Page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Trackback', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dynamic Sidebar', 'stackable-ultimate-gutenberg-blocks' );
__( 'RTL Reading', 'stackable-ultimate-gutenberg-blocks' );
__( 'Multisite', 'stackable-ultimate-gutenberg-blocks' );
__( 'Main Site', 'stackable-ultimate-gutenberg-blocks' );
__( 'Child Theme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Customize Preview', 'stackable-ultimate-gutenberg-blocks' );
__( 'Multi-author Site', 'stackable-ultimate-gutenberg-blocks' );
__( 'Feed', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sticky Post', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hierarchical Post Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Archive Post Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Comments Open', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pings Open', 'stackable-ultimate-gutenberg-blocks' );
__( 'Has Excerpt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Has Post Thumbnail', 'stackable-ultimate-gutenberg-blocks' );
__( 'Has Tags', 'stackable-ultimate-gutenberg-blocks' );
__( 'Has Terms', 'stackable-ultimate-gutenberg-blocks' );
__( 'Has Primary Nav Menu', 'stackable-ultimate-gutenberg-blocks' );
__( 'The Custom PHP allows you to configure the block’s visibility based on the expression entered. If the expression evaluates to true, the block will be displayed.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sample PHP code:', 'stackable-ultimate-gutenberg-blocks' );
__( 'If a syntax error is present, check your PHP code', 'stackable-ultimate-gutenberg-blocks' );
__( 'Now', 'stackable-ultimate-gutenberg-blocks' );
__( 'Never', 'stackable-ultimate-gutenberg-blocks' );
__( 'Days of the Week', 'stackable-ultimate-gutenberg-blocks' );
__( 'If set, the block will be displayed / hidden on selected days.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sunday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Monday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tuesday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Wednesday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Thursday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Friday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Saturday', 'stackable-ultimate-gutenberg-blocks' );
__( 'Logged-In Users', 'stackable-ultimate-gutenberg-blocks' );
__( 'Logged-Out Users', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Post IDs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Meta Key', 'stackable-ultimate-gutenberg-blocks' );
__( 'Operator', 'stackable-ultimate-gutenberg-blocks' );
__( 'True', 'stackable-ultimate-gutenberg-blocks' );
__( 'False', 'stackable-ultimate-gutenberg-blocks' );
__( 'Equal', 'stackable-ultimate-gutenberg-blocks' );
__( 'Not Equal', 'stackable-ultimate-gutenberg-blocks' );
__( 'Less Than', 'stackable-ultimate-gutenberg-blocks' );
__( 'Less Than & Equal To', 'stackable-ultimate-gutenberg-blocks' );
__( 'Greater Than', 'stackable-ultimate-gutenberg-blocks' );
__( 'Greater Than & Equal To', 'stackable-ultimate-gutenberg-blocks' );
__( 'Contains', 'stackable-ultimate-gutenberg-blocks' );
__( 'Does Not Contain', 'stackable-ultimate-gutenberg-blocks' );
__( 'Regular Expression', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Value', 'stackable-ultimate-gutenberg-blocks' );
__( 'Value to compare with the post meta value.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Post Types', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Queries', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter one query string per line. The block will be displayed / hidden if any of the query strings match.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Enter Role', 'stackable-ultimate-gutenberg-blocks' );
__( 'Option Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Value to compare with the option value.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Current Post', 'stackable-ultimate-gutenberg-blocks' );
__( 'Choose Product', 'stackable-ultimate-gutenberg-blocks' );
__( 'Property', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sales', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stock Quantity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Downloadable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Featured', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is in Stock', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is on Backorder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is on Sale', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Purchasable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Shipping Taxable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Sold Individually', 'stackable-ultimate-gutenberg-blocks' );
__( 'Is Taxable', 'stackable-ultimate-gutenberg-blocks' );
__( 'Value', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Placeholder', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full Access', 'stackable-ultimate-gutenberg-blocks' );
__( 'Manager', 'stackable-ultimate-gutenberg-blocks' );
__( 'No Access', 'stackable-ultimate-gutenberg-blocks' );
__( 'Full editing mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content only editing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Let me enter my Font Awesome Pro Kit code', 'stackable-ultimate-gutenberg-blocks' );
__( 'Don\'t show me this again', 'stackable-ultimate-gutenberg-blocks' );
__( 'Paste your Kit code %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Verify', 'stackable-ultimate-gutenberg-blocks' );
__( 'Please make sure you have Pro icons selected in your kit. Edit your kit settings at: ', 'stackable-ultimate-gutenberg-blocks' );
__( 'Click here to check again', 'stackable-ultimate-gutenberg-blocks' );
__( 'Please enter a valid Font Awesome Pro Kit code.', 'stackable-ultimate-gutenberg-blocks' );
__( 'If you have Font Awesome Pro, you can use your Pro icons by inputting your Pro Kit code here.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Need help? Read our guide.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Delete Condition', 'stackable-ultimate-gutenberg-blocks' );
__( 'Deleting will remove this condition for the block. Proceed?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Condition Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Visibility', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show on condition match', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide on condition match', 'stackable-ultimate-gutenberg-blocks' );
__( 'No conditions yet. Add your first condition.', 'stackable-ultimate-gutenberg-blocks' );
__( 'OR', 'stackable-ultimate-gutenberg-blocks' ); // translators: This is the separator between conditions: OR / AND.
__( 'AND', 'stackable-ultimate-gutenberg-blocks' ); // translators: This is the separator between conditions: OR / AND.
__( 'Trigger if ANY condition matches', 'stackable-ultimate-gutenberg-blocks' );
__( 'Trigger if ALL conditions match', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add New', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add New Condition', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Conditional Display', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can use this area to further customize your block. Any custom CSS added here will only affect this block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Media queries are supported. Use the widths 1024px and 768px for tablet and mobile breakpoints.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Rotate', 'stackable-ultimate-gutenberg-blocks' );
__( 'Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Entrance Animation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scroll Animation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Start Position', 'stackable-ultimate-gutenberg-blocks' );
__( 'Entrance Animation Speed', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fast', 'stackable-ultimate-gutenberg-blocks' );
__( 'Entrance Animation Delay', 'stackable-ultimate-gutenberg-blocks' );
__( 'Smoothen Scroll Animation', 'stackable-ultimate-gutenberg-blocks' );
__( 'Use 3D Transforms', 'stackable-ultimate-gutenberg-blocks' );
__( 'Perspective', 'stackable-ultimate-gutenberg-blocks' );
__( 'Exit Animation', 'stackable-ultimate-gutenberg-blocks' );
__( 'This block has Transforms assigned to it, Motion Effects may not work as expected.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about Motion Effects', 'stackable-ultimate-gutenberg-blocks' );
__( 'Translate X', 'stackable-ultimate-gutenberg-blocks' );
__( 'TranslateY', 'stackable-ultimate-gutenberg-blocks' );
__( 'TranslateZ', 'stackable-ultimate-gutenberg-blocks' );
__( 'RotateX', 'stackable-ultimate-gutenberg-blocks' );
__( 'RotateY', 'stackable-ultimate-gutenberg-blocks' );
__( 'Skew X', 'stackable-ultimate-gutenberg-blocks' );
__( 'Skew Y', 'stackable-ultimate-gutenberg-blocks' );
__( 'Separator Layer %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer Blend mode', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sets how the sepator layer is blended into the background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transition Duration (secs)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transition Function', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease Out', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Out', 'stackable-ultimate-gutenberg-blocks' );
__( 'Linear', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Quad', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease Out Quad', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Out Quad', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Expo', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease Out Expo', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Out Expo', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Back', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease Out Back', 'stackable-ultimate-gutenberg-blocks' );
__( 'Ease In Out Back', 'stackable-ultimate-gutenberg-blocks' );
__( 'Transform Origin', 'stackable-ultimate-gutenberg-blocks' );
__( 'This block has Motion Effects assigned to it, applying transforms above may prevent the Motion Effects from working as expected.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Translate Y', 'stackable-ultimate-gutenberg-blocks' );
__( 'Shape Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Color #%s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Multicolor only works for custom uploaded icons that have multiple path elements or for Font Awesome Pro Duotones.', 'stackable-ultimate-gutenberg-blocks' );
__( 'URL Query String', 'stackable-ultimate-gutenberg-blocks' );
__( 'This is the string appended to the URL when changing pages.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show Next & Previous Button', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous Label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Page %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next page', 'stackable-ultimate-gutenberg-blocks' );
__( 'Offset your posts by a specific number of items.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Exclude Post IDs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Excludes specific IDs from the display. Enter post IDs separated by a commas', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hide the current post', 'stackable-ultimate-gutenberg-blocks' );
__( 'Removes the current post from the posts list', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display Specific Post IDs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Limit display to only these specific IDs. Enter post IDs separated by a commas', 'stackable-ultimate-gutenberg-blocks' );
__( 'Sale', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adjusts the transparency of the separator layer', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number of Items', 'stackable-ultimate-gutenberg-blocks' );
__( 'No saved designs yet', 'stackable-ultimate-gutenberg-blocks' );
__( 'Click here to save your block\'s design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Error Getting Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Click here to retry fetching your saved designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save as new block design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Manage saved designs', 'stackable-ultimate-gutenberg-blocks' );
__( '(default)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Favorite', 'stackable-ultimate-gutenberg-blocks' );
__( 'Saved Block Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save designs to reuse them across your site. Note that using saved designs will override your current block settings.', 'stackable-ultimate-gutenberg-blocks' );
__( 'You have unsaved changes, discard them?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Manage Saved Designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'Design Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set as a favorite design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Design name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set as default block design', 'stackable-ultimate-gutenberg-blocks' );
__( 'My Block Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save as New Block Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Set as favorite', 'stackable-ultimate-gutenberg-blocks' );
__( 'Place at the top of the list of saved designs', 'stackable-ultimate-gutenberg-blocks' );
__( 'New blocks created will use this design automatically', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add New Design', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more how multicolor works', 'stackable-ultimate-gutenberg-blocks' );
__( 'here', 'stackable-ultimate-gutenberg-blocks' );
__( 'Outline Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Icon Shape Color', 'stackable-ultimate-gutenberg-blocks' );
__( 'Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'Attributes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Delimiter', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show as link', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Text', 'stackable-ultimate-gutenberg-blocks' );
__( 'Date Format', 'stackable-ultimate-gutenberg-blocks' );
__( 'Custom Format', 'stackable-ultimate-gutenberg-blocks' );
__( 'Change the date format of your dynamic content.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn more about date formats', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dynamic Source', 'stackable-ultimate-gutenberg-blocks' );
__( 'Field', 'stackable-ultimate-gutenberg-blocks' );
__( 'Learn how to use Dynamic Content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Apply', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add Custom Format', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Format', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add your custom format by adding %s.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Thumbnail', 'stackable-ultimate-gutenberg-blocks' );
__( 'Image Quality', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text Field', 'stackable-ultimate-gutenberg-blocks' );
__( 'Taxonomy Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display Option', 'stackable-ultimate-gutenberg-blocks' );
__( 'All values', 'stackable-ultimate-gutenberg-blocks' );
__( '%s value', 'stackable-ultimate-gutenberg-blocks' );
__( '1st', 'stackable-ultimate-gutenberg-blocks' );
__( '2nd', 'stackable-ultimate-gutenberg-blocks' );
__( '3rd', 'stackable-ultimate-gutenberg-blocks' );
__( '4th', 'stackable-ultimate-gutenberg-blocks' );
__( '5th', 'stackable-ultimate-gutenberg-blocks' );
__( '6th', 'stackable-ultimate-gutenberg-blocks' );
__( '7th', 'stackable-ultimate-gutenberg-blocks' );
__( '8th', 'stackable-ultimate-gutenberg-blocks' );
__( '9th', 'stackable-ultimate-gutenberg-blocks' );
__( '10th', 'stackable-ultimate-gutenberg-blocks' );
__( 'Strip HTML tags', 'stackable-ultimate-gutenberg-blocks' );
__( 'If this option is false, the content rendered in the editor will still be stripped to prevent an error from occuring', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display text when true', 'stackable-ultimate-gutenberg-blocks' );
__( 'Display text when false', 'stackable-ultimate-gutenberg-blocks' );
__( 'Remove the selected block(s).', 'stackable-ultimate-gutenberg-blocks' );
__( 'Content Editing mode is enabled', 'stackable-ultimate-gutenberg-blocks' );
__( 'To unlock Full Editing mode, please contact your administrator.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Style copied successfully!', 'stackable-ultimate-gutenberg-blocks' );
__( 'Style pasted successfully!', 'stackable-ultimate-gutenberg-blocks' );
__( 'Used to copy core/stackable block styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Used to paste core/stackable block styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adv Copy Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Adv Paste Styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Copy & paste styles', 'stackable-ultimate-gutenberg-blocks' );
__( 'Field cannot be empty', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slug contains invalid characters', 'stackable-ultimate-gutenberg-blocks' );
__( 'Slug must be unique', 'stackable-ultimate-gutenberg-blocks' );
__( 'https://', 'stackable-ultimate-gutenberg-blocks' );
__( 'Link title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Do you want to delete this field?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add New Field', 'stackable-ultimate-gutenberg-blocks' );
__( 'Field Type', 'stackable-ultimate-gutenberg-blocks' );
__( 'Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Time', 'stackable-ultimate-gutenberg-blocks' );
__( 'Url', 'stackable-ultimate-gutenberg-blocks' );
__( 'Field Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Field Slug', 'stackable-ultimate-gutenberg-blocks' );
__( 'A unique string that will be used to identify this field. Must contain only letters, numbers, underscores and dashes.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save field', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add a custom field now to start exploring the possibilities.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Add field', 'stackable-ultimate-gutenberg-blocks' );
__( 'You don\'t have any custom fields.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Save changes', 'stackable-ultimate-gutenberg-blocks' );
__( 'You have unsaved changes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Error in saving content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Dynamic Content', 'stackable-ultimate-gutenberg-blocks' );
__( 'Are you sure you want to delete this font pair preset?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Editing Font Pair', 'stackable-ultimate-gutenberg-blocks' );
__( 'Delete this icon?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Upload SVG icons to your library to use them in your blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Do you also want to add this icon to your icon library for future use?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default Block Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Base Color Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Default color scheme to use for all blocks when no special options are enabled.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Background Mode Color Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Colors applied when the background option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Container Mode Color Scheme', 'stackable-ultimate-gutenberg-blocks' );
__( 'Colors applied when the container option is enabled for a block.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color Scheme %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'You have duplicated the color scheme. You are now editing the new one.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Deleting this color scheme would remove all colors linked to it. Any blocks that use this color scheme will revert to the default scheme. Delete this color scheme?', 'stackable-ultimate-gutenberg-blocks' );
__( 'Offset your posts by a specific number of items', 'stackable-ultimate-gutenberg-blocks' );
__( 'Show previous and next buttons', 'stackable-ultimate-gutenberg-blocks' );
__( 'Previous label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Next label', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover & Active Opacity', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hover & Active Colors', 'stackable-ultimate-gutenberg-blocks' );
__( 'Reverse columns', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapsed Row Gap', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapsed Col. Arrangement', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapse image on Mobile', 'stackable-ultimate-gutenberg-blocks' );
__( 'Collapse image height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Override settings for column %d', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Tilt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Zoom & Tilt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Up', 'stackable-ultimate-gutenberg-blocks' );
__( 'Down', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blur In', 'stackable-ultimate-gutenberg-blocks' );
__( 'Blur Out', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grayscale In', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grayscale Out', 'stackable-ultimate-gutenberg-blocks' );
__( 'Box Hover Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Color on Hover', 'stackable-ultimate-gutenberg-blocks' );
__( 'Bubble Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Number Background', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column / Container Spacing', 'stackable-ultimate-gutenberg-blocks' );
__( 'Min. Column Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Column Header', 'stackable-ultimate-gutenberg-blocks' );
_x( '%s #%d', 'Nth Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Layer Color', 'stackable-ultimate-gutenberg-blocks' );
__( '%s More', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lift w/ shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Staggered lift', 'stackable-ultimate-gutenberg-blocks' );
__( 'Staggered lift w/ shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Scale w/ shadow', 'stackable-ultimate-gutenberg-blocks' );
__( 'Lower', 'stackable-ultimate-gutenberg-blocks' );
__( 'Grayscale Hover Effect', 'stackable-ultimate-gutenberg-blocks' );
__( 'Settings that control custom fields functionality and permissions.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Roles which only allow content only editing.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hold on! We noticed that you\'re using the Font Awesome plugin and that you\'re using a free Kit. If you have a FontAwesome Pro subscription, you can just set your Kit to use Pro Icons, and you should be able to use your Pro Icons inside your Stackable blocks. %sLearn more about this here.%s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Hold on! We noticed that you\'re using the Font Awesome plugin and that you\'re using the free CDN. If you have a FontAwesome Pro subscription, you can just set your CDN to use Pro Icons, and you should be able to use your Pro Icons inside your Stackable blocks. %sLearn more about this here.%s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Good news! We noticed that you\'re using the Font Awesome plugin. Your Font Awesome Pro icons are already available inside your Stackable blocks.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Make sure you need to add your WordPress site to the %sallowed domains for your CDN%s.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Load more button for your Stackable Posts block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Pagination for your Stackable Posts block', 'stackable-ultimate-gutenberg-blocks' );
__( 'Invalid parameters. Please try again.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Don\'t show Font Awesome plugin settings error', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font Awesome Kit ID', 'stackable-ultimate-gutenberg-blocks' );
__( 'Font Awesome icon version to server', 'stackable-ultimate-gutenberg-blocks' );
__( '%s must be a string.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Security error, please refresh the page and try again.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Invalid arguments.', 'stackable-ultimate-gutenberg-blocks' );
__( 'ACF' , 'stackable-ultimate-gutenberg-blocks' );
__( 'The field type provided is not valid.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text input is empty', 'stackable-ultimate-gutenberg-blocks' );
__( '`whenTrueText` and `whenFalseText` arguments are required.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable Custom Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'JetEngine' , 'stackable-ultimate-gutenberg-blocks' );
__( 'Latest Post', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Latest %s', 'stackable-ultimate-gutenberg-blocks' ); // translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
__( 'Nth', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post', 'stackable-ultimate-gutenberg-blocks' );
__( '%s Latest %s', 'stackable-ultimate-gutenberg-blocks' );
__( 'Meta Box' , 'stackable-ultimate-gutenberg-blocks' );
__( 'Meta Box | ' . $title , 'stackable-ultimate-gutenberg-blocks' );
__( 'Multiple select not supported in this field.', 'stackable-ultimate-gutenberg-blocks' );
__( 'You have not selected an image.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Text fields are empty', 'stackable-ultimate-gutenberg-blocks' );
__( 'Other Posts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Posts/Pages', 'stackable-ultimate-gutenberg-blocks' );
__( 'Search for posts/pages', 'stackable-ultimate-gutenberg-blocks' );
__( 'Detected Custom Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post ID', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Slug', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Excerpt', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Date', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Date GMT', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Modified', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Modified GMT', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post Status', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author ID', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Posts URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Profile Picture URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Posts', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author First Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Author Last Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Comment Number', 'stackable-ultimate-gutenberg-blocks' );
__( 'Comment', 'stackable-ultimate-gutenberg-blocks' );
__( 'Comment Status', 'stackable-ultimate-gutenberg-blocks' );
__( 'Featured Image URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Media', 'stackable-ultimate-gutenberg-blocks' );
__( 'Post not found.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Site', 'stackable-ultimate-gutenberg-blocks' );
__( 'Site Tagline', 'stackable-ultimate-gutenberg-blocks' );
__( 'Site Title', 'stackable-ultimate-gutenberg-blocks' );
__( 'Site URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Url', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Name', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Description', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Short Description', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Purchase Note', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Image', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Price', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Price (Regular)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Price (No Tax)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Produce Price (Sale)', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Date Created', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Sale Date From', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Sale Date To', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Add to Cart URL', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product SKU', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Total Sales', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Total Stock', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Low Stock', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Weight', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Width', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Length', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Height', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Review Count', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Tax Status', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Tax Class', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Tags', 'stackable-ultimate-gutenberg-blocks' );
__( 'Product Attributes', 'stackable-ultimate-gutenberg-blocks' );
__( 'Data from Stackable custom fields', 'stackable-ultimate-gutenberg-blocks' );
_e( '📋 Custom Fields', 'stackable-ultimate-gutenberg-blocks' );
__( 'You can add small pieces of content here which you can use across your website - things like your contact email or the number of customers you\'ve served. You can find these fields under the "Site source" area when placing "Dynamic Content" in your blocks. %sLearn more%s.' , 'stackable-ultimate-gutenberg-blocks' );
_e( 'This is only available in Stackable Premium.', 'stackable-ultimate-gutenberg-blocks' );
_e( 'Go Premium', 'stackable-ultimate-gutenberg-blocks' );
__( '%s must be an integer.', 'stackable-ultimate-gutenberg-blocks' );
__( 'Stackable Global Color Schemes', 'stackable-ultimate-gutenberg-blocks' );
