var frontend_block_count_up;(()=>{"use strict";var t={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(t);const e=t=>{clearTimeout(t.countUpTimeout),t._countUpOrigInnerHTML&&(t.innerHTML=t._countUpOrigInnerHTML,t._countUpOrigInnerHTML=void 0),t.style.visibility=""},n=(t,e={})=>{const{duration:n=1e3,delay:i=16}=e,o=n/i,r=t.toString().split(/(<[^>]+>|[0-9.][,.0-9]*[0-9]*)/),c=[];for(let t=0;t<o;t++)c.push("");for(let t=0;t<r.length;t++)if(/([0-9.][,.0-9]*[0-9]*)/.test(r[t])&&!/<[^>]+>/.test(r[t])){let e=r[t];const n=[...e.matchAll(/[.,]/g)].map((t=>({char:t[0],i:e.length-t.index-1}))).sort(((t,e)=>t.i-e.i));e=e.replace(/[.,]/g,"");let i=c.length-1;for(let t=o;t>=1;t--){let r=parseInt(e/o*t,10);r=n.reduce(((t,{char:e,i:n})=>t.length<=n?t:t.slice(0,-n)+e+t.slice(-n)),r.toString()),c[i--]+=r}}else for(let e=0;e<o;e++)c[e]+=r[t];return c[c.length]=t.toString(),c},i="stk--count-up-active";var o;window.stackableCountUp=new class{callback=t=>{t.forEach((t=>{const o=t.target,r=o.getAttribute("data-duration")||1e3;!o.classList.contains(i)&&t.isIntersecting&&setTimeout((()=>{((t,i={})=>{const{action:o="start",duration:r=1e3,delay:c=16}=i;if("stop"===o)return void e(t);if(e(t),!/[0-9]/.test(t.innerHTML))return;const s=n(t.innerHTML,{duration:r||t.getAttribute("data-duration"),delay:c||t.getAttribute("data-delay")});t._countUpOrigInnerHTML=t.innerHTML,t.innerHTML=s[0]||"&nbsp;",t.style.visibility="visible";const a=function(){t.innerHTML=s.shift()||"&nbsp;",s.length?(clearTimeout(t.countUpTimeout),t.countUpTimeout=setTimeout(a,c)):t._countUpOrigInnerHTML=void 0};t.countUpTimeout=setTimeout(a,c)})(o,{duration:r}),o.classList.add(i)}),200)}))};init=()=>{const t=document.querySelectorAll(".stk-block-count-up__text"),e=window.matchMedia("(prefers-reduced-motion: reduce)").matches;"IntersectionObserver"in window&&!e?(this.io&&this.io.disconnect(),this.io=new IntersectionObserver(this.callback,{threshold:.25}),t.forEach((t=>{this.io.observe(t)}))):t.forEach((t=>{t.classList.add(i)}))}},o=window.stackableCountUp.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",o):o()),frontend_block_count_up=t})();