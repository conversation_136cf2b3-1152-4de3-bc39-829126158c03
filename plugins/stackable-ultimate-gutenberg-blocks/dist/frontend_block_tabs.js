var frontend_block_tabs;(()=>{"use strict";var t,e={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(e);class i{constructor(t){this.uniqueId=t.getAttribute("data-block-id"),this.parentEl=t,this.getDefaultState(),this.initTabs(),this.initWindowEventListeners()}getDefaultState=()=>{this.activeTab=parseInt(this.parentEl.getAttribute("data-initial-tab")||"1",10),this.tabList=this.parentEl.querySelector('[role="tablist"]'),this.tabs=this.parentEl.querySelector(".stk-block-tab-labels__wrapper").children,this.contents=this.parentEl.querySelector(".stk-block-tab-content > .stk-inner-blocks").children,this.customTabAnchors={}};initTabs=()=>{const t=[];Array.from(this.tabs).forEach(((e,i,s)=>{let a=!0;e.getAttribute("id")?this.customTabAnchors[e.getAttribute("id")]={index:i+1,tab:e}:(e.setAttribute("id",`stk-block-tab-label-${this.uniqueId}-${i+1}`),a=!1),t.push(e.getAttribute("id")),window.location.hash===`#${e.getAttribute("id")}`&&(s[this.activeTab-1].classList.remove("stk-block-tabs__tab--active"),this.activeTab=i+1),e.setAttribute("aria-controls",`stk-block-tab-content-${this.uniqueId}-${i+1}`),e.setAttribute("aria-selected",this.activeTab===i+1?"true":"false"),e.setAttribute("tabindex",this.activeTab===i+1?"0":"-1"),this.activeTab===i+1&&e.classList.add("stk-block-tabs__tab--active"),e.addEventListener("click",(()=>{this.changeTab(i+1),a&&window.location.hash!==`#${e.getAttribute("id")}`&&history.pushState({},"",`#${e.getAttribute("id")}`)}))})),Array.from(this.contents).forEach(((e,i)=>{e.setAttribute("id",`stk-block-tab-content-${this.uniqueId}-${i+1}`),e.setAttribute("aria-labelledby",t[i]),e.setAttribute("tabindex","0"),this.activeTab!==i+1?e.setAttribute("hidden","true"):e.removeAttribute("hidden")}));let e=0;this.tabList.addEventListener("keydown",(t=>{t.key.startsWith("Arrow")&&(this.tabs[e].setAttribute("tabindex",-1),"ArrowRight"===t.key||"ArrowDown"===t.key?(e++,e>=this.tabs.length&&(e=0)):"ArrowLeft"!==t.key&&"ArrowUp"!==t.key||(e--,e<0&&(e=this.tabs.length-1)),this.tabs[e].setAttribute("tabindex",0),this.tabs[e].focus(),t.preventDefault())}))};changeTab=t=>{this.activeTab!==t&&(Array.from(this.tabs).forEach(((e,i)=>{e.setAttribute("aria-selected",t===i+1?"true":"false"),e.setAttribute("tabindex",t===i+1?"0":"-1"),t===i+1?e.classList.add("stk-block-tabs__tab--active"):e.classList.remove("stk-block-tabs__tab--active")})),Array.from(this.contents).forEach(((e,i)=>{t!==i+1?e.setAttribute("hidden","true"):e.removeAttribute("hidden")})),this.activeTab=t)};initWindowEventListeners=()=>{Object.keys(this.customTabAnchors).length&&(window.addEventListener("hashchange",(()=>{const t=window.location.hash.slice(1);t in this.customTabAnchors&&this.changeTab(this.customTabAnchors[t].index)})),window.addEventListener("beforeunload",(()=>{const t=window.location.hash.slice(1);t in this.customTabAnchors&&this.customTabAnchors[t].tab.scrollIntoView()})))}}window.stackableTabs=new class{init=()=>{document.querySelectorAll(".stk-block-tabs").forEach((t=>{t._StackableHasInitTabs||(new i(t),t._StackableHasInitTabs=!0)}))}},t=window.stackableTabs.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",t):t()),frontend_block_tabs=e})();