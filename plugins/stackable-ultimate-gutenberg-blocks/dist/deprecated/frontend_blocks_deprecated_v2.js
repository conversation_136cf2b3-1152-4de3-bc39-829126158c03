/*! For license information please see frontend_blocks_deprecated_v2.js.LICENSE.txt */
(()=>{var t={2527:(t,e,n)=>{"use strict";function o(t){"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",t):t())}n.d(e,{Z:()=>o})},8178:(t,e,n)=>{"use strict";n.r(e),n.d(e,{initAll:()=>s});var o=n(2527);const r=t=>t.classList.contains("ugb-accordion--open"),i=function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=t.querySelector(".ugb-accordion__text, .ugb-accordion__content");if(clearTimeout(t.aTimeout),r(t)?(t.setAttribute("data-height",`${n.clientHeight+50}px`),n.style.maxHeight=t.getAttribute("data-height"),t.aTimeout=setTimeout((()=>{n.style.maxHeight=0}),1)):(n.style.maxHeight=t.getAttribute("data-height"),t.aTimeout=setTimeout((()=>{n.style.maxHeight=""}),350)),t.setAttribute("aria-expanded",r(t)?"false":"true"),t.classList.toggle("ugb-accordion--open"),!e&&t.classList.contains("ugb-accordion--single-open")){let e=t.nextElementSibling;for(;e&&e.classList.contains("ugb-accordion");)r(e)&&i(e,!0),e=e.nextElementSibling;for(e=t.previousElementSibling;e&&e.classList.contains("ugb-accordion");)r(e)&&i(e,!0),e=e.previousElementSibling}};let a=1;const s=()=>{document.querySelectorAll(".ugb-accordion, .ugb-accordion--v2").forEach((t=>(t=>{(t=>{const e=t.classList.contains("ugb-accordion--open"),n=t.querySelector(".ugb-accordion__text, .ugb-accordion__content");e||(t.style.display="none",n.style.maxHeight="none",t.classList.toggle("ugb-accordion--open"),t.style.display="");const o=t.querySelector(".ugb-accordion__text, .ugb-accordion__content").clientHeight;t.setAttribute("data-height",`${o+50}px`),e||(t.style.display="none",t.classList.toggle("ugb-accordion--open"),n.style.maxHeight="",t.style.display="")})(t);const e=t.querySelector(".ugb-accordion__heading");e.addEventListener("click",(e=>{e.preventDefault(),i(t)})),e.addEventListener("keypress",(e=>{e.preventDefault(),i(t)}));const n=t.querySelector(".ugb-accordion__heading h4, .ugb-accordion__title"),o=t.querySelector(".ugb-accordion__text, .ugb-accordion__content");n.setAttribute("id",`ugb-accordion-${a}__heading`),o.setAttribute("id",`ugb-accordion-${a}__content`),n.setAttribute("aria-controls",`ugb-accordion-${a}__content`),o.setAttribute("aria-labelledby",`ugb-accordion-${a}__heading`),a++})(t)))};(0,o.Z)(s)},426:(t,e,n)=>{"use strict";n.r(e);const o=t=>{clearTimeout(t.countUpTimeout),t._countUpOrigInnerHTML&&(t.innerHTML=t._countUpOrigInnerHTML,t._countUpOrigInnerHTML=void 0),t.style.visibility=""},r=(t,e={})=>{const{duration:n=1e3,delay:o=16}=e,r=n/o,i=t.toString().split(/(<[^>]+>|[0-9.][,.0-9]*[0-9]*)/),a=[];for(let t=0;t<r;t++)a.push("");for(let t=0;t<i.length;t++)if(/([0-9.][,.0-9]*[0-9]*)/.test(i[t])&&!/<[^>]+>/.test(i[t])){let e=i[t];const n=[...e.matchAll(/[.,]/g)].map((t=>({char:t[0],i:e.length-t.index-1}))).sort(((t,e)=>t.i-e.i));e=e.replace(/[.,]/g,"");let o=a.length-1;for(let t=r;t>=1;t--){let i=parseInt(e/r*t,10);i=n.reduce(((t,{char:e,i:n})=>t.length<=n?t:t.slice(0,-n)+e+t.slice(-n)),i.toString()),a[o--]+=i}}else for(let e=0;e<r;e++)a[e]+=i[t];return a[a.length]=t.toString(),a};(0,n(2527).Z)((()=>{n(6682),document.querySelectorAll(".ugb-countup .ugb-counter, .ugb-countup__counter").forEach((t=>{t.classList.add("ugb-countup--hide"),new Waypoint({element:t,handler(){((t,e={})=>{const{action:n="start",duration:i=1e3,delay:a=16}=e;if("stop"===n)return void o(t);if(o(t),!/[0-9]/.test(t.innerHTML))return;const s=r(t.innerHTML,{duration:i||t.getAttribute("data-duration"),delay:a||t.getAttribute("data-delay")});t._countUpOrigInnerHTML=t.innerHTML,t.innerHTML=s[0]||"&nbsp;",t.style.visibility="visible";const l=function(){t.innerHTML=s.shift()||"&nbsp;",s.length?(clearTimeout(t.countUpTimeout),t.countUpTimeout=setTimeout(l,a)):t._countUpOrigInnerHTML=void 0};t.countUpTimeout=setTimeout(l,a)})(t),t.classList.remove("ugb-countup--hide"),this.destroy()},offset:"bottom-in-view"})}))}))},2818:(t,e,n)=>{"use strict";n.r(e);var o=n(2527);(0,o.Z)((()=>{document.querySelectorAll(".ugb-expand").forEach((t=>{const e=t.querySelector(".ugb-expand__toggle"),n=n=>{t.classList.toggle("ugb-expand--more");const o=t.classList.contains("ugb-expand--more");e.setAttribute("aria-expanded",o?"true":"false"),n.preventDefault()};e&&(e.addEventListener("click",n),e.addEventListener("tapEnd",n))}))})),(0,o.Z)((()=>{document.querySelectorAll(".ugb-expand").forEach((t=>{const e=t.querySelector(".ugb-expand-button"),n=e=>{t.classList.toggle("ugb-more"),e.preventDefault()};e&&(e.addEventListener("click",n),e.addEventListener("tapEnd",n))}))}))},8596:(t,e,n)=>{"use strict";n.r(e);var o=n(2527),r=n(2568),i=n.n(r);(0,o.Z)((()=>{document.querySelectorAll(".ugb-notification.ugb-notification--dismissible").forEach((t=>{const e=t.getAttribute("data-uid")?t.getAttribute("data-uid"):i()(t.outerHTML).substr(0,6);t.querySelector(".ugb-notification__close-button").addEventListener("click",(n=>{n.preventDefault(),localStorage.setItem(`stckbl-notif-${e}`,1),t.style.display=""})),t.querySelector(".ugb-notification__close-button").addEventListener("keypress",(n=>{n.preventDefault(),localStorage.setItem(`stckbl-notif-${e}`,1),t.style.display=""})),window.location.search.match(/preview=\w+/)?t.style.display="flex":localStorage.getItem(`stckbl-notif-${e}`)||(t.style.display="flex")}))})),(0,o.Z)((()=>{document.querySelectorAll(".ugb-notification.dismissible-true[data-uid]").forEach((t=>{const e=t.getAttribute("data-uid");t.querySelector(".close-button").addEventListener("click",(()=>{localStorage.setItem(`stckbl-notif-${e}`,1),t.style.display=""})),window.location.search.match(/preview=\w+/)?t.style.display="block":localStorage.getItem(`stckbl-notif-${e}`)||(t.style.display="block")}))}))},6068:(t,e,n)=>{"use strict";n.r(e);var o=n(765),r=n.n(o);(0,n(2527).Z)((()=>{document.querySelectorAll(".ugb-video-popup[data-video], .ugb-video-popup [data-video]").forEach((t=>{t.querySelector(".ugb-video-popup__overlay, a").addEventListener("click",(e=>{e.preventDefault(),(t=>{if(r()){(t=>{const e=t.getAttribute("data-caption");e&&t.setAttribute("data-caption",e.replace(/<[^>]+>/g,"").replace(/[^\w. ]/gi,(function(t){return"&#"+t.charCodeAt(0)+";"})))})(t);const e=t.getAttribute("data-video"),n={el:t,noLoader:!0};e.match(/^\d+$/g)?n.vimeoSrc=e:e.match(/^https?:\/\//g)?n.vidSrc=e:n.ytSrc=e,r()(n)}})(t)}))}))}))},7321:()=>{window.NodeList&&!window.NodeList.prototype.forEach&&(window.NodeList.prototype.forEach=Array.prototype.forEach),function(t){try{t&&t.prototype&&null==t.prototype.firstElementChild&&Object.defineProperty(t.prototype,"firstElementChild",{get(){let t,e=this.childNodes,n=0;for(;t=e[n++];)if(1===t.nodeType)return t;return null}})}catch(t){}}(window.Node||window.Element)},765:t=>{var e,n,o,r,i,a,s,l,c,u,d,p,f,h,g,y,m,b,v,w,x,S,T,_,A,E,L,k,H,O,M,C,q,z=[],I={},W="appendChild",B="createElement",D="removeChild";function P(){var t=e.getBoundingClientRect(),n=t.top,i=t.left,a=t.width,s=t.height;return"transform:translate3D("+(i-(o.clientWidth-a)/2)+"px, "+(n-(o.clientHeight-s)/2)+"px, 0) scale3D("+e.clientWidth/r.clientWidth+", "+e.clientHeight/r.clientHeight+", 0)"}function j(t){var e=O.length-1;if(!f){if(t>0&&H===e||t<0&&!H){if(!q.loop)return X(i,""),void setTimeout(X,9,i,"animation:"+(t>0?"bpl":"bpf")+" .3s;transition:transform .35s");H=t>0?-1:e+1}if([(H=Math.max(0,Math.min(H+t,e)))-1,H,H+1].forEach((function(t){if(t=Math.max(0,Math.min(t,e)),!I[t]){var n=O[t].src,o=document[B]("IMG");o.addEventListener("load",Z.bind(null,n)),o.src=n,I[t]=o}})),I[H].complete)return R(t);f=!0,X(g,"opacity:.4;"),o[W](g),I[H].onload=function(){x&&R(t)},I[H].onerror=function(){O[H]={error:"Error loading image"},x&&R(t)}}}function R(t){f&&(o[D](g),f=!1);var n=O[H];if(n.error)alert(n.error);else{var a=o.querySelector("img:last-of-type");X(i=r=I[H],"animation:"+(t>0?"bpfl":"bpfr")+" .35s;transition:transform .35s"),X(a,"animation:"+(t>0?"bpfol":"bpfor")+" .35s both"),o[W](i),n.el&&(e=n.el)}M.innerHTML=H+1+"/"+O.length,F(O[H].caption),E&&E([i,O[H]])}function N(){var t,e,n=.95*window.innerHeight,o=.95*window.innerWidth,r=n/o,i=q.dimensions||[1920,1080],a=i[0],s=i[1],c=s/a;c>r?e=(t=Math.min(s,n))/c:t=(e=Math.min(a,o))*c,l.style.cssText+="width:"+e+"px;height:"+t+"px;"}function U(t){~[1,4].indexOf(r.readyState)?(Q(),setTimeout((function(){r.play()}),99)):r.error?Q(t):h=setTimeout(U,35,t)}function $(t){q.noLoader||(t&&X(g,"top:"+e.offsetTop+"px;left:"+e.offsetLeft+"px;height:"+e.clientHeight+"px;width:"+e.clientWidth+"px"),e.parentElement[t?W:D](g),f=t)}function F(t){m.innerHTML=t,X(y,"opacity:"+(t?"1;pointer-events:auto":"0"))}function Z(t){!~z.indexOf(t)&&z.push(t)}function Q(t){if(f&&$(),_&&_(),"string"==typeof t)return G(),q.onError?q.onError():alert("Error: The requested "+t+" could not be loaded.");T&&Z(u),r.style.cssText+=P(),X(o,"opacity:1;pointer-events:auto"),A&&(A=setTimeout(A,410)),w=!0,x=!!O,setTimeout((function(){r.style.cssText+="transition:transform .35s;transform:none",b&&setTimeout(F,250,b)}),60)}function V(t){var e=t?t.target:o,n=[y,v,a,s,m,k,L,g];e.blur(),S||~n.indexOf(e)||(r.style.cssText+=P(),X(o,"pointer-events:auto"),setTimeout(G,350),clearTimeout(A),w=!1,S=!0)}function G(){if((r===l?c:r).removeAttribute("src"),document.body[D](o),o[D](r),X(o,""),X(r,""),F(!1),x){for(var t=o.querySelectorAll("img"),e=0;e<t.length;e++)o[D](t[e]);f&&o[D](g),o[D](M),x=O=!1,I={},C||o[D](L),C||o[D](k),i.onload=Q,i.onerror=Q.bind(null,"image")}q.onClose&&q.onClose(),S=f=!1}function X(t,e){t.style.cssText=e}t.exports=function(t){var S,I,P,R;return n||function(){var t;function e(t){var e=document[B]("button");return e.className=t,e.innerHTML='<svg viewBox="0 0 48 48"><path d="M28 24L47 5a3 3 0 1 0-4-4L24 20 5 1a3 3 0 1 0-4 4l19 19L1 43a3 3 0 1 0 4 4l19-19 19 19a3 3 0 0 0 4 0v-4L28 24z"/></svg>',e}function u(t,e){var n=document[B]("button");return n.className="bp-lr",n.innerHTML='<svg viewBox="0 0 129 129" height="70" fill="#fff"><path d="M88.6 121.3c.8.8 1.8 1.2 2.9 1.2s2.1-.4 2.9-1.2a4.1 4.1 0 0 0 0-5.8l-51-51 51-51a4.1 4.1 0 0 0-5.8-5.8l-54 53.9a4.1 4.1 0 0 0 0 5.8l54 53.9z"/></svg>',X(n,e),n.onclick=function(e){e.stopPropagation(),j(t)},n}var p=document[B]("STYLE");p.innerHTML="#bp_caption,#bp_container{bottom:0;left:0;right:0;position:fixed;opacity:0}#bp_container>*,#bp_loader{position:absolute;right:0;z-index:10}#bp_container,#bp_caption,#bp_container svg{pointer-events:none}#bp_container{top:0;z-index:9999;background:rgba(0,0,0,.7);opacity:0;transition:opacity .35s}#bp_loader{top:0;left:0;bottom:0;display:flex;align-items:center;cursor:wait;background:0;z-index:9}#bp_loader svg{width:50%;max-width:300px;max-height:50%;margin:auto;animation:bpturn 1s infinite linear}#bp_aud,#bp_container img,#bp_sv,#bp_vid{user-select:none;max-height:96%;max-width:96%;top:0;bottom:0;left:0;margin:auto;box-shadow:0 0 3em rgba(0,0,0,.4);z-index:-1}#bp_sv{background:#111}#bp_sv svg{width:66px}#bp_caption{font-size:.9em;padding:1.3em;background:rgba(15,15,15,.94);color:#fff;text-align:center;transition:opacity .3s}#bp_aud{width:650px;top:calc(50% - 20px);bottom:auto;box-shadow:none}#bp_count{left:0;right:auto;padding:14px;color:rgba(255,255,255,.7);font-size:22px;cursor:default}#bp_container button{position:absolute;border:0;outline:0;background:0;cursor:pointer;transition:all .1s}#bp_container>.bp-x{padding:0;height:41px;width:41px;border-radius:100%;top:8px;right:14px;opacity:.8;line-height:1}#bp_container>.bp-x:focus,#bp_container>.bp-x:hover{background:rgba(255,255,255,.2)}.bp-x svg,.bp-xc svg{height:21px;width:20px;fill:#fff;vertical-align:top;}.bp-xc svg{width:16px}#bp_container .bp-xc{left:2%;bottom:100%;padding:9px 20px 7px;background:#d04444;border-radius:2px 2px 0 0;opacity:.85}#bp_container .bp-xc:focus,#bp_container .bp-xc:hover{opacity:1}.bp-lr{top:50%;top:calc(50% - 130px);padding:99px 0;width:6%;background:0;border:0;opacity:.4;transition:opacity .1s}.bp-lr:focus,.bp-lr:hover{opacity:.8}@keyframes bpf{50%{transform:translatex(15px)}100%{transform:none}}@keyframes bpl{50%{transform:translatex(-15px)}100%{transform:none}}@keyframes bpfl{0%{opacity:0;transform:translatex(70px)}100%{opacity:1;transform:none}}@keyframes bpfr{0%{opacity:0;transform:translatex(-70px)}100%{opacity:1;transform:none}}@keyframes bpfol{0%{opacity:1;transform:none}100%{opacity:0;transform:translatex(-70px)}}@keyframes bpfor{0%{opacity:1;transform:none}100%{opacity:0;transform:translatex(70px)}}@keyframes bpturn{0%{transform:none}100%{transform:rotate(360deg)}}@media (max-width:600px){.bp-lr{font-size:15vw}}",document.head[W](p),(o=document[B]("DIV")).id="bp_container",o.onclick=V,d=e("bp-x"),o[W](d),"ontouchstart"in window&&(C=!0,o.ontouchstart=function(e){var n=e.changedTouches;t=n[0].pageX},o.ontouchmove=function(t){t.preventDefault()},o.ontouchend=function(e){var n=e.changedTouches;if(x){var o=n[0].pageX-t;o<-30&&j(1),o>30&&j(-1)}}),i=document[B]("IMG"),(a=document[B]("VIDEO")).id="bp_vid",a.setAttribute("playsinline",!0),a.controls=!0,a.loop=!0,(s=document[B]("audio")).id="bp_aud",s.controls=!0,s.loop=!0,(M=document[B]("span")).id="bp_count",(y=document[B]("DIV")).id="bp_caption",(v=e("bp-xc")).onclick=F.bind(null,!1),y[W](v),m=document[B]("SPAN"),y[W](m),o[W](y),L=u(1,"transform:scalex(-1)"),k=u(-1,"left:0;right:auto"),(g=document[B]("DIV")).id="bp_loader",g.innerHTML='<svg viewbox="0 0 32 32" fill="#fff" opacity=".8"><path d="M16 0a16 16 0 0 0 0 32 16 16 0 0 0 0-32m0 4a12 12 0 0 1 0 24 12 12 0 0 1 0-24" fill="#000" opacity=".5"/><path d="M16 0a16 16 0 0 1 16 16h-4A12 12 0 0 0 16 4z"/></svg>',(l=document[B]("DIV")).id="bp_sv",(c=document[B]("IFRAME")).setAttribute("allowfullscreen",!0),c.allow="autoplay; fullscreen",c.onload=function(){return l[D](g)},X(c,"border:0;position:absolute;height:100%;width:100%;left:0;top:0"),l[W](c),i.onload=Q,i.onerror=Q.bind(null,"image"),window.addEventListener("resize",(function(){x||f&&$(!0),r===l&&N()})),document.addEventListener("keyup",(function(t){var e=t.keyCode;27===e&&w&&V(),x&&(39===e&&j(1),37===e&&j(-1),38===e&&j(10),40===e&&j(-10))})),document.addEventListener("keydown",(function(t){x&&~[37,38,39,40].indexOf(t.keyCode)&&t.preventDefault()})),document.addEventListener("focus",(function(t){w&&!o.contains(t.target)&&(t.stopPropagation(),d.focus())}),!0),n=!0}(),f&&(clearTimeout(h),G()),q=t,p=t.ytSrc||t.vimeoSrc,_=t.animationStart,A=t.animationEnd,E=t.onChangeImage,e=t.el,T=!1,b=e.getAttribute("data-caption"),t.gallery?function(t,n){var a=q.galleryAttribute||"data-bp";if(Array.isArray(t))O=t,b=t[H=n||0].caption;else{var s=(O=[].slice.call("string"==typeof t?document.querySelectorAll(t+" ["+a+"]"):t)).indexOf(e);H=0===n||n?n:-1!==s?s:0,O=O.map((function(t){return{el:t,src:t.getAttribute(a),caption:t.getAttribute("data-caption")}}))}T=!0,u=O[H].src,!~z.indexOf(u)&&$(!0),O.length>1?(o[W](M),M.innerHTML=H+1+"/"+O.length,C||(o[W](L),o[W](k))):O=!1,(r=i).src=u}(t.gallery,t.position):p||t.iframeSrc?(r=l,P="https://",R="autoplay=1",q.ytSrc?I=P+"www.youtube"+(q.ytNoCookie?"-nocookie":"")+".com/embed/"+p+"?html5=1&rel=0&playsinline=1&"+R:q.vimeoSrc?I=P+"player.vimeo.com/video/"+p+"?"+R:q.iframeSrc&&(I=q.iframeSrc),X(g,""),l[W](g),c.src=I,N(),setTimeout(Q,9)):t.imgSrc?(T=!0,u=t.imgSrc,!~z.indexOf(u)&&$(!0),(r=i).src=u):t.audio?($(!0),(r=s).src=t.audio,U("audio file")):t.vidSrc?($(!0),t.dimensions&&X(a,"width:"+t.dimensions[0]+"px"),S=t.vidSrc,Array.isArray(S)?(r=a.cloneNode(),S.forEach((function(t){var e=document[B]("SOURCE");e.src=t,e.type="video/"+t.match(/.(\w+)$/)[1],r[W](e)}))):(r=a).src=S,U("video")):(r=i).src="IMG"===e.tagName?e.src:window.getComputedStyle(e).backgroundImage.replace(/^url|[(|)|'|"]/g,""),o[W](r),document.body[W](o),{close:V,next:function(){return j(1)},prev:function(){return j(-1)}}}},487:t=>{var e={utf8:{stringToBytes:function(t){return e.bin.stringToBytes(unescape(encodeURIComponent(t)))},bytesToString:function(t){return decodeURIComponent(escape(e.bin.bytesToString(t)))}},bin:{stringToBytes:function(t){for(var e=[],n=0;n<t.length;n++)e.push(255&t.charCodeAt(n));return e},bytesToString:function(t){for(var e=[],n=0;n<t.length;n++)e.push(String.fromCharCode(t[n]));return e.join("")}}};t.exports=e},1012:t=>{var e,n;e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(t,e){return t<<e|t>>>32-e},rotr:function(t,e){return t<<32-e|t>>>e},endian:function(t){if(t.constructor==Number)return 16711935&n.rotl(t,8)|4278255360&n.rotl(t,24);for(var e=0;e<t.length;e++)t[e]=n.endian(t[e]);return t},randomBytes:function(t){for(var e=[];t>0;t--)e.push(Math.floor(256*Math.random()));return e},bytesToWords:function(t){for(var e=[],n=0,o=0;n<t.length;n++,o+=8)e[o>>>5]|=t[n]<<24-o%32;return e},wordsToBytes:function(t){for(var e=[],n=0;n<32*t.length;n+=8)e.push(t[n>>>5]>>>24-n%32&255);return e},bytesToHex:function(t){for(var e=[],n=0;n<t.length;n++)e.push((t[n]>>>4).toString(16)),e.push((15&t[n]).toString(16));return e.join("")},hexToBytes:function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(parseInt(t.substr(n,2),16));return e},bytesToBase64:function(t){for(var n=[],o=0;o<t.length;o+=3)for(var r=t[o]<<16|t[o+1]<<8|t[o+2],i=0;i<4;i++)8*o+6*i<=8*t.length?n.push(e.charAt(r>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(t){t=t.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],o=0,r=0;o<t.length;r=++o%4)0!=r&&n.push((e.indexOf(t.charAt(o-1))&Math.pow(2,-2*r+8)-1)<<2*r|e.indexOf(t.charAt(o))>>>6-2*r);return n}},t.exports=n},8738:t=>{function e(t){return!!t.constructor&&"function"==typeof t.constructor.isBuffer&&t.constructor.isBuffer(t)}t.exports=function(t){return null!=t&&(e(t)||function(t){return"function"==typeof t.readFloatLE&&"function"==typeof t.slice&&e(t.slice(0,0))}(t)||!!t._isBuffer)}},2568:(t,e,n)=>{var o,r,i,a,s;o=n(1012),r=n(487).utf8,i=n(8738),a=n(487).bin,(s=function(t,e){t.constructor==String?t=e&&"binary"===e.encoding?a.stringToBytes(t):r.stringToBytes(t):i(t)?t=Array.prototype.slice.call(t,0):Array.isArray(t)||t.constructor===Uint8Array||(t=t.toString());for(var n=o.bytesToWords(t),l=8*t.length,c=1732584193,u=-271733879,d=-1732584194,p=271733878,f=0;f<n.length;f++)n[f]=16711935&(n[f]<<8|n[f]>>>24)|4278255360&(n[f]<<24|n[f]>>>8);n[l>>>5]|=128<<l%32,n[14+(l+64>>>9<<4)]=l;var h=s._ff,g=s._gg,y=s._hh,m=s._ii;for(f=0;f<n.length;f+=16){var b=c,v=u,w=d,x=p;c=h(c,u,d,p,n[f+0],7,-680876936),p=h(p,c,u,d,n[f+1],12,-389564586),d=h(d,p,c,u,n[f+2],17,606105819),u=h(u,d,p,c,n[f+3],22,-1044525330),c=h(c,u,d,p,n[f+4],7,-176418897),p=h(p,c,u,d,n[f+5],12,1200080426),d=h(d,p,c,u,n[f+6],17,-1473231341),u=h(u,d,p,c,n[f+7],22,-45705983),c=h(c,u,d,p,n[f+8],7,1770035416),p=h(p,c,u,d,n[f+9],12,-1958414417),d=h(d,p,c,u,n[f+10],17,-42063),u=h(u,d,p,c,n[f+11],22,-1990404162),c=h(c,u,d,p,n[f+12],7,1804603682),p=h(p,c,u,d,n[f+13],12,-40341101),d=h(d,p,c,u,n[f+14],17,-1502002290),c=g(c,u=h(u,d,p,c,n[f+15],22,1236535329),d,p,n[f+1],5,-165796510),p=g(p,c,u,d,n[f+6],9,-1069501632),d=g(d,p,c,u,n[f+11],14,643717713),u=g(u,d,p,c,n[f+0],20,-373897302),c=g(c,u,d,p,n[f+5],5,-701558691),p=g(p,c,u,d,n[f+10],9,38016083),d=g(d,p,c,u,n[f+15],14,-660478335),u=g(u,d,p,c,n[f+4],20,-405537848),c=g(c,u,d,p,n[f+9],5,568446438),p=g(p,c,u,d,n[f+14],9,-1019803690),d=g(d,p,c,u,n[f+3],14,-187363961),u=g(u,d,p,c,n[f+8],20,1163531501),c=g(c,u,d,p,n[f+13],5,-1444681467),p=g(p,c,u,d,n[f+2],9,-51403784),d=g(d,p,c,u,n[f+7],14,1735328473),c=y(c,u=g(u,d,p,c,n[f+12],20,-1926607734),d,p,n[f+5],4,-378558),p=y(p,c,u,d,n[f+8],11,-2022574463),d=y(d,p,c,u,n[f+11],16,1839030562),u=y(u,d,p,c,n[f+14],23,-35309556),c=y(c,u,d,p,n[f+1],4,-1530992060),p=y(p,c,u,d,n[f+4],11,1272893353),d=y(d,p,c,u,n[f+7],16,-155497632),u=y(u,d,p,c,n[f+10],23,-1094730640),c=y(c,u,d,p,n[f+13],4,681279174),p=y(p,c,u,d,n[f+0],11,-358537222),d=y(d,p,c,u,n[f+3],16,-722521979),u=y(u,d,p,c,n[f+6],23,76029189),c=y(c,u,d,p,n[f+9],4,-640364487),p=y(p,c,u,d,n[f+12],11,-421815835),d=y(d,p,c,u,n[f+15],16,530742520),c=m(c,u=y(u,d,p,c,n[f+2],23,-995338651),d,p,n[f+0],6,-198630844),p=m(p,c,u,d,n[f+7],10,1126891415),d=m(d,p,c,u,n[f+14],15,-1416354905),u=m(u,d,p,c,n[f+5],21,-57434055),c=m(c,u,d,p,n[f+12],6,1700485571),p=m(p,c,u,d,n[f+3],10,-1894986606),d=m(d,p,c,u,n[f+10],15,-1051523),u=m(u,d,p,c,n[f+1],21,-2054922799),c=m(c,u,d,p,n[f+8],6,1873313359),p=m(p,c,u,d,n[f+15],10,-30611744),d=m(d,p,c,u,n[f+6],15,-1560198380),u=m(u,d,p,c,n[f+13],21,1309151649),c=m(c,u,d,p,n[f+4],6,-145523070),p=m(p,c,u,d,n[f+11],10,-1120210379),d=m(d,p,c,u,n[f+2],15,718787259),u=m(u,d,p,c,n[f+9],21,-343485551),c=c+b>>>0,u=u+v>>>0,d=d+w>>>0,p=p+x>>>0}return o.endian([c,u,d,p])})._ff=function(t,e,n,o,r,i,a){var s=t+(e&n|~e&o)+(r>>>0)+a;return(s<<i|s>>>32-i)+e},s._gg=function(t,e,n,o,r,i,a){var s=t+(e&o|n&~o)+(r>>>0)+a;return(s<<i|s>>>32-i)+e},s._hh=function(t,e,n,o,r,i,a){var s=t+(e^n^o)+(r>>>0)+a;return(s<<i|s>>>32-i)+e},s._ii=function(t,e,n,o,r,i,a){var s=t+(n^(e|~o))+(r>>>0)+a;return(s<<i|s>>>32-i)+e},s._blocksize=16,s._digestsize=16,t.exports=function(t,e){if(null==t)throw new Error("Illegal argument "+t);var n=o.wordsToBytes(s(t,e));return e&&e.asBytes?n:e&&e.asString?a.bytesToString(n):o.bytesToHex(n)}},6682:()=>{!function(){"use strict";var t=0,e={};function n(o){if(!o)throw new Error("No options passed to Waypoint constructor");if(!o.element)throw new Error("No element option passed to Waypoint constructor");if(!o.handler)throw new Error("No handler option passed to Waypoint constructor");this.key="waypoint-"+t,this.options=n.Adapter.extend({},n.defaults,o),this.element=this.options.element,this.adapter=new n.Adapter(this.element),this.callback=o.handler,this.axis=this.options.horizontal?"horizontal":"vertical",this.enabled=this.options.enabled,this.triggerPoint=null,this.group=n.Group.findOrCreate({name:this.options.group,axis:this.axis}),this.context=n.Context.findOrCreateByElement(this.options.context),n.offsetAliases[this.options.offset]&&(this.options.offset=n.offsetAliases[this.options.offset]),this.group.add(this),this.context.add(this),e[this.key]=this,t+=1}n.prototype.queueTrigger=function(t){this.group.queueTrigger(this,t)},n.prototype.trigger=function(t){this.enabled&&this.callback&&this.callback.apply(this,t)},n.prototype.destroy=function(){this.context.remove(this),this.group.remove(this),delete e[this.key]},n.prototype.disable=function(){return this.enabled=!1,this},n.prototype.enable=function(){return this.context.refresh(),this.enabled=!0,this},n.prototype.next=function(){return this.group.next(this)},n.prototype.previous=function(){return this.group.previous(this)},n.invokeAll=function(t){var n=[];for(var o in e)n.push(e[o]);for(var r=0,i=n.length;r<i;r++)n[r][t]()},n.destroyAll=function(){n.invokeAll("destroy")},n.disableAll=function(){n.invokeAll("disable")},n.enableAll=function(){for(var t in n.Context.refreshAll(),e)e[t].enabled=!0;return this},n.refreshAll=function(){n.Context.refreshAll()},n.viewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight},n.viewportWidth=function(){return document.documentElement.clientWidth},n.adapters=[],n.defaults={context:window,continuous:!0,enabled:!0,group:"default",horizontal:!1,offset:0},n.offsetAliases={"bottom-in-view":function(){return this.context.innerHeight()-this.adapter.outerHeight()},"right-in-view":function(){return this.context.innerWidth()-this.adapter.outerWidth()}},window.Waypoint=n}(),function(){"use strict";function t(t){window.setTimeout(t,1e3/60)}var e=0,n={},o=window.Waypoint,r=window.onload;function i(t){this.element=t,this.Adapter=o.Adapter,this.adapter=new this.Adapter(t),this.key="waypoint-context-"+e,this.didScroll=!1,this.didResize=!1,this.oldScroll={x:this.adapter.scrollLeft(),y:this.adapter.scrollTop()},this.waypoints={vertical:{},horizontal:{}},t.waypointContextKey=this.key,n[t.waypointContextKey]=this,e+=1,o.windowContext||(o.windowContext=!0,o.windowContext=new i(window)),this.createThrottledScrollHandler(),this.createThrottledResizeHandler()}i.prototype.add=function(t){var e=t.options.horizontal?"horizontal":"vertical";this.waypoints[e][t.key]=t,this.refresh()},i.prototype.checkEmpty=function(){var t=this.Adapter.isEmptyObject(this.waypoints.horizontal),e=this.Adapter.isEmptyObject(this.waypoints.vertical),o=this.element==this.element.window;t&&e&&!o&&(this.adapter.off(".waypoints"),delete n[this.key])},i.prototype.createThrottledResizeHandler=function(){var t=this;function e(){t.handleResize(),t.didResize=!1}this.adapter.on("resize.waypoints",(function(){t.didResize||(t.didResize=!0,o.requestAnimationFrame(e))}))},i.prototype.createThrottledScrollHandler=function(){var t=this;function e(){t.handleScroll(),t.didScroll=!1}this.adapter.on("scroll.waypoints",(function(){t.didScroll&&!o.isTouch||(t.didScroll=!0,o.requestAnimationFrame(e))}))},i.prototype.handleResize=function(){o.Context.refreshAll()},i.prototype.handleScroll=function(){var t={},e={horizontal:{newScroll:this.adapter.scrollLeft(),oldScroll:this.oldScroll.x,forward:"right",backward:"left"},vertical:{newScroll:this.adapter.scrollTop(),oldScroll:this.oldScroll.y,forward:"down",backward:"up"}};for(var n in e){var o=e[n],r=o.newScroll>o.oldScroll?o.forward:o.backward;for(var i in this.waypoints[n]){var a=this.waypoints[n][i];if(null!==a.triggerPoint){var s=o.oldScroll<a.triggerPoint,l=o.newScroll>=a.triggerPoint;(s&&l||!s&&!l)&&(a.queueTrigger(r),t[a.group.id]=a.group)}}}for(var c in t)t[c].flushTriggers();this.oldScroll={x:e.horizontal.newScroll,y:e.vertical.newScroll}},i.prototype.innerHeight=function(){return this.element==this.element.window?o.viewportHeight():this.adapter.innerHeight()},i.prototype.remove=function(t){delete this.waypoints[t.axis][t.key],this.checkEmpty()},i.prototype.innerWidth=function(){return this.element==this.element.window?o.viewportWidth():this.adapter.innerWidth()},i.prototype.destroy=function(){var t=[];for(var e in this.waypoints)for(var n in this.waypoints[e])t.push(this.waypoints[e][n]);for(var o=0,r=t.length;o<r;o++)t[o].destroy()},i.prototype.refresh=function(){var t,e=this.element==this.element.window,n=e?void 0:this.adapter.offset(),r={};for(var i in this.handleScroll(),t={horizontal:{contextOffset:e?0:n.left,contextScroll:e?0:this.oldScroll.x,contextDimension:this.innerWidth(),oldScroll:this.oldScroll.x,forward:"right",backward:"left",offsetProp:"left"},vertical:{contextOffset:e?0:n.top,contextScroll:e?0:this.oldScroll.y,contextDimension:this.innerHeight(),oldScroll:this.oldScroll.y,forward:"down",backward:"up",offsetProp:"top"}}){var a=t[i];for(var s in this.waypoints[i]){var l,c,u,d,p=this.waypoints[i][s],f=p.options.offset,h=p.triggerPoint,g=0,y=null==h;p.element!==p.element.window&&(g=p.adapter.offset()[a.offsetProp]),"function"==typeof f?f=f.apply(p):"string"==typeof f&&(f=parseFloat(f),p.options.offset.indexOf("%")>-1&&(f=Math.ceil(a.contextDimension*f/100))),l=a.contextScroll-a.contextOffset,p.triggerPoint=Math.floor(g+l-f),c=h<a.oldScroll,u=p.triggerPoint>=a.oldScroll,d=!c&&!u,!y&&c&&u?(p.queueTrigger(a.backward),r[p.group.id]=p.group):(!y&&d||y&&a.oldScroll>=p.triggerPoint)&&(p.queueTrigger(a.forward),r[p.group.id]=p.group)}}return o.requestAnimationFrame((function(){for(var t in r)r[t].flushTriggers()})),this},i.findOrCreateByElement=function(t){return i.findByElement(t)||new i(t)},i.refreshAll=function(){for(var t in n)n[t].refresh()},i.findByElement=function(t){return n[t.waypointContextKey]},window.onload=function(){r&&r(),i.refreshAll()},o.requestAnimationFrame=function(e){(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||t).call(window,e)},o.Context=i}(),function(){"use strict";function t(t,e){return t.triggerPoint-e.triggerPoint}function e(t,e){return e.triggerPoint-t.triggerPoint}var n={vertical:{},horizontal:{}},o=window.Waypoint;function r(t){this.name=t.name,this.axis=t.axis,this.id=this.name+"-"+this.axis,this.waypoints=[],this.clearTriggerQueues(),n[this.axis][this.name]=this}r.prototype.add=function(t){this.waypoints.push(t)},r.prototype.clearTriggerQueues=function(){this.triggerQueues={up:[],down:[],left:[],right:[]}},r.prototype.flushTriggers=function(){for(var n in this.triggerQueues){var o=this.triggerQueues[n],r="up"===n||"left"===n;o.sort(r?e:t);for(var i=0,a=o.length;i<a;i+=1){var s=o[i];(s.options.continuous||i===o.length-1)&&s.trigger([n])}}this.clearTriggerQueues()},r.prototype.next=function(e){this.waypoints.sort(t);var n=o.Adapter.inArray(e,this.waypoints);return n===this.waypoints.length-1?null:this.waypoints[n+1]},r.prototype.previous=function(e){this.waypoints.sort(t);var n=o.Adapter.inArray(e,this.waypoints);return n?this.waypoints[n-1]:null},r.prototype.queueTrigger=function(t,e){this.triggerQueues[e].push(t)},r.prototype.remove=function(t){var e=o.Adapter.inArray(t,this.waypoints);e>-1&&this.waypoints.splice(e,1)},r.prototype.first=function(){return this.waypoints[0]},r.prototype.last=function(){return this.waypoints[this.waypoints.length-1]},r.findOrCreate=function(t){return n[t.axis][t.name]||new r(t)},o.Group=r}(),function(){"use strict";var t=window.Waypoint;function e(t){return t===t.window}function n(t){return e(t)?t:t.defaultView}function o(t){this.element=t,this.handlers={}}o.prototype.innerHeight=function(){return e(this.element)?this.element.innerHeight:this.element.clientHeight},o.prototype.innerWidth=function(){return e(this.element)?this.element.innerWidth:this.element.clientWidth},o.prototype.off=function(t,e){function n(t,e,n){for(var o=0,r=e.length-1;o<r;o++){var i=e[o];n&&n!==i||t.removeEventListener(i)}}var o=t.split("."),r=o[0],i=o[1],a=this.element;if(i&&this.handlers[i]&&r)n(a,this.handlers[i][r],e),this.handlers[i][r]=[];else if(r)for(var s in this.handlers)n(a,this.handlers[s][r]||[],e),this.handlers[s][r]=[];else if(i&&this.handlers[i]){for(var l in this.handlers[i])n(a,this.handlers[i][l],e);this.handlers[i]={}}},o.prototype.offset=function(){if(!this.element.ownerDocument)return null;var t=this.element.ownerDocument.documentElement,e=n(this.element.ownerDocument),o={top:0,left:0};return this.element.getBoundingClientRect&&(o=this.element.getBoundingClientRect()),{top:o.top+e.pageYOffset-t.clientTop,left:o.left+e.pageXOffset-t.clientLeft}},o.prototype.on=function(t,e){var n=t.split("."),o=n[0],r=n[1]||"__default",i=this.handlers[r]=this.handlers[r]||{};(i[o]=i[o]||[]).push(e),this.element.addEventListener(o,e)},o.prototype.outerHeight=function(t){var n,o=this.innerHeight();return t&&!e(this.element)&&(n=window.getComputedStyle(this.element),o+=parseInt(n.marginTop,10),o+=parseInt(n.marginBottom,10)),o},o.prototype.outerWidth=function(t){var n,o=this.innerWidth();return t&&!e(this.element)&&(n=window.getComputedStyle(this.element),o+=parseInt(n.marginLeft,10),o+=parseInt(n.marginRight,10)),o},o.prototype.scrollLeft=function(){var t=n(this.element);return t?t.pageXOffset:this.element.scrollLeft},o.prototype.scrollTop=function(){var t=n(this.element);return t?t.pageYOffset:this.element.scrollTop},o.extend=function(){var t=Array.prototype.slice.call(arguments);function e(t,e){if("object"==typeof t&&"object"==typeof e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}for(var n=1,o=t.length;n<o;n++)e(t[0],t[n]);return t[0]},o.inArray=function(t,e,n){return null==e?-1:e.indexOf(t,n)},o.isEmptyObject=function(t){for(var e in t)return!1;return!0},t.adapters.push({name:"noframework",Adapter:o}),t.Adapter=o}()},7096:(t,e,n)=>{var o={"./accordion/frontend.js":8178,"./count-up/frontend.js":426,"./expand/frontend.js":2818,"./notification/frontend.js":8596,"./video-popup/frontend.js":6068};function r(t){var e=i(t);return n(e)}function i(t){if(!n.o(o,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return o[t]}r.keys=function(){return Object.keys(o)},r.resolve=i,t.exports=r,r.id=7096}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var i=e[o]={exports:{}};return t[o](i,i.exports,n),i.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{"use strict";n(7321);const t=n(7096);t.keys().forEach((e=>t(e)))})()})();