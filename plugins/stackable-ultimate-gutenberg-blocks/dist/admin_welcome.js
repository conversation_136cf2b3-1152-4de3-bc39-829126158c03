/*! For license information please see admin_welcome.js.LICENSE.txt */
(()=>{var e,t,n,o,a={7385:()=>{Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){let t=this;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null})},6446:(e,t,n)=>{"use strict";const o=wp.element,a=wp.ajax;function l(e){"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",e):e())}const r=wp.components,i=e=>o.createRoot?(0,o.createRoot)(e):{render:t=>wp.element.render(t,e),unmount:()=>wp.element.unmountComponentAtNode(e)},s=stackable;function c(){return c=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},c.apply(this,arguments)}l((()=>{const e=document.querySelector(".s-news-box-content");e&&""===e.innerHTML&&(i(e).render((0,o.createElement)("div",null,(0,o.createElement)(r.Spinner,null))),(0,a.send)("stackable_news_feed_ajax",{success:t=>{e.innerHTML=t},error:e=>{alert(e)},data:{nonce:s.nonceNews}}))}));var u=n(7294);const d=wp.i18n,p=wp.api,m=wp.hooks;var h=n(4184),g=n.n(h);const v=lodash;let b=null;const f=()=>(b||(b=p.loadPromise.then((()=>(new p.models.Settings).fetch().then((e=>(b=null,e)))))),b);let k=1;const _=e=>{const{showLabel:t=!0}=e,[n]=(0,o.useState)("ugb-admin-setting-"+k++),a=!e.searchedSettings||e.searchedSettings.includes(e.label),l=g()(["ugb-admin-setting",e.className],{[`ugb-admin-setting--${e.size}`]:e.size,"ugb-admin-setting--not-highlight":!a});return(0,o.createElement)("div",{className:l,id:n},(0,o.createElement)("label",{className:"ugb-admin-setting__label-wrapper",htmlFor:n,onClick:e.onClick},!!e.label&&t&&(0,o.createElement)("span",{className:"ugb-admin-setting__label"},e.label),(0,o.createElement)("div",{className:"ugb-admin-setting__field"},e.children)),e.help&&(0,o.createElement)("p",{className:"ugb-admin-setting__help"},e.help))};_.defaultProps={label:"",onClick:()=>{}};const y=_,w=e=>(0,o.createElement)(y,e,(0,o.createElement)("select",{className:"ugb-admin-select-setting",value:e.value,onChange:t=>e.onChange(t.target.value)},e.options.map(((e,t)=>(0,o.createElement)("option",{key:t,value:e.value},e.name)))),e.children);w.defaultProps={label:"",value:"",onChange:()=>{},options:[]};const E=w,S=e=>{const t=(0,o.createRef)();return(0,o.createElement)(y,c({onClick:n=>{e.onChange(!e.value),n.preventDefault(),t.current.focus()}},e,{className:g()(e.className,"ugb-admin-toggle-setting")}),(0,o.createElement)("button",{ref:t,className:g()("ugb-admin-toggle-setting__button",{"ugb-admin-toggle-setting__button--enabled":!!e.value}),type:"button",role:"switch","aria-checked":!!e.value,onClick:t=>{e.onChange(!e.value),t.preventDefault(),t.stopPropagation()},style:{minWidth:e.width||void 0},disabled:e.isDisabled?"disabled":""}),(0,o.createElement)("span",{className:"ugb-admin-toggle-setting__label"},(0,o.createElement)("span",{style:{visibility:e.value?"visible":"hidden"}},e.enabled),(0,o.createElement)("span",{style:{visibility:e.value?"hidden":"visible"}},e.disabled)))};S.defaultProps={label:"",value:!1,placeholder:"",onChange:()=>{},disabled:(0,d.__)("Disabled",s.i18n),enabled:(0,d.__)("Enabled",s.i18n),width:"",isDisabled:!1};const x=S,C=e=>{const t=(0,o.createRef)();return(0,o.createElement)(y,c({onClick:e=>{e.preventDefault(),t.current.focus()}},e),(0,o.createElement)("input",{ref:t,className:"ugb-admin-text-setting",type:e.type,value:e.value,placeholder:e.placeholder,onChange:t=>{e.onChange(t.target.value),t.preventDefault(),t.stopPropagation()}}),e.children)};C.defaultProps={label:"",type:"text",value:"",placeholder:"",onChange:()=>{}};const T=C,M=(0,o.forwardRef)(((e,t)=>{const n=g()([e.className,"ugb-button-component"]);return(0,o.createElement)(r.Button,c({},e,{className:n,ref:t}))})),N=e=>(0,o.createElement)(y,c({},e,{showLabel:!1}),(0,o.createElement)("div",{className:"ugb-admin-toolbar-setting__wrapper"},(0,o.createElement)("h3",null,e.label),(0,o.createElement)("a",{href:e.demoLink,target:"_blank",rel:"noopener noreferrer",onClick:e=>e.stopPropagation()},(0,d.__)("view demo",s.i18n)),(0,o.createElement)("div",{className:"ugb-admin-toolbar-setting__group-wrapper"},(0,o.createElement)(r.ButtonGroup,{children:e.controls.map((t=>{const n=e.value?e.value===t.value:e.placeholder===t.value,a=n?"0":"-1";return!e.availableStates||e.availableStates.includes(t.value)?(0,o.createElement)(M,{style:t.selectedColor&&n?{color:t.selectedColor}:{},isPrimary:!t.selectedColor&&n,key:t.value,label:t.title||e.label,tabIndex:a,"aria-pressed":n,onClick:()=>{t.value!==e.value&&e.onChange(t.value)},onKeyDown:e=>{const t=e.target;if(t)if(39===e.keyCode){const e=t.nextElementSibling||t.parentElement.firstElementChild;e.focus(),e.click()}else if(37===e.keyCode){const e=t.previousElementSibling||t.parentElement.lastElementChild;e.focus(),e.click()}},children:(0,o.createElement)("span",{className:"ugb-admin-toolbar-setting__option"},t.title)}):null})),className:"ugb-admin-toolbar-setting"}))));N.defaultProps={controls:[],label:"",value:"",onChange:()=>{}};const I=N,O=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M7 17L17 7",stroke:"url(#arrow-up-right_svg__paint0_linear_285_198)",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.createElement)("path",{d:"M7 7h10v10",stroke:"url(#arrow-up-right_svg__paint1_linear_285_198)",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.createElement)("defs",null,(0,o.createElement)("linearGradient",{id:"arrow-up-right_svg__paint0_linear_285_198",x1:10.311,y1:3.071,x2:20.414,y2:11.96,gradientUnits:"userSpaceOnUse"},(0,o.createElement)("stop",{stopColor:"#EE006B"}),(0,o.createElement)("stop",{offset:1,stopColor:"#B300BE"})),(0,o.createElement)("linearGradient",{id:"arrow-up-right_svg__paint1_linear_285_198",x1:10.311,y1:3.071,x2:20.414,y2:11.96,gradientUnits:"userSpaceOnUse"},(0,o.createElement)("stop",{stopColor:"#EE006B"}),(0,o.createElement)("stop",{offset:1,stopColor:"#B300BE"}))))},B=[{title:(0,d.__)("Tutorials",s.i18n),subtitle:(0,d.__)("Get to know the plugin and start your journey with our brand new Stackable Courses.",s.i18n),link:"https://wpstackable.com/learn/?utm_source=plugin&utm_medium=getting_started&utm_campaign=tutorial_button",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M2 3h6a4 4 0 014 4v14a3 3 0 00-3-3H2V3zM22 3h-6a4 4 0 00-4 4v14a3 3 0 013-3h7V3z",stroke:"#101828",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}))}),null)},{title:(0,d.__)("Documentation",s.i18n),subtitle:(0,d.__)("Visit our knowledge base for troubleshooting, guides, FAQs and updates.",s.i18n),link:"https://docs.wpstackable.com/",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z",stroke:"#101828",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.createElement)("path",{d:"M14 2v6h6",stroke:"#101828",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}),(0,o.createElement)("path",{d:"M16 13H8M16 17H8M10 9H8",stroke:"#000",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}))}),null)},{title:(0,d.__)("Community",s.i18n),subtitle:(0,d.__)("Join our very active Stackable Community on Facebook.",s.i18n),link:"https://www.facebook.com/groups/wpstackable/",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2M12 11a4 4 0 100-8 4 4 0 000 8z",stroke:"#101828",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"}))}),null)}],P=[{title:(0,d.__)("The Basics of Stackable Blocks",s.i18n),subtitle:(0,d.__)("Learn how to personalize and tailor Stackable Blocks to match your website's unique style and design.",s.i18n),src:"https://www.youtube.com/embed/GsQxH_gDp1A",url:"#"},{title:(0,d.__)("Flexbox Controls",s.i18n),subtitle:(0,d.__)("Explore the powerful Flexbox controls in Stackable that allow you to effortlessly create dynamic and responsive layouts.",s.i18n),src:"https://www.youtube.com/embed/73N9uXnpUJE",url:"#"},{title:(0,d.__)("Introduction to Hover Styles",s.i18n),subtitle:(0,d.__)("Discover the exciting world of hover styles in Stackable and learn how to add interactive and engaging effects to your website.",s.i18n),src:"https://www.youtube.com/embed/6vSxPN3kOWY",url:"#"},{title:(0,d.__)("How to Use Image Settings",s.i18n),subtitle:(0,d.__)("Master the art of optimizing and enhancing images using Stackable's intuitive image settings for a visually captivating website.",s.i18n),src:"https://www.youtube.com/embed/SS60OM8hQBo",url:"#"},{title:(0,d.__)("Introduction to the Design Library",s.i18n),subtitle:(0,d.__)("Dive into the fundamentals of the Stackable Design Library and learn how to streamline your website development process with ease.",s.i18n),src:"https://www.youtube.com/embed/zJBQ7CRgzHI",url:"#"},{title:(0,d.__)("All About Typography in Stackable",s.i18n),subtitle:(0,d.__)("Unlock the full potential of typography in Stackable and gain insights on how to create visually stunning and impactful text designs.",s.i18n),src:"https://www.youtube.com/embed/dvISiVMmPDQ",url:"#"}],D=e=>{let{title:t,subtitle:n,link:a,icon:l}=e;return(0,o.createElement)("a",{href:a,className:"s-card s-card-link",target:"_blank",rel:"noreferrer"},(0,o.createElement)("div",{className:"s-icon-wrapper"}," ",l," "),(0,o.createElement)("h3",{className:"s-card-title"}," ",t," "),(0,o.createElement)("p",{className:"s-card-subtitle"}," ",n," "),(0,o.createElement)("div",{className:"s-bottom-icon-wrapper"}," ",(0,o.createElement)(O,null)," "))},L=e=>{let{title:t,subtitle:n,src:a}=e;return(0,o.createElement)("div",{className:"s-card"},(0,o.createElement)("div",{className:"s-video-wrapper s-card-top"},(0,o.createElement)("iframe",{className:"s-video",src:a,title:t,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture;",allowFullScreen:!0})),(0,o.createElement)("h3",{className:"s-card-title"}," ",t," "),(0,o.createElement)("p",{className:"s-card-subtitle"}," ",n," "))},R=()=>(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"s-body"},(0,o.createElement)("div",{className:"s-getting-started__centered"},(0,o.createElement)("h2",{className:"title"}," ",(0,d.__)("Unleash the Full Potential of the WordPress Block Editor by Turning It into a Page Builder",s.i18n)," "),(0,o.createElement)("p",{className:"subtitle"},(0,d.__)("Learn the essentials in just a few minutes by watching this video. Scroll down to see more quick tutorials.",s.i18n)," "),(0,o.createElement)("div",{className:"s-video-wrapper s-getting-started-video"},(0,o.createElement)("iframe",{className:"s-video",src:"https://www.youtube.com/embed/WP2LHxGulps",title:(0,d.__)("Getting Started",s.i18n),allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture;",allowFullScreen:!0})),(0,o.createElement)("div",{className:"s-button-container"},(0,o.createElement)("a",{href:"/wp-admin/post-new.php?post_type=page",target:"_new",className:"s-button s-secondary-button uppercase"},(0,d.__)("Create a new page",s.i18n)))),(0,o.createElement)("div",{className:"s-getting-started__section"},(0,o.createElement)("div",{className:"s-card-container s-card-general"},B.map(((e,t)=>(0,o.createElement)(D,c({},e,{key:t})))))),(0,o.createElement)("div",{className:"s-getting-started__section"},(0,o.createElement)("div",{className:"s-card-header"},(0,o.createElement)("h2",null," ",(0,d.__)("Learn the essentials",s.i18n)," ")),(0,o.createElement)("div",{className:"s-card-container s-card-essentials"},P.map(((e,t)=>(0,o.createElement)(L,c({},e,{key:t})))))),(0,o.createElement)("div",{className:"s-getting-started__footer-banner"},(0,o.createElement)("div",{className:"s-banner-wrapper"},(0,o.createElement)("div",{className:"s-banner-content"},(0,o.createElement)("h2",null," ",(0,d.__)("Check out our library of tutorials and guides",s.i18n)," ")),(0,o.createElement)("div",{className:"s-button-container"},(0,o.createElement)("a",{href:"https://wpstackable.com/learn/?utm_source=plugin&utm_medium=getting_started&utm_campaign=tutorial_button",target:"_blank",rel:"noreferrer",className:"s-button s-secondary-button"},(0,d.__)("Go to Stackable Learn",s.i18n))))))),A={ru_RU:"cyrillic",bg_BG:"cyrillic",he_IL:"hebrew",el:"greek",vi:"vietnamese",uk:"cyrillic",cs_CZ:"latin-ext",ro_RO:"latin-ext",pl_PL:"latin-ext"},H={"Sans-Serif":{value:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',label:(0,d.__)("Sans-Serif",s.i18n)},Serif:{value:'"Palatino Linotype", Palatino, Palladio, "URW Palladio L", "Book Antiqua", Baskerville, "Bookman Old Style", "Bitstream Charter", "Nimbus Roman No9 L", Garamond, "Apple Garamond", "ITC Garamond Narrow", "New Century Schoolbook", "Century Schoolbook", "Century Schoolbook L", Georgia, serif',label:(0,d.__)("Serif",s.i18n)},"Serif-Alt":{value:'Constantia, Lucida Bright, Lucidabright, "Lucida Serif", Lucida, "DejaVu Serif", "Bitstream Vera Serif", "Liberation Serif", Georgia, serif',label:(0,d.__)("Serif Alternative",s.i18n)},Monospace:{value:'SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace',label:(0,d.__)("Monospace",s.i18n)}},F={"modern-stack-system-ui":{value:"system-ui, sans-serif",label:(0,d.__)("System UI",s.i18n)},"modern-stack-transitional":{value:"Charter, 'Bitstream Charter', 'Sitka Text', Cambria, serif",label:(0,d.__)("Transitional",s.i18n)},"modern-stack-old-style":{value:"'Iowan Old Style', 'Palatino Linotype', 'URW Palladio L', P052, serif",label:(0,d.__)("Old Style",s.i18n)},"modern-stack-humanist":{value:"Seravek, 'Gill Sans Nova', Ubuntu, Calibri, 'DejaVu Sans', source-sans-pro, sans-serif",label:(0,d.__)("Humanist",s.i18n)},"modern-stack-geometric-humanist":{value:"Avenir, Montserrat, Corbel, 'URW Gothic', source-sans-pro, sans-serif",label:(0,d.__)("Geometric Humanist",s.i18n)},"modern-stack-classical-humanist":{value:"Optima, Candara, 'Noto Sans', source-sans-pro, sans-serif",label:(0,d.__)("Classical Humanist",s.i18n)},"modern-stack-neo-grotesque":{value:"Inter, Roboto, 'Helvetica Neue', 'Arial Nova', 'Nimbus Sans', Arial, sans-serif",label:(0,d.__)("Neo Grotesque",s.i18n)},"modern-stack-monospace-slab-serif":{value:"'Nimbus Mono PS', 'Courier New', monospace",label:(0,d.__)("Monospace Slab Serif",s.i18n)},"modern-stack-monospace-code":{value:"ui-monospace, 'Cascadia Code', 'Source Code Pro', Menlo, Consolas, 'DejaVu Sans Mono', monospace",label:(0,d.__)("Monospace Code",s.i18n)},"modern-stack-industrial":{value:"Bahnschrift, 'DIN Alternate', 'Franklin Gothic Medium', 'Nimbus Sans Narrow', sans-serif-condensed, sans-serif",label:(0,d.__)("Industrial",s.i18n)},"modern-stack-rounded-sans":{value:"ui-rounded, 'Hiragino Maru Gothic ProN', Quicksand, Comfortaa, Manjari, 'Arial Rounded MT', 'Arial Rounded MT Bold', Calibri, source-sans-pro, sans-serif",label:(0,d.__)("Rounded Sans",s.i18n)},"modern-stack-slab-serif":{value:"Rockwell, 'Rockwell Nova', 'Roboto Slab', 'DejaVu Serif', 'Sitka Small', serif",label:(0,d.__)("Slab Serif",s.i18n)},"modern-stack-antique":{value:"Superclarendon, 'Bookman Old Style', 'URW Bookman', 'URW Bookman L', 'Georgia Pro', Georgia, serif",label:(0,d.__)("Antique",s.i18n)},"modern-stack-didone":{value:"Didot, 'Bodoni MT', 'Noto Serif Display', 'URW Palladio L', P052, Sylfaen, serif",label:(0,d.__)("Didone",s.i18n)},"modern-stack-handwritten":{value:"'Segoe Print', 'Bradley Hand', Chilanka, TSCu_Comic, casual, cursive",label:(0,d.__)("Handwritten",s.i18n)}},V=e=>`https://fonts.googleapis.com/css?family=${e.replace(/ /g,"+")}:100,100italic,200,200italic,300,300italic,400,400italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic${A[s.locale]?`&subset=${A}`:""}`,j=e=>!!e&&!Object.keys(H).includes(e)&&!Object.keys(F).includes(e),z=e=>{const t=t=>{if(t&&j(e)){if(U(e,t))return;const n=$(e);t.appendChild(n)}};let n=0;const o=setInterval((()=>{n++,8===n&&clearInterval(o);const e=document.querySelector('iframe[name="editor-canvas"]')?document.querySelector('iframe[name="editor-canvas"]').contentWindow.document.querySelector("head"):document.querySelector("head");t(e),e!==document.querySelector("head")&&t(document.querySelector("head"))}),250)},$=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";const t=document.createElement("link");return t.classList.add("ugb-google-fonts"),t.setAttribute("data-font-name",e),t.setAttribute("href",V(e)),t.setAttribute("rel","stylesheet"),t.setAttribute("type","text/css"),t},U=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.querySelector("head");return t.querySelector(`[data-font-name="${e}"]`)},q=e=>{const t='-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';return e?e.match(/^\s*var\(--/)?e:F[e]?F[e].value:j(e)?`"${e}", Sans-serif`:H[e]?H[e].value:t:t},G=e=>{Object.keys(e).filter((e=>e.match(/fontfamily/i))).forEach((t=>{const n=e[t];n&&z(n)}))};var W=n(4247),K=n.n(W),J=n(4863),Y=n.n(J);const X=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"%s",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return(0,v.camelCase)((0,d.sprintf)(e,(0,v.upperFirst)(t),(0,v.upperFirst)(n)))},Q=e=>function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return X(e,t,n)},Z=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desktop",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"normal";const o="desktop"===t.toLowerCase()?"":(0,v.upperFirst)(t),a="normal"===n?"":"hover"===n?"Hover":"collapsed"===n?"Collapsed":"ParentHover";return null!=e&&e.includes("%s")?(0,d.sprintf)(e,`${o}${a}`):`${e}${o}${a}`},ee=e=>"object"==typeof e?Object.values(e).every((e=>ee(e))):void 0===e||!e&&""===e;n(6537),n(8874),n(2409),n(5985);const te=()=>{const[e,t]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{var e;const n=e=>{t(!!e.shiftKey)};window.addEventListener("keydown",n),window.addEventListener("keyup",n);const o=null===(e=document)||void 0===e?void 0:e.querySelector('iframe[name="editor-canvas"]');return o&&(o.contentDocument||o.contentWindow.document).addEventListener("keyup",n),()=>{var e;window.removeEventListener("keydown",n),window.removeEventListener("keyup",n);const t=null===(e=document)||void 0===e?void 0:e.querySelector('iframe[name="editor-canvas"]');t&&(t.contentDocument||t.contentWindow.document).removeEventListener("keyup",n)}}),[]),e},ne=wp.data,oe=()=>{const{deviceType:e}=(0,ne.useSelect)((e=>{var t,n,o,a,l,r;let i="Desktop";return i=(null===(t=e("core/editor"))||void 0===t||null===(n=t.getDeviceType)||void 0===n?void 0:n.call(t))||(null===(o=e("core/edit-site"))||void 0===o||null===(a=o.__experimentalGetPreviewDeviceType)||void 0===a?void 0:a.call(o))||(null===(l=e("core/edit-post"))||void 0===l||null===(r=l.__experimentalGetPreviewDeviceType)||void 0===r?void 0:r.call(l))||e("stackable/device-type").getDeviceType(),{deviceType:i}}),[]);return e||""};(0,ne.register)((0,ne.createReduxStore)("stackable/device-type",{reducer:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Desktop",t=arguments.length>1?arguments[1]:void 0;return"UPDATE_DEVICE_TYPE"===t.type?t.deviceType:e},actions:{setDeviceType:e=>({type:"UPDATE_DEVICE_TYPE",deviceType:e})},selectors:{getDeviceType:e=>e}}));var ae=n(4063),le=n.n(ae);const re=Symbol(),ie=Symbol();function se(e){const t=(0,o.createContext)({[re]:{v:{current:e},n:{current:-1},l:new Set,u:e=>e()}});var n;return t[ie]=t.Provider,t.Provider=(n=t.Provider,e=>{let{value:t,children:a}=e;const l=(0,o.useRef)(t),r=(0,o.useRef)(0),[i,s]=(0,o.useState)(null);i&&(i(t),s(null));const c=(0,o.useRef)();if(!c.current){const e=new Set,t=(t,n)=>{r.current+=1;const o={n:r.current};null!=n&&n.suspense&&(o.n*=-1,o.p=new Promise((e=>{s((()=>t=>{o.v=t,delete o.p,e(t)}))}))),e.forEach((e=>e(o))),t()};c.current={[re]:{v:l,n:r,l:e,u:t}}}return(0,o.useLayoutEffect)((()=>{l.current=t,r.current+=1,c.current[re].l.forEach((e=>{e({n:r.current,v:t})}))}),[t]),(0,o.createElement)(n,{value:c.current},a)}),delete t.Consumer,t}const ce=(e,t)=>(e=>!Array.isArray(e)&&"object"==typeof e)(e)?le()(e,t):Object.is(e,t);function ue(e,t){const n=(0,o.useContext)(e)[re],{v:{current:a},n:{current:l},l:r}=n,i=t(a),[s,c]=(0,o.useReducer)(((e,n)=>{if(!n)return[a,i];if("p"in n)throw n.p;if(n.n===l)return ce(e[1],i)?e:[a,i];try{if("v"in n){if(ce(e[0],n.v))return e;const o=t(n.v);return ce(e[1],o)?e:[n.v,o]}}catch(e){}return[...e]}),[a,i]);return ce(s[1],i)||c(),(0,o.useLayoutEffect)((()=>(r.add(c),()=>{r.delete(c)})),[r]),s[1]}const de=se({}),pe=(0,o.createContext)((()=>{})),me=se({}),he=e=>e,ge={},ve=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:he;return ue(de,(t=>e(t)))},be=()=>(0,o.useContext)(pe),fe=e=>(0,o.createElement)(de.Provider,{value:e.attributes},(0,o.createElement)(pe.Provider,{value:e.setAttributes},(0,o.createElement)(me.Provider,{value:e.context||ge},e.children))),ke=(wp.tokenList,wp.blockEditor),_e=wp.blocks,ye={selectedBlock:null,hoverState:"normal",hasParentHoverState:!1,selectedParentHoverBlock:null,selectedParentHoverChildren:[],selectedHoverChildren:[],hasCollapsedState:!1,selectedCollapsedBlock:null,selectedCollapsedChildren:[]},we={updateSelectedBlock:(e,t)=>{var n,o;const a=null==t?void 0:t.querySelector(`[data-block="${e}"]`),l=null==a||null===(n=a.closest(".stk-hover-parent"))||void 0===n?void 0:n.closest("[data-block]"),r=(null==l?void 0:l.getAttribute("data-block"))||null,i=Array.from((null==l?void 0:l.querySelectorAll("[data-block]"))||[]).map((e=>e.getAttribute("data-block")))||[],s=Array.from((null==a?void 0:a.querySelectorAll("[data-block]"))||[]).map((e=>e.getAttribute("data-block")))||[],c=(null==a||null===(o=a.closest(".stk-block-accordion"))||void 0===o?void 0:o.closest("[data-block]"))||("stackable/accordion"===(null==a?void 0:a.getAttribute("data-type"))?a:null),u=(null==c?void 0:c.getAttribute("data-block"))||null;return{type:"UPDATE_SELECTED_BLOCK",clientId:e,parentHoverClientId:r,hasParentHoverState:!!r,parentHoverChildrenClientIds:i,hoverChildrenClientIds:s,collapsedClientId:u,collapsedChildrenClientIds:Array.from((null==c?void 0:c.querySelectorAll("[data-block]"))||[]).map((e=>e.getAttribute("data-block")))||[],hasCollapsedState:!!u}},clearSelectedBlock:()=>({type:"CLEAR_SELECTED_BLOCK"}),updateHoverState:e=>({type:"UPDATE_HOVER_STATE",value:e})};(0,ne.register)((0,ne.createReduxStore)("stackable/hover-state",{reducer:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ye,t=arguments.length>1?arguments[1]:void 0;switch(t.type){case"UPDATE_SELECTED_BLOCK":return{...e,selectedBlock:t.clientId,selectedParentHoverBlock:t.parentHoverClientId,hasParentHoverState:t.hasParentHoverState,selectedParentHoverChildren:t.parentHoverChildrenClientIds,selectedHoverChildren:t.hoverChildrenClientIds,hasCollapsedState:t.hasCollapsedState,selectedCollapsedBlock:t.collapsedClientId,selectedCollapsedChildren:t.collapsedChildrenClientIds};case"CLEAR_SELECTED_BLOCK":return{...ye};case"UPDATE_HOVER_STATE":return{...e,hoverState:t.value}}return e},actions:we,selectors:{getSelectedBlock:e=>e.selectedBlock,getHoverState:e=>e.hoverState,getHasParentHoverState:e=>e.hasParentHoverState,getSelectedParentHoverBlock:e=>e.selectedParentHoverBlock,getSelectedParentHoverBlockChildren:e=>e.selectedParentHoverChildren,getSelectedHoverChildren:e=>e.selectedHoverChildren,getHasCollapsedState:e=>e.hasCollapsedState,getSelectedCollapsedBlock:e=>e.selectedCollapsedBlock,getSelectedCollapsedBlockChildren:e=>e.selectedCollapsedChildren}}));const Ee=function(){let{forceUpdateHoverState:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{clientId:t}=(0,ke.useBlockEditContext)(),n=(0,ne.useSelect)((e=>e("core/block-editor").getMultiSelectedBlockClientIds())),{currentHoverState:o,blockHoverClass:a,hasParentHoverState:l,hasCollapsedState:r,isCollapsedBlock:i}=(0,ne.useSelect)((o=>{const a=o("stackable/hover-state").getHoverState(),l=o("stackable/hover-state").getSelectedBlock(),{getSelectedParentHoverBlock:r,getSelectedParentHoverBlockChildren:i,getSelectedHoverChildren:s,getHasParentHoverState:c,getHasCollapsedState:u,getSelectedCollapsedBlock:d,getSelectedCollapsedBlockChildren:p}=o("stackable/hover-state"),m=c(),h=r(),g=u(),v=d(),b=t===l||n.includes(t),f=t===v;let k="",_="normal";if(b)"hover"!==a&&"parent-hover"!==a||(k="stk--is-hovered"),_=a,m||"parent-hover"!==a||(_="hover");else if(t===h)"hover"!==a&&"parent-hover"!==a||(k="stk--is-hovered",_="hover");else{const e=i(),n=s(),o=p(),l=e.includes(t),r=n.includes(t),c=o.includes(t);l||r?"hover"!==a&&"parent-hover"!==a||(k="stk--is-hovered",_="parent-hover"):(c||f)&&(_="collapsed")}return e&&(_=a),{currentHoverState:_,blockHoverClass:k,hasParentHoverState:m,hasCollapsedState:g,isCollapsedBlock:f}}),[t,n]);return[o,a,l,r,i]},Se=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"all",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"all";const o=oe(),[a]=Ee();let l="desktop";("all"===t||Array.isArray(t)&&t.includes(o))&&(l=o);let r="normal";return("all"===n||Array.isArray(n)&&n.includes(a))&&(r=a),Z(e,l,r)},xe=e=>{const[t,n]=(0,o.useState)(e),[a,l]=(0,o.useState)(e);return le()(e,a)||(l(e),n(e)),[t,n]},Ce=wp.url,Te=wp.apiFetch;var Me=n.n(Te);(0,d.__)("URL",s.i18n);const Ne=e=>(0,o.createElement)("path",e),Ie=e=>{let{className:t,isPressed:n,...a}=e;const l={...a,className:g()(t,{"is-pressed":n})||void 0,role:"img","aria-hidden":!0,focusable:!1};return(0,o.createElement)("svg",l)},Oe=((0,o.createElement)(Ie,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,o.createElement)(Ne,{d:"M6.734 16.106l2.176-2.38-1.093-1.028-3.846 4.158 3.846 4.157 1.093-1.027-2.176-2.38h2.811c1.125 0 2.25.03 3.374 0 1.428-.001 3.362-.25 4.963-1.277 1.66-1.065 2.868-2.906 2.868-5.859 0-2.479-1.327-4.896-3.65-5.93-1.82-.813-3.044-.8-4.806-.788l-.567.002v1.5c.184 0 .368 0 .553-.002 1.82-.007 2.704-.014 4.21.657 1.854.827 2.76 2.657 2.76 4.561 0 2.472-.973 3.824-2.178 4.596-1.258.807-2.864 1.04-4.163 1.04h-.02c-1.115.03-2.229 0-3.344 0H6.734z"})),(0,d.__)("Opens in new tab",s.i18n),(0,d.__)("Nofollow link",s.i18n),(0,d.__)("Sponsored",s.i18n),(0,d.__)("UGC",s.i18n),e=>{const{value:t,options:n}=e,[a,l]=(0,o.useState)(!1),r=(0,o.useRef)(null),i=(0,o.useMemo)((()=>e.options.findIndex((e=>e.value===t))/n.length*100),[n,t]);if((0,o.useEffect)((()=>{const e=e=>{var t;a&&((null===(t=e.target)||void 0===t?void 0:t.closest(".stk-label-unit-toggle"))!==r.current&&l(!1))};return document.body.addEventListener("click",e),()=>document.body.removeEventListener("click",e)}),[a,r.current]),n.length<=1)return null;const s=g()([e.className,"stk-label-unit-toggle"],{"stk-label-unit-toggle__colored":e.hasColors,"is-open":a});return(0,o.createElement)("div",{className:s,"aria-expanded":a,ref:r},(0,o.createElement)("div",{className:g()("stk-label-unit-toggle__wrapper",{"is-open":a}),style:{transform:`translateY(-${i}%)`}},n.length>1&&n.map(((n,r)=>{const i=n.label||n.value,s=t===n.value,c=g()({"is-active":s,"has-value":n.hasValue});return(0,o.createElement)(M,{key:r,className:c,"data-index":r,"data-value":n.value,disabled:n.disabled,tabIndex:s?"0":"-1",onClick:()=>{a?(e.onChange(n.value),l(!1)):l(!0)},icon:n.icon,label:e.hasLabels?i:"","aria-haspopup":"true",tooltipPosition:"middle right",onKeyDown:e=>{const t=e.target;if(t&&a){if("ArrowUp"===e.key||"ArrowRight"===e.key||"ArrowDown"===e.key||"ArrowLeft"===e.key)if(e.preventDefault(),"ArrowUp"===e.key||"ArrowLeft"===e.key){const e=t.previousElementSibling||t.parentElement.lastElementChild;e&&e.focus()}else{const e=t.nextElementSibling||t.parentElement.firstElementChild;e&&e.focus()}"Tab"===e.key&&l(!1),"Escape"===e.key&&(e.preventDefault(),l(!1))}}},n.icon?void 0:i)}))))});Oe.defaultProps={className:"",value:"",options:[],onChange:null,labelPosition:"right",buttonLabel:"",hasLabels:!0,hasColors:!0};const Be=(0,o.memo)(Oe),Pe={desktop:"Desktop",tablet:"Tablet",mobile:"Mobile"},De=[{label:(0,d.__)("Desktop",s.i18n),value:"desktop",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M14.5 2.4h-13c-.4 0-.8.3-.8.8v7c0 .*******.8h5.8v1.3H5.6c-.4 0-.8.3-.8.8s.3.8.8.8h4.8c.4 0 .8-.3.8-.8s-.3-.8-.8-.8H8.8V11h5.8c.4 0 .8-.3.8-.8v-7c-.2-.5-.5-.8-.9-.8zm-.7 7H2.2V3.9h11.5v5.5z"}))}),null)},{label:(0,d.__)("Tablet",s.i18n),value:"tablet",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M13 14.8H3c-.4 0-.8-.3-.8-.8V2c0-.4.3-.8.8-.8h10c.4 0 .8.3.8.8v12c0 .4-.4.8-.8.8zm-9.2-2.6h8.5V2.8H3.8v9.4z"}))}),null)},{label:(0,d.__)("Mobile",s.i18n),value:"mobile",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M11 14.2H5c-.4 0-.8-.3-.8-.8v-11c0-.4.3-.8.8-.8h6c.4 0 .8.3.8.8v11c0 .5-.4.8-.8.8zm-5.2-2h4.5v-9H5.8v9z"}))}),null)}],Le=["normal","hover","parent-hover","collapsed"].filter((e=>"normal"!==e)).map((e=>(0,v.upperFirst)((0,v.camelCase)(e)))),Re=e=>{const t=oe(),n=De.filter((t=>{var n;let{value:o}=t;return null===(n=e.screens)||void 0===n?void 0:n.includes(o)})),a=ve((t=>{const n=e.valueCheckAttribute||e.attribute;if(!n)return{};const o=[t[`${n}Tablet`]];Le.forEach((e=>{o.push(t[`${n}Tablet${e}`])}));const a=[t[`${n}Mobile`]];return Le.forEach((e=>{a.push(t[`${n}Mobile${e}`])})),{tablet:o,mobile:a}})),l=n.map((t=>{if("desktop"===t.value)return t;let n=!1;e.attribute&&(n=!a[t.value].every((e=>ee(e))));const o="desktop"!==t.value&&("tablet"===t.value?e.hasTabletValue:e.hasMobileValue);return{...t,hasValue:o||n}}));return l<=1?null:t?(0,o.createElement)(Be,{className:"stk-control-responsive-toggle",value:t.toLowerCase(),options:l,onChange:e=>{(0,ne.dispatch)("core/edit-site")&&(0,ne.dispatch)("core/edit-site").__experimentalSetPreviewDeviceType?(0,ne.dispatch)("core/edit-site").__experimentalSetPreviewDeviceType(Pe[e]):(0,ne.dispatch)("core/edit-post")&&(0,ne.dispatch)("core/edit-post").__experimentalSetPreviewDeviceType?(0,ne.dispatch)("core/edit-post").__experimentalSetPreviewDeviceType(Pe[e]):(0,ne.dispatch)("stackable/device-type").setDeviceType(Pe[e])}}):null};Re.defaultProps={screens:["desktop"],attribute:"",suffix:"",hasTabletValue:void 0,hasMobileValue:void 0};const Ae=(0,o.memo)(Re),He=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{className:"state-parent-hover_svg__st0",d:"M9.8 15.8c-.3 0-.6-.2-.7-.5l-3.8-9c-.1-.3 0-.6.2-.8.2-.2.5-.3.8-.2l9 3.8c.3.1.5.4.5.7 0 .3-.2.6-.5.7l-3.6 1.2-1.2 3.6c-.1.2-.4.4-.7.5zM7.4 7.4l2.3 5.5.7-2c.1-.2.2-.4.5-.5l2-.7-5.5-2.3z"}),(0,o.createElement)("path",{className:"state-parent-hover_svg__st0",d:"M6.4 12.8H2.2V2.2h11.5v4.5l1.5.6V1.5c0-.4-.3-.8-.8-.8h-13c-.4 0-.8.3-.8.8v12c0 .*******.8H7l-.6-1.5z"}))},Fe=[{label:(0,d.__)("Normal State",s.i18n),value:"normal",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{className:"state-normal_svg__st0",d:"M7 14.8c-.3 0-.6-.2-.7-.5l-5-12c-.1-.3 0-.6.2-.8.2-.2.5-.3.8-.2l12 5c.3.1.5.4.5.7 0 .3-.2.6-.5.7L9.4 9.4l-1.7 4.9c-.1.2-.4.4-.7.5zM3.4 3.4l3.5 8.5L8 8.6c.1-.2.2-.4.5-.5L11.8 7 3.4 3.4z"}))}),null)},{label:(0,d.__)("Hovered State",s.i18n),value:"hover",icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{className:"state-hover_svg__st0",d:"M9.2 15.8c-.3 0-.6-.2-.7-.5l-4.2-10c-.1-.3 0-.6.2-.8.2-.2.5-.3.8-.2l10 4.2c.3.1.5.4.5.7 0 .3-.2.6-.5.7l-4 1.4-1.4 4c-.1.2-.4.4-.7.5zM6.4 6.4l2.7 6.5.8-2.5c.1-.2.2-.4.5-.5l2.5-.8-6.5-2.7z"}),(0,o.createElement)("path",{className:"state-hover_svg__st0",d:"M3.7 7.6C2.7 7.2 2 6.2 2 5c0-1.6 1.3-3 3-3 1.2 0 2.2.7 2.7 1.7l1.7.7C9.1 2.2 7.3.5 5 .5 2.5.5.5 2.5.5 5c0 2.3 1.7 4.1 3.9 4.4l-.7-1.8z"}))}),null)},{label:(0,d.__)("Parent Container Hovered State",s.i18n),value:"parent-hover",icon:(0,o.createElement)(He,null)},{label:(0,d.__)("Collapsed",s.i18n),value:"collapsed",icon:(0,o.createElement)(He,null)}],Ve=["normal","hover","parent-hover","collapsed"],je=Ve.map((e=>(0,v.upperFirst)((0,v.camelCase)(e)))),ze=e=>{const[t,n,a,l,r]=Ee({forceUpdateHoverState:e.forceUpdateHoverState}),i=oe(),c=ve((t=>e.attribute?Ve.reduce(((n,o,a)=>({...n,[o]:t[`${e.attribute}${e.hasResponsive&&"Desktop"!==i?i:""}${je[a]}`]})),{}):{})),u="all"===e.hover?Ve:e.hover,p=Fe.filter((e=>{let{value:t}=e;return!(!l&&"collapsed"===t&&!r)&&u.includes(t)})),m=!e.forceUpdateHoverState&&!a,h=p.map((t=>"parent-hover"===t.value?{disabled:!e.forceUpdateHoverState&&!a,tooltip:m?(0,o.createElement)("span",{className:"stk-tooltip__text"},(0,d.sprintf)("%s - %s",(0,d.__)("Parent Hovered",s.i18n),(0,d.__)("Add a Container Background to a parent block to enable this state.",s.i18n)),(0,o.createElement)("br",null),(0,o.createElement)("a",{href:"https://docs.wpstackable.com/article/465-how-to-style-the-different-block-hover-states?utm_source=wp-settings-global-settings&utm_campaign=learnmore&utm_medium=wp-dashboard",target:"_docs"},(0,d.__)("Learn more",s.i18n))):void 0,...t}:t)).map((t=>{var n,o;return e.forceUpdateHoverState?{...t,hasValue:null!==(n=null===(o=e.hasHoverStateValue)||void 0===o?void 0:o[t.value])&&void 0!==n&&n}:"normal"!==t.value&&e.attribute?{...t,hasValue:!ee(c[t.value])}:t}));return(0,o.createElement)(Be,{value:t,options:h,onChange:e=>(0,ne.dispatch)("stackable/hover-state").updateHoverState(e)})};ze.defaultProps={hover:!1,attribute:"",hasResponsive:!1,forceUpdateHoverState:!1,hasHoverStateValue:void 0};const $e=(0,o.memo)(ze),Ue=e=>{const{uniqueId:t,selector:n="",highlight:a="",value:l="",defaultValue:r="",responsive:i="all"}=e||{},s=`.editor-styles-wrapper.editor-styles-wrapper .stk-${t} ${n.replace(/%s/g,t)}`.trim(),{getEditorDom:c}=(0,ne.useSelect)("stackable/editor-dom"),u=oe(),[d,p]=(0,o.useState)(0),m=(0,o.useMemo)((()=>{var e;const t=s.split(",")[0],n=null===(e=c())||void 0===e?void 0:e.querySelector(t);return n||setTimeout((()=>{p&&p(d+1)}),50),n?window.getComputedStyle(n):{}}),[s,d])||{};if("all"!==i&&!i.includes(u.toLowerCase()))return null;const h=Je(a,s,l,r,m);return(0,o.createElement)("style",null,h)};Ue.defaultProps={uniqueId:"",highlightStyles:{}};const qe=Ue,Ge="var(--wp-components-color-accent, var(--wp-admin-theme-color, #007cba))",We="rgba(220, 158, 93, 0.5)",Ke="rgba(220, 158, 93, 0.25)",Je=(e,t,n,o,a)=>{switch(e){case"row-gap":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t${t} > [data-type]:not(:nth-last-child(2))::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 2;\n\t\t\t\ttop: 100%;\n\t\t\t\tleft: 0;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: max(${parseInt(a.rowGap,10)||0}px, 1px);\n\t\t\t\tbackground: ${We};\n\t\t\t\tpointer-events: none;\n\t\t\t}`;case"column-gap":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t${t} > [data-type]:not(:nth-last-child(2))::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 2;\n\t\t\t\tleft: 100%;\n\t\t\t\ttop: 0;\n\t\t\t\twidth: max(${parseInt(a.columnGap,10)||0}px, 1px);\n\t\t\t\theight: 100%;\n\t\t\t\tbackground: ${We};\n\t\t\t\tpointer-events: none;\n\t\t\t}`;case"columns:column-gap":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t${t} > [data-type="stackable/column"]:not(:last-child)::before {\n\t\t\t\tcontent: '';\n\t\t\t\tposition: absolute;\n\t\t\t\tz-index: 2;\n\t\t\t\tleft: 100%;\n\t\t\t\ttop: 0;\n\t\t\t\twidth: max(${parseInt(a.columnGap,10)||0}px, 1px);\n\t\t\t\theight: 100%;\n\t\t\t\tbackground: ${We};\n\t\t\t\tpointer-events: none;\n\t\t\t}`;case"padding":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t${t}::after {\n\t\t\t\tall: unset;\n\t\t\t\tcontent: '' !important;\n\t\t\t\tz-index: 2;\n\t\t\t\tposition: absolute;\n\t\t\t\tinset: 0;\n\t\t\t\tborder-style: solid;\n\t\t\t\tborder-color: rgba(162, 201, 68, 0.5);\n\t\t\t\tborder-top-width: max(${a.paddingTop}, 1px);\n\t\t\t\tborder-right-width: max(${a.paddingRight}, 1px);\n\t\t\t\tborder-bottom-width: max(${a.paddingBottom}, 1px);\n\t\t\t\tborder-left-width: max(${a.paddingLeft}, 1px);\n\t\t\t\tbackground-color: transparent !important;\n\t\t\t}`;case"margin":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t\toverflow: visible !important;\n\t\t\t}\n\t\t\t${t}::after {\n\t\t\t\tall: unset;\n\t\t\t\tcontent: '' !important;\n\t\t\t\tz-index: 2;\n\t\t\t\tposition: absolute;\n\t\t\t\tinset: 0;\n\t\t\t\tborder-style: solid;\n\t\t\t\tborder-top-color: ${parseInt(a.marginTop,10)<0?Ke:We};\n\t\t\t\tborder-right-color: ${parseInt(a.marginRight,10)<0?Ke:We};\n\t\t\t\tborder-bottom-color: ${parseInt(a.marginBottom,10)<0?Ke:We};\n\t\t\t\tborder-left-color: ${parseInt(a.marginLeft,10)<0?Ke:We};\n\t\t\t\tborder-top-width: max(${a.marginTop}, calc(${a.marginTop} * -1), 1px);\n\t\t\t\tborder-right-width: max(${a.marginRight}, calc(${a.marginRight} * -1), 1px);\n\t\t\t\tborder-bottom-width: max(${a.marginBottom}, calc(${a.marginBottom} * -1), 1px);\n\t\t\t\tborder-left-width: max(${a.marginLeft}, calc(${a.marginLeft} * -1), 1px);\n\t\t\t\ttop: calc(max(${a.marginTop}, 1px) * -1);\n\t\t\t\tright: calc(max(${a.marginRight}, 1px) * -1);\n\t\t\t\tbottom: calc(max(${a.marginBottom}, 1px) * -1);\n\t\t\t\tleft: calc(max(${a.marginLeft}, 1px) * -1);\n\t\t\t\tbackground-color: transparent !important;\n\t\t\t}`;case"column-spacing":return`${t} {\n\t\t\t\tposition: relative;\n\t\t\t}\n\t\t\t${t}::after {\n\t\t\t\tall: unset;\n\t\t\t\tcontent: '';\n\t\t\t\tz-index: 2;\n\t\t\t\tposition: absolute;\n\t\t\t\twidth: 100%;\n\t\t\t\theight: 100%;\n\t\t\t\tborder-style: solid;\n\t\t\t\tborder-color: ${We};\n\t\t\t\ttop: calc(max(var(--column-spacing-top, var(--stk-columns-spacing, ${o})), 1px) * -1);\n\t\t\t\tborder-top-width: max(var(--column-spacing-top, var(--stk-columns-spacing, ${o})), 1px);\n\t\t\t\tright: calc(max(var(--column-spacing-right, var(--stk-columns-spacing, ${o})), 1px) * -1);\n\t\t\t\tborder-right-width: max(var(--column-spacing-right, var(--stk-columns-spacing, ${o})), 1px);\n\t\t\t\tbottom: calc(max(var(--column-spacing-bottom, var(--stk-columns-spacing, ${o})), 1px) * -1);\n\t\t\t\tborder-bottom-width: max(var(--column-spacing-bottom, var(--stk-columns-spacing, ${o})), 1px);\n\t\t\t\tleft: calc(max(var(--column-spacing-left, var(--stk-columns-spacing, ${o})), 1px) * -1);\n\t\t\t\tborder-left-width: max(var(--column-spacing-left, var(--stk-columns-spacing, ${o})), 1px);\n\t\t\t\tbackground-color: transparent !important;\n\t\t\t}`;case"outline-first-offset":return`${t} {\n\t\t\t\toutline: 1px dashed ${Ge};\n\t\t\t}\n\t\t\t${(0,v.first)(t.split(","))} {\n\t\t\t\toutline-offset: 8px;\n\t\t\t}`;case"outline-second-offset":return`${t} {\n\t\t\t\toutline: 1px dashed ${Ge};\n\t\t\t}\n\t\t\t${(0,v.last)(t.split(","))} {\n\t\t\t\toutline-offset: 8px;\n\t\t\t}`;default:return`${t} {\n\t\t\t\toutline: 1px dashed ${Ge};\n\t\t\t}`}},Ye=wp.compose,Xe=(0,o.createContext)(null),Qe=((0,Ye.createHigherOrderComponent)((e=>t=>{const n=t.attributes.uniqueId,[a,l]=Oi(null);return(0,o.useEffect)((()=>{t.isSelected||l(null)}),[t.isSelected]),(0,o.createElement)(Xe.Provider,{value:l},a&&(0,o.createElement)(qe,c({uniqueId:n},a||{})),(0,o.createElement)(e,c({className:g()(t.className,{"stk-has-visual-guide":!!a})},t)))}),"withVisualGuideContext"),e=>{const[t,n]=(0,o.useState)(!1),a=(0,o.useRef)(!1),l=(0,o.useContext)(Xe);return(0,o.useEffect)((()=>{!t&&l&&l(null)}),[t]),l?(t&&l(e),(0,o.createElement)("div",{className:"stk-visual-guideer-trigger",onMouseEnter:()=>{var e,t,o,l;a.current=!1,setTimeout((()=>{!a.current&&n&&n(!0)}),10),null!==(e=document)&&void 0!==e&&null!==(t=e.activeElement)&&void 0!==t&&null!==(o=t.getAttribute("data-type"))&&void 0!==o&&o.startsWith("stackable/")&&(null===(l=document.activeElement)||void 0===l||l.blur())},onMouseLeave:()=>{a.current=!0,n(!1)}},e.children)):e.children}),Ze={"inner-block-padding":"dist/videos/help/advanced-column-paddings.mp4","column-gap":"dist/videos/help/advanced-column-gap.mp4","advanced-block-paddings":"dist/videos/help/advanced-block-paddings.mp4","image-shape":"dist/videos/help/image-shape.mp4","image-size":"dist/videos/help/image-size-no-crop.mp4","image-border-radius":"dist/videos/help/image-border-radius.mp4","advanced-opacity":"dist/videos/help/advanced-opacity.mp4","advanced-zindex":"dist/videos/help/advanced-zindex.mp4","content-horizontal-align":"dist/videos/help/advanced-block-horizontal-align.mp4","block-height":"dist/videos/help/advanced-block-height.mp4","advanced-block-margin":"dist/videos/help/advanced-block-margins.mp4","content-vertical-align":"dist/videos/help/advanced-column-content-vertical-align.mp4","column-vertical-align":"dist/videos/help/advanced-block-vertical-align.mp4","max-content-width":"dist/videos/help/advanced-block-content-width.mp4","gradient-location":"dist/videos/help/gradient-location.mp4","background-color-opacity":"dist/videos/help/background-color-opacity.mp4","background-blend-mode":"dist/videos/help/background-blend-mode.mp4","background-tint":"dist/videos/help/background-tint.mp4","background-fixed":"dist/videos/help/background-fixed.mp4","background-image-position":"dist/videos/help/background-image-position.mp4","background-image-repeat":"dist/videos/help/background-image-repeat.mp4","background-image-size":"dist/videos/help/background-image-size.mp4","general-border-radius":"dist/videos/help/general-border-radius.mp4","general-shadow":"dist/videos/help/general-shadow.mp4","typography-family":"dist/videos/help/typography-family.mp4","typography-weight":"dist/videos/help/typography-weight.mp4","typography-transform":"dist/videos/help/typography-transform.mp4","typography-line-height":"dist/videos/help/typography-line-height.mp4","typography-letter-spacing":"dist/videos/help/typography-letter-spacing.mp4","button-hover-effect":"dist/videos/help/button-hover-effect.mp4","image-shadow":"dist/videos/help/image-shadow.mp4","separator-height":"dist/videos/help/separator-height.mp4","separator-width":"dist/videos/help/separator-width.mp4","separator-shadow":"dist/videos/help/separator-shadow.mp4","separator-bring-to-front":"dist/videos/help/separator-bring-to-front.mp4","separator-layer-blend-mode":"dist/videos/help/separator-layer-blend-mode.mp4","accordion-adjacent-open":"dist/videos/help/accordion-adjacent-open.mp4","alignment-all":"dist/videos/help/alignment-all.mp4","posts-content-order":"dist/videos/help/posts-content-order.mp4","posts-meta-separator":"dist/videos/help/posts-meta-separator.mp4"},et=e=>{const t=Ze[e]||"";return`${s.cdnUrl}/${t}`},tt=e=>(0,o.createElement)(r.Popover,c({},e,{className:g()("stk-popover",e.className),onKeyDown:t=>{if(27===t.keyCode)return t.preventDefault(),t.stopPropagation(),void e.onEscape();e.onKeyDown&&e.onKeyDown(t)}}));tt.defaultProps={className:"",onEscape:()=>{}};const nt=tt,ot=()=>{},at=e=>(0,o.createElement)(nt,{className:"stk-control-help-tooltip",title:e.title,placement:"left",offset:28,noArrow:!1,onFocusOutside:e.closeOnEscape?e.onClose:ot,onEscape:e.closeOnEscape?e.onClose:ot},(0,o.createElement)(r.PanelBody,null,(0,o.createElement)("button",{className:"stk-control-help-tooltip__remove",onClick:e.onClose},(0,o.createElement)(r.Dashicon,{icon:"no-alt"})),e.video&&(0,o.createElement)("video",{width:"600",autoPlay:!0,loop:!0,muted:!0,playsInline:!0,src:et(e.video)}),(0,o.createElement)("h4",null,e.title),(0,o.createElement)("p",null,e.description),e.showTooltipCheckbox&&(0,o.createElement)(r.CheckboxControl,{label:(0,d.__)("Stop showing tooltips",s.i18n),className:"ugb-help-tooltip__checkbox",checked:!e.tooltipsEnabled,onChange:t=>e.onTooltipsEnabledChange(!t)})));at.defaultProps={title:"",video:"",description:"",closeOnEscape:!0,onClose:ot,showTooltipCheckbox:!0,tooltipsEnabled:!0,onTooltipsEnabledChange:ot};const lt=at,rt=e=>{const{title:t,label:n,video:a,description:l}=e,[r,i]=(0,o.useState)(!1),[s,c]=function(){const[e,t]=(0,o.useState)(st);return(0,o.useEffect)((()=>{var e;const n=e=>t(e.detail);return null===(e=window)||void 0===e||e.addEventListener("_stkHelpTooltipEnabledChanged",n),()=>{window.removeEventListener("_stkHelpTooltipEnabledChanged",n)}}),[]),[e,e=>{st=e,new p.models.Settings({stackable_help_tooltip_disabled:e?"":"1"}).save(),window.dispatchEvent(new CustomEvent("_stkHelpTooltipEnabledChanged",{detail:e}))}]}(),u=ct(i),d=(0,o.useRef)(),m=(0,o.useRef)();return l&&(s||r)?(0,o.createElement)("div",{className:"components-base-control__label"},(0,o.createElement)("span",{className:"stk-control__label--has-tooltip",onMouseEnter:()=>{clearTimeout(d.current),d.current=setTimeout((()=>{u(!0),setTimeout((()=>{document.querySelector(".stk-control-help-tooltip").focus()}),100)}),800)},onMouseLeave:()=>{clearTimeout(d.current)},onMouseDown:()=>{m.current=r},onClick:()=>{m.current||(clearTimeout(d.current),u((e=>!e)),r||setTimeout((()=>{document.querySelector(".stk-control-help-tooltip").focus()}),100))},onKeyDown:e=>{13!==e.keyCode&&32!==e.keyCode||(e.preventDefault(),u((e=>!e)))},role:"button",tabIndex:"0"},n),r&&(0,o.createElement)(lt,{title:t||n,description:l,video:a,onClose:()=>u(!1),tooltipsEnabled:s,onTooltipsEnabledChange:c})):(0,o.createElement)("div",{className:"components-base-control__label"},n)};rt.defaultProps={label:"",description:"",video:""};const it=rt;let st=!0;l((()=>{f().then((e=>{st="1"!==e.stackable_help_tooltip_disabled})).catch((()=>{}))}));const ct=e=>((0,o.useEffect)((()=>{var t;const n=()=>e(!1);return null===(t=window)||void 0===t||t.addEventListener("_stkHelpTooltipOpened",n),()=>{window.removeEventListener("_stkHelpTooltipOpened",n)}}),[]),t=>{t&&window.dispatchEvent(new CustomEvent("_stkHelpTooltipOpened")),e(t)}),ut=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;const l=be(),r=Se(e,t,n),i=ve((e=>e[r])),s=void 0!==i?i:"";let c=void 0!==i?i:"";o&&(c=o(c));const u=e=>{const t=a?a(e,s):e;l({[r]:t})};return[c,u]},dt=["desktop","tablet","mobile"],pt={},mt=e=>{var t,n,a,l;const i=oe(),u=g()(["stk-control",e.className],{"stk-control--disabled":e.disableTablet&&"Tablet"===i||e.disableMobile&&"Mobile"===i}),p=!(null===(t=e.responsive)||void 0===t||!t.length),m=!(null===(n=e.hover)||void 0===n||!n.length),h=!(null===(a=e.units)||void 0===a||!a.length),v="all"===e.responsive?dt:e.responsive,b=e.units&&(null===(l=e.units)||void 0===l?void 0:l.map((e=>({value:e}))))||[],f=g()(["stk-control-label"],{"stk-control-label--bold":e.boldLabel}),k=e.boldLabel?(0,o.createElement)("h3",null,e.label):e.label,_=e.visualGuide!==pt?Qe:o.Fragment;return(0,o.createElement)(r.BaseControl,{help:e.help,className:u},(0,o.createElement)(_,e.visualGuide,(0,o.createElement)("div",{className:f},(0,o.createElement)(it,c({label:k},e.helpTooltip)),(0,o.createElement)("div",{className:"stk-control-label__toggles"},p&&(0,o.createElement)(Ae,{screens:v,attribute:e.attribute,hasTabletValue:e.hasTabletValue,hasMobileValue:e.hasMobileValue,valueCheckAttribute:e.valueCheckAttribute}),m&&(0,o.createElement)($e,{hover:e.hover,attribute:e.attribute,hasResponsive:p,forceUpdateHoverState:e.forceUpdateHoverState,hasHoverStateValue:e.hasHoverStateValue})),(0,o.createElement)("div",{className:"stk-control-label__after"},h&&(0,o.createElement)(Be,{className:"stk-control-label__units",value:e.unit,options:b,onChange:t=>e.onChangeUnit(t),buttonLabel:(0,d.__)("Unit",s.i18n),hasLabels:!1,hasColors:!1,labelPosition:"left"}),e.after)),(0,o.createElement)("div",{className:"stk-control-content"},e.children)))};mt.defaultProps={className:"",label:"",help:"",boldLabel:!1,attribute:"",responsive:!1,hover:!1,units:!1,unit:"",onChangeUnit:null,after:null,disableTablet:!1,disableMobile:!1,hasTabletValue:void 0,hasMobileValue:void 0,visualGuide:pt,helpTooltip:pt,forceUpdateHoverState:!1,hasHoverStateValue:void 0};const ht=e=>{const t=Se(`${e.attribute}Unit`,e.responsive,e.hover),n=ve((e=>e[t]))||"",a=e.unit?e.unit:n,l=be();return(0,o.createElement)(mt,c({},e,{unit:a,onChangeUnit:o=>{if(e.onChangeUnit)return e.onChangeUnit(o,t,n);l({[t]:o})}}))};ht.defaultProps={className:"",label:"",help:"",attribute:"",responsive:!1,hover:!1,units:!1,onChangeUnit:null,unit:null,after:null,valueCheckAttribute:"",disableTablet:!1,disableMobile:!1,hasTabletValue:void 0,hasMobileValue:void 0,visualGuide:pt,helpTooltip:pt,forceUpdateHoverState:!1,hasHoverStateValue:void 0};const gt=ht,vt=e=>{const t=[...Object.keys(ht.defaultProps),"allowReset","screens"],n=(0,v.pick)(e,t);return e.screens&&(n.responsive=e.screens),[(0,v.omit)(e,t),n]},bt=(0,o.createElement)(r.Dashicon,{icon:"image-rotate"}),ft=(0,o.memo)((e=>{const t=null!==e.showReset?e.showReset:void 0!==e.value&&e.value!==e.default&&e.value!==e.placeholder,n=g()(["stk-control__reset-button",{"stk-control__reset-button--no-modified":!e.hasPanelModifiedIndicator}]);return e.allowReset&&t&&(0,o.createElement)(M,{className:n,isSmall:!0,isTertiary:!0,label:(0,d.__)("Reset",s.i18n),onClick:()=>{e.onChange(void 0===e.default?"":e.default)},icon:bt})}));ft.defaultProps={allowReset:!0,showReset:null,value:"",default:"",onChange:null,hasPanelModifiedIndicator:!0};const kt=[{value:"",title:(0,d.__)("Single",s.i18n)},{value:"gradient",title:(0,d.__)("Gradient",s.i18n)}],_t=(0,o.memo)((e=>{const{onChange:t,preOnChange:n,value:a,colors:l,gradients:i,isGradient:s}=e,[c,u]=(0,o.useState)(a.startsWith("linear-")||a.startsWith("radial-")?"gradient":""),d=l.reduce(((e,t)=>[...e,...t.colors||t.gradients]),[]);let p,h=a;return d.some((e=>(e.color===a||e.gradient===a)&&(h=e.name,p=e.name,!0))),p=h||("transparent"===a?"Transparent":a),(0,o.createElement)(o.Fragment,null,e.hasGradientPicker&&(0,o.createElement)(fn,{className:"stk-color-palette-popup-control__tabs",controls:kt,fullwidth:!1,allowReset:!1,value:c,onChange:e=>u(e),disabled:e.enableGradient?[]:["gradient"]}),(e.hasGradientPicker?c:s)&&(0,o.createElement)(r.GradientPicker,{onChange:e=>{t(n(e,a))},value:a.startsWith("linear-")||a.startsWith("radial-")?a:null,gradients:e.hasGradientPicker?i:l,clearable:!1,__experimentalHasMultipleOrigins:!0}),(e.hasGradientPicker?!c:!s)&&(0,o.createElement)(r.ColorPicker,{onChange:e=>{t(n(e,a))},color:a,enableAlpha:!0}),(e.hasGradientPicker?!c:!s)&&(0,o.createElement)(r.ColorPalette,{value:a,onChange:e=>{const o=(0,ke.getColorObjectByColorValue)(d,e);t(n((0,m.applyFilters)("stackable.color-palette-control.change",e,o),a))},disableCustomColors:!0,label:p,clearable:!1,colors:l,__experimentalHasMultipleOrigins:!0}))}));_t.defaultProps={value:"",onChange:()=>{},preOnChange:e=>e,colors:[],gradients:[],isGradient:!1,hasGradientPicker:!1,enableGradient:!1};const yt={placement:"left-start",offset:36,shift:!0};(0,m.addFilter)("stackable.color-palette-control.colors","stackable/global-color-schemes-color-palette-control",(e=>{let{colors:t,gradients:n}=e;const{getColorGroups:o}=Di(),{colorSchemeColors:a,colorSchemeGradients:l}=o();let r=(0,v.cloneDeep)(t),i=(0,v.cloneDeep)(n);return i=[...l,...i],r=[...a,...t],{colors:r,gradients:i}})),(0,m.addFilter)("stackable.color-palette-control.colors","stackable/color-palette-control",(e=>{let{colors:t,gradients:n}=e;const{stackableColors:o,stackableGradients:a}=(0,ne.select)("stackable/global-colors").getSettings();let l=(0,v.cloneDeep)(t),r=(0,v.cloneDeep)(n);return a&&a.length&&(r=[{name:(0,d.__)("Global Gradients",s.i18n),gradients:(0,v.cloneDeep)(a),id:"stk-global-gradients"},...r]),o&&o.length&&(l=[{name:(0,d.__)("Global Colors",s.i18n),colors:(0,v.cloneDeep)(o),id:"stk-global-colors"},...l]),{colors:l,gradients:r}})),(0,m.addFilter)("stackable.color-palette-control.color-value","stackable/color-palette-control",(e=>"string"==typeof e&&e.includes("--stk-global-color")&&e.match(/#[\d\w]{6,}/)?e.match(/#[\d\w]{6,}/)[0]:e));const wt=(0,o.memo)((e=>{const{label:t,className:n=""}=e,[a,l]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[i,s]=vt(e),{hideThemeColors:u,hideDefaultColors:p,hideSiteEditorColors:h}=(0,ne.useSelect)("stackable/global-colors").getSettings();let{colors:v,gradients:b}=(0,m.applyFilters)("stackable.color-palette-control.colors",(0,ke.__experimentalUseMultipleOriginColorsAndGradients)());v=v.filter((e=>!(u&&e.name===(0,d._x)("Theme","Indicates this palette comes from the theme.")||p&&e.name===(0,d._x)("Default","Indicates this palette comes from WordPress.")||h&&e.name===(0,d._x)("Custom","Indicates this palette comes from the theme.")))),b=b.filter((e=>!(u&&e.name===(0,d._x)("Theme","Indicates this palette comes from the theme.")||p&&e.name===(0,d._x)("Default","Indicates this palette comes from WordPress.")||h&&e.name===(0,d._x)("Custom","Indicates this palette comes from the theme."))));const f=[...v,...b].reduce(((e,t)=>[...e,...t.colors||t.gradients]),[]);let k=void 0===e.value?a:e.value;const _=void 0===e.onChange?l:e.onChange;k=(0,m.applyFilters)("stackable.color-palette-control.color-value",k);let y,w=k,E=k;f.some((e=>e.color===k||e.gradient===k?(w=e.name,y=e.name,!0):!(!e.slug||`var(--${e.slug})`!==k||(w=e.name,y=e.name,E=e.color,0)))),y=w||("transparent"===k?"Transparent":k);const S={colorValue:k,label:e.colorLabel||y,additionalToggleProps:e.additionalToggleProps},x=(0,o.createElement)(_t,{value:E,onChange:_,preOnChange:e.preOnChange,colors:e.isGradient?b:v,isGradient:e.isGradient,hasGradientPicker:e.hasGradientPicker,enableGradient:e.hasGradientPicker&&e.enableGradient,gradients:b});return(0,o.createElement)(gt,c({},s,{className:g()([n,"editor-color-palette-control","stk-color-palette-control"]),label:t}),e.isExpanded&&x,!e.isExpanded&&(0,o.createElement)(r.Dropdown,{popoverProps:yt,className:"block-editor-tools-panel-color-gradient-settings__dropdown",renderToggle:St(S),renderContent:()=>(0,o.createElement)("div",{className:"stk-color-palette-control__popover-content"},x)}),(0,o.createElement)(ft,{allowReset:e.allowReset,value:k,default:e.default,onChange:_}))}));wt.defaultProps={allowReset:!0,default:"",attribute:"",value:void 0,colorLabel:void 0,onChange:void 0,preOnChange:e=>e,isExpanded:!1,isGradient:!1,hasGradientPicker:!1,enableGradient:!1,additionalToggleProps:{}};const Et=wt,St=e=>t=>{let{onToggle:n,isOpen:a}=t;const{colorValue:l,label:i,additionalToggleProps:s}=e,c={onClick:n,className:g()("block-editor-panel-color-gradient-settings__dropdown",{"is-open":a}),"aria-expanded":a,...s};return(0,o.createElement)(r.Button,c,(0,o.createElement)(xt,{colorValue:l,label:i}))},xt=e=>{let{colorValue:t,label:n}=e;return(0,o.createElement)(r.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(r.ColorIndicator,{className:"stk-color-indicator block-editor-panel-color-gradient-settings__color-indicator",colorValue:t}),(0,o.createElement)(r.FlexItem,{className:"stk-color-name block-editor-panel-color-gradient-settings__color-name",title:n},n))},Ct=(0,o.createContext)(null);(0,Ye.createHigherOrderComponent)((e=>t=>(0,o.createElement)(Ct.Provider,{value:t.context},(0,o.createElement)(e,t))),"withQueryLoopContext");const Tt=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},e),(0,o.createElement)("path",{d:"M289 445l-33 35-33-35L0 208 112 32h288l112 176-223 237zm142-221H81l175 186 175-186zm3.7-48L385 97.9 311.5 176h123.2zM336 80H176l80 85 80-85zM127 97.9L77.3 176h123.2L127 97.9z"}))},Mt={image:{title:(0,d.__)("Get More Image Shapes",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Mask images with a variety of blob-like shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Choose from over 50 different shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Enhances the overall aesthetic of images",s.i18n)))},"dynamic-attributes":{title:(0,d.__)("Use Dynamic Content",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Add dynamic content from posts or post meta",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Use third-party plugins as dynamic sources such as ACF, Meta Box, Toolset, and more",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Build custom loop design with the Native Query Loop",s.i18n)))},separator:{title:(0,d.__)("Separator Layers",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Add a second and third layer to separators",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Change layer color, size and opacity",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Greater creativity in designing separators",s.i18n)))},"icon-colors":{title:(0,d.__)("Elevate Your Icons",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Liven up icons with gradient fills, multiple colors and background shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("More design options and customization for icons",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Choose from over 50 background shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Greater visual interest and variety for your icons",s.i18n)))},"icon-background-shape":{description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Liven up icons with background shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Choose from over 50 background shapes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Greater visual interest and variety for your icons",s.i18n)))},transforms:{description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Adjust timing of CSS transitions",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Change X and Y position of blocks",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Scale or rotate blocks",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Perfect for hover animations",s.i18n)))},"motion-effects":{description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Entrance fade-ins and animations when scrolling to blocks",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Smooth scroll animations based on scrolling position",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Create a more visually engaging and interactive experience",s.i18n)))},"conditional-display":{description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Show or hide blocks based on conditions",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Display blocks based on time, role, meta, custom PHP, option, taxonomy and more",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Use multiple conditions",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Show targeted content and personalization",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Greater control over the visibility of content",s.i18n)))},"custom-css":{description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Add custom CSS rules specific for each block",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Support for media queries",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Fine-tune styling on a per block basis",s.i18n)))},"column-arrangement":{title:(0,d.__)("Adjust Column Arrangement",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Adjust the arrangement of columns when collapsed on tablet and mobile",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Ensure that content remains organized and easily readable on mobile",s.i18n)))},"design-library":{title:(0,d.__)("This is a Premium Design",s.i18n),description:(0,d.__)("Unlock access to the entire design library and set your website apart from the rest.",s.i18n)},posts:{title:(0,d.__)("Get More Post Options",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("New Custom Post Type option",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Offset, exclude, include specific posts",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Hide the current post - great for synced patterns",s.i18n)))},"icon-library":{title:(0,d.__)("Unlock Your Icon Library",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Add your custom SVG icons",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Easily access your custom icons in the icon picker",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Organize your custom icons in your library",s.i18n)))},"font-pairs":{title:(0,d.__)("Premium Typography",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Access to 90+ curated font pairs",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Create your own custom font pairs",s.i18n)))},"color-schemes":{title:(0,d.__)("Premium Color Schemes",s.i18n),description:(0,o.createElement)("ul",null,(0,o.createElement)("li",null,(0,d.__)("Access to 50+ curated color scheme presets",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Create your own color schemes",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Set default color schemes for blocks and sections",s.i18n)),(0,o.createElement)("li",null,(0,d.__)("Streamline your design workflow",s.i18n)))}},Nt=e=>{var t,n,a;const l=g()(["ugb-design-control-pro-note",e.className]),r=e.description||(null===(t=Mt[e.type])||void 0===t?void 0:t.description);return(0,o.createElement)("div",{className:l},e.isDismissible&&(0,o.createElement)(M,{className:"ugb-design-control-pro-note__close",icon:"no-alt",isTertiary:!0,onClick:e.onClose}),e.showImage&&(0,o.createElement)("img",{src:("dist/images/components-pro-control-pro-icon.05ae547.png",`${s.srcUrl}/dist/images/components-pro-control-pro-icon.05ae547.png`),className:"ugb-design-control-pro-note__logo",alt:""}),(0,o.createElement)("h4",null,e.title||(null===(n=Mt[e.type])||void 0===n?void 0:n.title)||(0,d.__)("This Is a Premium Feature",s.i18n)),r&&(0,o.createElement)("div",{className:"ugb-design-control-pro-note__description"},r),e.showButton&&(0,o.createElement)("div",null,(0,o.createElement)("a",{href:"https://wpstackable.com/premium/?utm_source=editor-learn-more&utm_campaign=learnmore&utm_medium=gutenberg",target:"_premium",className:"button button-secondary"},(0,o.createElement)(Tt,null),e.button||(null===(a=Mt[e.type])||void 0===a?void 0:a.button)||(0,d.__)("Learn More",s.i18n))),e.demoUrl&&(0,o.createElement)("p",{className:"ugb-design-control-pro-note__demo-link"},(0,o.createElement)("a",{href:e.demoUrl,target:"_premium",className:"button button-secondary"},(0,d.__)("View Demo",s.i18n))),e.showHideNote&&(0,o.createElement)("p",{className:"ugb-design-control-pro-note__notice"},(0,d.__)("You can hide premium hints in the settings",s.i18n)))};Nt.defaultProps={className:"",type:"",title:"",description:"",button:"",showImage:!0,showButton:!0,showHideNote:!0,demoUrl:"",buttonUtmSource:void 0,isDismissible:!1,onClose:()=>{}};const It=Nt,Ot=e=>{var t,n;const[a,l]=(0,o.useState)(!1),[r,i]=(0,o.useState)(e.value);(0,o.useEffect)((()=>{const e=e=>{a&&(e.target.closest(".stackable-dynamic-content__popover")||e.target.closest(".stk-dynamic-content-control__button")||e.target.closest(".components-color-picker")||e.target.closest(".react-autosuggest__suggestions-container")||e.target.closest(".components-dropdown__content")||l(!1))};return document.body.addEventListener("mousedown",e),()=>document.body.removeEventListener("mousedown",e)}),[a]),(0,o.useEffect)((()=>{const t=setTimeout((()=>{i(e.value)}),300);return()=>clearTimeout(t)}),[e.value]);const s=[];var c,u;null!=r&&null!==(t=r.includes)&&void 0!==t&&t.call(r,"!#stk_dynamic")&&(null===(c=r.match(/\!#stk_dynamic\/(.*)\!#/g))||void 0===c||c.forEach((e=>{const t=e.replace(/\!#/g,"").replace("stk_dynamic/","");s.push(t)}))),null!=r&&null!==(n=r.includes)&&void 0!==n&&n.call(r,'data-stk-dynamic="')&&(null===(u=r.match(/data-stk-dynamic="[^"]*"/g))||void 0===u||u.forEach((e=>{var t,n,o;const a=null===(t=e.match(/data-stk-dynamic="(.*?(?="))"/g))||void 0===t||null===(n=t[0])||void 0===n||null===(o=n.replace(/"/g,""))||void 0===o?void 0:o.replace("data-stk-dynamic=","");a&&s.push(a)})));const d=Bt(r),p=Pt(r),m=a||s.length,h=(0,v.first)(s)||"";return{onClick:()=>{l(!a)},isPressed:m,isPopoverOpen:a,value:d,placeholder:p,onClose:()=>{l(!1)},onReset:()=>{e.onChange("")},onChange:(t,n,o)=>{const a=e.isFormatType?`<span data-stk-dynamic="${o}" contenteditable="false" class="stk-dynamic-content">${t}</span>`:`!#stk_dynamic/${o}!#`;e.onChange(a),i(a),l(!1)},activeAttribute:h}},Bt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";const{clientId:t}=(0,ke.useBlockEditContext)(),n=(0,ne.select)("core/block-editor").getBlock(t),a=(0,o.useContext)(Ct);return(0,ne.useSelect)((t=>{var o;if(!e||!(0,v.isString)(e))return e;if(!e.includes("!#stk_dynamic")&&!e.includes("data-stk-dynamic"))return e;if(!t("stackable/dynamic-content"))return e;let l=(null===(o=t("core/editor"))||void 0===o?void 0:o.getCurrentPostId())||-1;var r,i;l&&(null==a?void 0:a.postId)!==l&&(l=(null===(r=a.postId)||void 0===r?void 0:r.toString())||-1),-1===l&&t("core/edit-site")&&(l=(null===(i=t("core/edit-site").getEditedPostContext())||void 0===i?void 0:i.postId)||-1);let s=e;var c,u;-1!==l&&(s=null===(c=s)||void 0===c?void 0:c.replace(/<span[^\>]+data-stk-dynamic=[^\>]*>(.*?)<\/span>/g,(e=>{const t=e.match(/data-stk-dynamic="([^\"]*)"/)[1],n=t.split("/");return t.startsWith("current-page")?(n.length>2&&n[2].startsWith("?")?n.splice(2,0,l):2===n.length&&n.push(l),e.replace(/data-stk-dynamic="[^\"]*"/g,'data-stk-dynamic="'+n.join("/")+'"')):e})),s=null===(u=s)||void 0===u?void 0:u.replace(/!#stk_dynamic(.*)\!#/g,(e=>{const t=e.replace(/\!#/g,"").replace("stk_dynamic/",""),n=t.split("/");return t.startsWith("current-page")?(n.length>2?n.splice(2,0,l):2===n.length&&n.push(l),"!#stk_dynamic/"+n.join("/")+"!#"):e})));let d=t("stackable/dynamic-content").parseDynamicContents(s,n);var p,m;return-1!==l&&(d=null===(p=d)||void 0===p?void 0:p.replace(/<span[^\>]+data-stk-dynamic=[^\>]*>(.*?)<\/span>/g,(e=>{const t=e.match(/data-stk-dynamic="([^\"]*)"/)[1],n=t.split("/");return t.startsWith("current-page")&&(0,v.last)(n).match(/^\d+$/)?(n.pop(),e.replace(/data-stk-dynamic="[^\"]*"/g,'data-stk-dynamic="'+n.join("/")+'"')):e})),d=null===(m=d)||void 0===m?void 0:m.replace(/!#stk_dynamic(.*)\!#/g,(e=>{const t=e.replace(/\!#/g,"").replace("stk_dynamic/",""),n=t.split("/");return t.startsWith("current-page")&&(0,v.last)(n).match(/^\d+$/)?"!#stk_dynamic/"+n.join("/")+"!#":e}))),d}),[e,null==a?void 0:a.postId])},Pt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return(0,ne.useSelect)((t=>{var n,o;if(!t("stackable/dynamic-content"))return e;let a=e;return null!=e&&null!==(n=e.includes)&&void 0!==n&&n.call(e,"!#stk_dynamic")&&(a=a.replace(/\!#stk_dynamic\/(.*)\!#/g,(e=>{const n=e.replace(/\!#/g,"").replace("stk_dynamic/","");let o=(0,v.first)(t("stackable/dynamic-content").getFieldTitle(n));var a;o||(o=null===(a=new URL(`stk:${n}`).pathname.split("/"))||void 0===a?void 0:a[1]);return o?`[${o}]`:""}))),null!=e&&null!==(o=e.includes)&&void 0!==o&&o.call(e,'data-stk-dynamic="')&&(a=a.replace(/<span[^\>]+data-stk-dynamic="[^>"]*"[^\>]*>(.*?)<\/span>/g,(n=>{var o,a,l;const r=null===(o=n.match(/data-stk-dynamic="(.*?(?="))"/g))||void 0===o||null===(a=o[0])||void 0===a||null===(l=a.replace(/"/g,""))||void 0===l?void 0:l.replace("data-stk-dynamic=","");if(e){let e=(0,v.first)(t("stackable/dynamic-content").getFieldTitle(r));var i;if(!e)e=null===(i=new URL(`stk:${r}`).pathname.split("/"))||void 0===i?void 0:i[1];return e?`[${e}]`:""}return n}))),a}))},Dt=(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({"aria-hidden":"true","data-prefix":"fal","data-icon":"database",className:"database-light_svg__svg-inline--fa database-light_svg__fa-database database-light_svg__fa-w-14",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512"},e),(0,o.createElement)("path",{fill:"currentColor",d:"M224 32c106 0 192 28.75 192 64v32c0 35.25-86 64-192 64S32 163.25 32 128V96c0-35.25 86-64 192-64m192 149.5V224c0 35.25-86 64-192 64S32 259.25 32 224v-42.5c41.25 29 116.75 42.5 192 42.5s150.749-13.5 192-42.5m0 96V320c0 35.25-86 64-192 64S32 355.25 32 320v-42.5c41.25 29 116.75 42.5 192 42.5s150.749-13.5 192-42.5m0 96V416c0 35.25-86 64-192 64S32 451.25 32 416v-42.5c41.25 29 116.75 42.5 192 42.5s150.749-13.5 192-42.5M224 0C145.858 0 0 18.801 0 96v320c0 77.338 146.096 96 224 96 78.142 0 224-18.801 224-96V96c0-77.338-146.096-96-224-96z"}))}),null),Lt=(0,o.memo)((e=>{if((0,ne.select)("core/customize-widgets"))return null;if(!s.isPro&&!s.showProNotice)return null;const t=(0,m.applyFilters)("stackable.dynamic-content.component")||o.Fragment;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(r.Button,{className:"stk-dynamic-content-control__button",icon:Dt,"aria-haspopup":"true",label:(0,d.__)("Dynamic Fields",s.i18n),variant:"secondary",onClick:e.onClick,isPressed:!!e.isPressed}),e.isPopoverOpen&&(0,o.createElement)(nt,{position:"top right",className:g()("stackable-dynamic-content__popover",{"stk-dynamic-content__popover--is-premium":!s.isPro}),onEscape:e.onClick},!s.isPro&&(0,o.createElement)(It,{type:"dynamic-attributes"}),s.isPro&&(0,o.createElement)(t,{onClose:e.onClose,onChange:e.onChange,activeAttribute:e.activeAttribute,type:e.type})))})),Rt=e=>{let{children:t,enable:n,...a}=e;if(!n)return t;const l=""!==a.activeAttribute,i=g()(["stk-dynamic-content-control"],{"stk--has-dynamic-content":l,"stk--has-control-tooltip":a.controlHasTooltip});return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:i},l?(0,o.createElement)(r.TextControl,{value:a.placeholder,disabled:!0}):t,(0,o.createElement)(Lt,a)),(0,o.createElement)(ft,{allowReset:!0,value:a.activeAttribute,default:"",hasPanelModifiedIndicator:a.hasPanelModifiedIndicator,onChange:a.onReset}))};Rt.defaultProps={enable:!1,controlHasTooltip:!1,children:null,activeAttribute:"",onReset:()=>{}};const At=Rt,Ht=(0,o.memo)((e=>{const[t,n]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[a,l]=vt(e),{isDynamic:i,isMultiline:s,changeDynamicContent:u,isFormatType:d,...p}=a,m=void 0!==u?u:void 0===e.onChange?n:e.onChange,h=Ot({value:void 0===e.value?t:e.value,onChange:m,isFormatType:d}),[v,b]=xe(void 0===e.value?t:e.value),f=void 0===e.onChange?n:e.onChange,k=e=>{b(e),f(e)},_=s?r.TextareaControl:r.TextControl;return(0,o.createElement)(gt,c({className:e.className},l),(0,o.createElement)(At,c({enable:i,hasPanelModifiedIndicator:e.hasPanelModifiedIndicator},h),(0,o.createElement)(_,c({},p,{value:v,onChange:k,className:g()(a.className,"ugb-advanced-text-control")}))),(0,o.createElement)(ft,{allowReset:l.allowReset&&!e.isDynamic,value:v,default:e.default,onChange:k,hasPanelModifiedIndicator:e.hasPanelModifiedIndicator}))}),v.isEqual);Ht.defaultProps={className:"",isMultiline:!1,allowReset:!0,default:"",attribute:"",responsive:!1,hover:!1,isDynamic:!1,isFormatType:!0,value:void 0,onChange:void 0,changeDynamicContent:void 0,hasPanelModifiedIndicator:!0};const Ft=Ht;(0,d.__)("None",s.i18n),(0,d.__)("Normal",s.i18n),(0,d.__)("Multiply",s.i18n),(0,d.__)("Screen",s.i18n),(0,d.__)("Overlay",s.i18n),(0,d.__)("Darken",s.i18n),(0,d.__)("Lighten",s.i18n),(0,d.__)("Color Dodge",s.i18n),(0,d.__)("Color Burn",s.i18n),(0,d.__)("Hard Light",s.i18n),(0,d.__)("Soft Light",s.i18n),(0,d.__)("Difference",s.i18n),(0,d.__)("Exclusion",s.i18n),(0,d.__)("Hue",s.i18n),(0,d.__)("Saturation",s.i18n),(0,d.__)("Color",s.i18n),(0,d.__)("Luminosity",s.i18n),(0,d.__)("Initial",s.i18n),(0,d.__)("Inherit",s.i18n),(0,d.__)("Unset",s.i18n);(0,d.__)("Mix Blend Mode",s.i18n);(0,d.__)("Alt Text (Alternative Text)",s.i18n);(0,d.__)("Shape",s.i18n);const Vt=e=>{let{imageSizes:t,value:n,className:a,defaultValue:l,...r}=e;const i=(e=>(0,v.map)(e,(e=>{let{name:t,slug:n}=e;return{value:n,label:t}})))(t);return(0,v.isEmpty)(i)?null:(0,o.createElement)(Wt,c({},r,{value:n||"large",options:i,className:a,defaultValue:l||"large",default:l||"large"}))};Vt.defaultProps={className:"",label:(0,d.__)("Image Size",s.i18n),value:"large",defaultValue:"",imageSizes:[],onChange:()=>{}},(0,Ye.compose)([(0,ne.withSelect)((e=>({imageSizes:e("core/block-editor").getSettings().imageSizes})))])(Vt);const jt=!!r.__experimentalNumberControl,zt=(0,o.memo)((e=>{const{allowReset:t,withInputField:n,isShiftStepEnabled:a,placeholderRender:l,defaultValue:i,...u}=e,p=()=>""===e.value||isNaN(e.value)&&"auto"!==e.value?"":e.value,[m,h]=(0,o.useState)(""===e.value||isNaN(e.value)&&"auto"!==e.value?"":e.value),[b,f]=(0,o.useState)(p());p()!==b&&(f(p()),""===e.value||isNaN(e.value)&&"auto"!==e.value?h(""):h(e.value));const k=t=>{if(h(t),"string"!=typeof t||"auto"!==t.toLowerCase()){if(!isNaN(t)){const n=parseFloat(t);if(!isNaN(n)){const t=(0,v.clamp)(n,e.min,e.max);return h(t),void e.onChange(t)}}e.onChange(e.resetFallbackValue)}else e.onChange(t)},_=g()(["ugb-range-control",e.className],{"ugb-range-control--blank":""===m}),y=""===m,w=null!==e.placeholder?e.placeholder:e.sliderMin||e.min,E=null!==e.initialPosition?e.initialPosition:w,S=((e,t,n)=>{const o=(e-t)/(n-t)*100;return`${(0,v.clamp)(o,0,100)}%`})(y?E:m,null!==e.sliderMin?e.sliderMin:e.min||0,null!==e.sliderMax?e.sliderMax:e.max||100);let x=e.placeholder;return"function"!=typeof l||m?null===e.placeholder&&(x=E):x=l(m),(0,o.createElement)("div",{className:_,style:{"--ugb-advanced-range-control--width":S}},(0,o.createElement)(r.RangeControl,c({},u,{value:m,initialPosition:"",onChange:k,withInputField:!1,allowReset:!1,min:null!==e.sliderMin?e.sliderMin:e.min===-1/0?0:e.min,max:null!==e.sliderMax?e.sliderMax:e.max===1/0?100:e.max})),n&&jt&&(0,o.createElement)(r.__experimentalNumberControl,{disabled:e.disabled,isShiftStepEnabled:a,max:e.max,min:e.min,onChange:k,onBlur:()=>{if("string"!=typeof m||"auto"!==m.toLowerCase()){if(!isNaN(m)){const t=parseFloat(m);if(!isNaN(t))return void h((0,v.clamp)(t,e.min,e.max))}h(e.resetFallbackValue)}else h(m)},shiftStep:e.shiftStep,step:e.step,value:m,placeholder:x,type:"text"}),t&&(0,o.createElement)(M,{className:"components-range-control__reset",disabled:e.disabled,isSecondary:!0,isSmall:!0,onClick:()=>{h(e.resetFallbackValue),e.onChange(e.resetFallbackValue)}},(0,d.__)("Reset",s.i18n)))}));zt.defaultProps={className:"",allowReset:!1,withInputField:!0,isShiftStepEnabled:!0,max:1/0,min:-1/0,sliderMax:null,sliderMin:null,shiftStep:10,step:1,resetFallbackValue:"",placeholder:null,placeholderRender:null,initialPosition:null,onChange:()=>{}};const $t=zt,Ut=e=>{var t,n;const[a,l]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[r,i]=vt(e),s=oe(),[u]=Ee(),d=!(null===(t=e.units)||void 0===t||!t.length),p=Se(`${e.attribute}Unit`,e.responsive,e.hover),{unitAttribute:m,_valueDesktop:h,_valueTablet:g,_unitDesktop:v,_unitTablet:b}=ve((t=>({unitAttribute:t[p],_valueDesktop:t[`${e.attribute}`],_valueTablet:t[`${e.attribute}Tablet`],_unitDesktop:t[`${e.attribute}Unit`],_unitTablet:t[`${e.attribute}UnitTablet`]}))),f="string"==typeof e.unit?e.unit||(null===(n=e.units)||void 0===n?void 0:n[0])||"px":m||"";if(d){const t=e.units.indexOf(f)<0?0:e.units.indexOf(f);Array.isArray(e.min)&&(r.min=e.min[t]),Array.isArray(e.max)&&(r.max=e.max[t]),Array.isArray(e.sliderMin)&&(r.sliderMin=e.sliderMin[t]),Array.isArray(e.sliderMax)&&(r.sliderMax=e.sliderMax[t]),Array.isArray(e.step)&&(r.step=e.step[t]),r.initialPosition=""!==e.initialPosition?e.initialPosition:e.placeholder,0!==t&&(r.initialPosition="",r.placeholder="")}"Mobile"===s&&g&&""!==g?(r.initialPosition=m===b?g:"",r.placeholder=m===b?g:""):"Mobile"!==s&&"Tablet"!==s||!h||""===h||(r.initialPosition=m===v?h:"",r.placeholder=m===v?h:""),e.forcePlaceholder||"normal"===u||(r.initialPosition="",r.placeholder="");let k=e.placeholderRender;("normal"!==u||d&&f!==e.units[0])&&(k=null);const _=t=>{const n=void 0===e.onChange?l:e.onChange;let o=e.isDynamic?t.toString():t;if(""===o){var a;const t=null===(a=e.onOverrideReset)||void 0===a?void 0:a.call(e);void 0!==t&&(o=t)}n(o)},y=void 0===e.value?a:e.value,w=Ot({value:y,onChange:_});return(0,o.createElement)(gt,i,(0,o.createElement)(At,c({enable:r.isDynamic,controlHasTooltip:!0},w),(0,o.createElement)($t,c({},r,{value:r.isDynamic?parseFloat(y):y,onChange:_,allowReset:!1,placeholderRender:k}))),(0,o.createElement)(ft,{allowReset:e.allowReset,value:y,default:e.default,onChange:_}))};Ut.defaultProps={allowReset:!0,isDynamic:!1,default:"",attribute:"",responsive:!1,hover:!1,value:void 0,onChange:void 0,onOverrideReset:void 0,forcePlaceholder:!1};const qt=(0,o.memo)(Ut,v.isEqual),Gt=(0,o.memo)((e=>{const[t,n]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[a,l]=vt(e),{defaultValue:i,...s}=a;return(0,o.createElement)(gt,c({},l,{className:g()("ugb-advanced-select-control",e.className)}),(0,o.createElement)(r.SelectControl,c({},s,{role:"listbox",value:void 0===e.value?t:e.value,onChange:void 0===e.onChange?n:e.onChange})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:void 0===e.value?t:e.value,default:e.default,onChange:void 0===e.onChange?n:e.onChange}))}),v.isEqual);Gt.defaultProps={className:"",url:"",allowReset:!0,default:"",attribute:"",responsive:!1,hover:!1,value:void 0,onChange:void 0};const Wt=Gt;var Kt=n(8808),Jt=n.n(Kt);const Yt=(e,t)=>{const n=e.trim().replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(!n)return t;const o=new RegExp(n,"i");return Xt(t)?t.map((e=>{let{title:t,options:n}=e;return{title:t,options:n.filter((e=>o.test(e.label)||o.test(e.value)))}})).filter((e=>e.options.length>0)):t.filter((e=>"string"==typeof e?o.test(e):o.test(e.label)||o.test(e.value)))},Xt=e=>!(!e.length||"object"!=typeof e[0].options),Qt=(e,t)=>{if(!t.length)return e;if(!Xt(t)){const n=t.filter((t=>("string"==typeof t?t:t.value)===e));return n.length?n[0].label:e}const n=t.map((t=>{let{title:n,options:o}=t;return{title:n,options:o.filter((t=>t.value===e))}})).filter((e=>e.options.length>0));return n.length?n[0].options[0].label:e},Zt=e=>e.value,en=e=>(0,o.createElement)("div",{className:"ugb--autosuggest-group"},e.title),tn=e=>e.options,nn=()=>!0;class on extends o.Component{constructor(){super(...arguments),this.state={value:"",label:"",suggestions:[],isEmpty:!1,isShowingSuggestions:!1,containerRect:null},this.onSuggestionsFetchRequested=this.onSuggestionsFetchRequested.bind(this),this.onSuggestionsClearRequested=this.onSuggestionsClearRequested.bind(this),this.onChange=this.onChange.bind(this),this.onFocus=this.onFocus.bind(this),this.autosuggestDiv=(0,o.createRef)(),this.suggestionContainerLocationUpdater=this.suggestionContainerLocationUpdater.bind(this)}onChange(e,t){let{newValue:n}=t;this.props.onChange(n),this.setState({value:n,label:Qt(n,this.props.options)})}onFocus(){this.props.highlightValueOnFocus&&setTimeout((()=>{const e=this.autosuggestDiv.current.querySelector(`[data-value="${this.state.value}"]`);e&&e.scrollIntoView()}),0)}onSuggestionsFetchRequested(e){let{value:t,reason:n}=e;if(this.props.disableAutoIndex)return void this.setState({suggestions:Yt("",this.props.options)});if("input-focused"===n||"suggestion-selected"===n)return void this.setState({suggestions:this.props.options,isEmpty:!1});const o=Yt(t,this.props.options);this.setState({suggestions:o,isEmpty:""!==t.trim()&&0===o.length})}suggestionContainerLocationUpdater(){var e;null!==(e=this.autosuggestDiv)&&void 0!==e&&e.current&&this.state.isShowingSuggestions&&(this.setState({containerRect:this.autosuggestDiv.current.getBoundingClientRect()}),requestAnimationFrame(this.suggestionContainerLocationUpdater))}componentDidMount(){this.props.options.length&&this.props.value&&this.setState({value:this.props.value,label:this.props.value?Qt(this.props.value,this.props.options):this.props.value,suggestions:Yt(this.props.disableAutoIndex?"":this.props.value,this.props.options)})}componentWillReceiveProps(e){this.setState({value:e.value,label:e.value?Qt(e.value,e.options):e.value,suggestions:Yt(this.props.disableAutoIndex?"":e.value,e.options)})}onSuggestionsClearRequested(){this.setState({suggestions:[]})}render(){var e,t,n,a;const{label:l,suggestions:r}=this.state,i={placeholder:this.props.placeholder,value:l,onChange:this.onChange,onFocus:this.onFocus,type:"search",...this.props.inputProps};return(0,o.createElement)(_l,{help:this.props.help,className:g()("ugb-advanced-autosuggest-control",this.props.className),label:this.props.label,screens:this.props.screens,value:this.props.value,defaultValue:null!==(e=null===(t=this.props)||void 0===t?void 0:t.defaultValue)&&void 0!==e?e:"",onChange:e=>{var t;(""===e||null!==(t=this.props)&&void 0!==t&&t.defaultValue)&&this.onChange(null,{newValue:e})},allowReset:this.props.allowReset,helpTooltip:this.props.helpTooltip},(0,o.createElement)("div",{className:"ugb-advanced-autosuggest-control__select",ref:this.autosuggestDiv},this.state.isShowingSuggestions&&(0,o.createElement)("style",null,`:root {\n\t\t\t\t\t\t\t\t--container-left: ${null===(n=this.state.containerRect)||void 0===n?void 0:n.left}px;\n\t\t\t\t\t\t\t\t--container-bottom: ${null===(a=this.state.containerRect)||void 0===a?void 0:a.bottom}px;\n\t\t\t\t\t\t\t}`),(0,o.createElement)(Jt(),{multiSection:Xt(this.props.options),suggestions:r,focusInputOnSuggestionClick:!1,onSuggestionsFetchRequested:this.onSuggestionsFetchRequested,onSuggestionsClearRequested:this.onSuggestionsClearRequested,onSuggestionSelected:this.props.onSuggestionSelected,getSuggestionValue:this.props.getSuggestionValue||Zt,renderSuggestion:e=>(0,o.createElement)("div",{className:"ugb-autosuggest-option","data-value":e.value,id:`ugb-autosuggest-option--${e.id||(0,v.kebabCase)(e.value)}`},this.props.renderOption?this.props.renderOption(e):e.label),renderSectionTitle:en,getSectionSuggestions:tn,shouldRenderSuggestions:nn,inputProps:i,renderInputComponent:this.props.renderInputComponent?this.props.renderInputComponent:e=>(0,o.createElement)("input",e),renderSuggestionsContainer:e=>{let{containerProps:t,children:n}=e;const a=t.className.indexOf("react-autosuggest__suggestions-container--open")>=0;if(a&&!this.state.isShowingSuggestions?(this.setState({isShowingSuggestions:!0}),requestAnimationFrame(this.suggestionContainerLocationUpdater)):!a&&this.state.isShowingSuggestions&&this.setState({isShowingSuggestions:!1}),a){const e=this.autosuggestDiv.current.getBoundingClientRect(),a={left:`calc(var(--container-left, 0px) + ${window.scrollX}px)`,top:`calc(var(--container-bottom, 0px) + ${window.scrollY}px)`,width:e.width},l=(0,o.createElement)("div",c({},t,{style:a,className:g()(t.className,"ugb-advanced-autosuggest__suggestions-container")}),n);return(0,o.createPortal)(l,document.body)}return null}}),this.state.isEmpty&&(0,o.createElement)("div",{className:"ugb--autosuggest-empty"},this.props.noMatchesLabel),this.props.children))}}on.defaultProps={onChange:()=>{},help:"",className:"",screens:["desktop"],options:[],value:"",noMatchesLabel:(0,d.__)("No matches found",s.i18n),renderOption:null,highlightValueOnFocus:!1,allowReset:!0,placeholder:"",getSuggestionValue:null,disableAutoIndex:!1,onSuggestionSelected:()=>{},renderInputComponent:null,inputProps:{},helpTooltip:{}};const an=on,ln=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M19 18H1v1h18v-1zM14 11h-3v6h3v-6zM9 9H6v8h3V9z"}))},rn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M10.5 1h-1v18h1V1zM8 5H5v10h3V5zM15 5h-3v10h3V5z"}))},sn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M2 1H1v18h1V1zM6 5H3v10h3V5zM10 5H7v10h3V5z"}))},cn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M19 1h-1v18h1V1zM13 5h-3v10h3V5zM17 5h-3v10h3V5z"}))},un=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M20 1h-1v18h1V1zM1 1H0v18h1V1zM5 5H2v10h3V5zM18 5h-3v10h3V5z"}))},dn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M20 1h-1v18h1V1zM1 1H0v18h1V1zM7 5H4v10h3V5zM16 5h-3v10h3V5z"}))},pn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M20 1h-1v18h1V1zM1 1H0v18h1V1zM8 5H5v10h3V5zM15 5h-3v10h3V5z"}))},mn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M19 1H1v1h18V1zM14 3h-3v6h3V3zM9 3H6v8h3V3z"}))},hn=function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M19 9.5H1v1h18v-1z"}),(0,o.createElement)("path",{d:"M14 7h-3v6h3V7zM9 6H6v8h3V6z"}))},gn={"flex-horizontal":[{value:"flex-start",title:(0,d.__)("Start",s.i18n),icon:(0,o.createElement)(sn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(rn,null)},{value:"flex-end",title:(0,d.__)("End",s.i18n),icon:(0,o.createElement)(cn,null)},{value:"space-between",title:(0,d.__)("Space Between",s.i18n),icon:(0,o.createElement)(un,null)},{value:"space-around",title:(0,d.__)("Space Around",s.i18n),icon:(0,o.createElement)(dn,null)},{value:"space-evenly",title:(0,d.__)("Space Evenly",s.i18n),icon:(0,o.createElement)(pn,null)}],"flex-horizontal-alt":[{value:"flex-start",title:(0,d.__)("Start",s.i18n),icon:(0,o.createElement)(sn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(rn,null)},{value:"flex-end",title:(0,d.__)("End",s.i18n),icon:(0,o.createElement)(cn,null)},{value:"space-between",title:(0,d.__)("Space Between",s.i18n),icon:(0,o.createElement)(un,null)}],"flex-vertical":[{value:"flex-start",title:(0,d.__)("Start",s.i18n),icon:(0,o.createElement)(mn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(hn,null)},{value:"flex-end",title:(0,d.__)("End",s.i18n),icon:(0,o.createElement)(ln,null)},{value:"stretch",title:(0,d.__)("Stretch",s.i18n),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({viewBox:"0 0 20 20",fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M19 18H1v1h18v-1zM19 1H1v1h18V1zM14 3h-3v14h3V3zM9 3H6v14h3V3z"}))}),null)},{value:"baseline",title:(0,d.__)("Baseline",s.i18n),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},e),(0,o.createElement)("path",{d:"M1 14.1h18v1H1zM11.4 11.1h-3l-.6 2H6.5l2.6-8.3h1.7l2.6 8.3H12l-.6-2zm-2.6-1h2.4L10 5.9l-1.2 4.2z"}))}),null)}],"flex-justify-vertical":[{value:"flex-start",title:(0,d.__)("Start",s.i18n),icon:(0,o.createElement)(mn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(hn,null)},{value:"flex-end",title:(0,d.__)("End",s.i18n),icon:(0,o.createElement)(ln,null)},{value:"space-between",title:(0,d.__)("Space Between",s.i18n),icon:(0,o.createElement)(un,{style:{transform:"rotate(90deg)"}})},{value:"space-around",title:(0,d.__)("Space Around",s.i18n),icon:(0,o.createElement)(dn,{style:{transform:"rotate(90deg)"}})},{value:"space-evenly",title:(0,d.__)("Space Evenly",s.i18n),icon:(0,o.createElement)(pn,{style:{transform:"rotate(90deg)"}})}],horizontal:[{value:"flex-start",title:(0,d.__)("Left",s.i18n),icon:(0,o.createElement)(sn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(rn,null)},{value:"flex-end",title:(0,d.__)("Right",s.i18n),icon:(0,o.createElement)(cn,null)}],vertical:[{value:"flex-start",title:(0,d.__)("Top",s.i18n),icon:(0,o.createElement)(mn,null)},{value:"center",title:(0,d.__)("Center",s.i18n),icon:(0,o.createElement)(hn,null)},{value:"flex-end",title:(0,d.__)("Bottom",s.i18n),icon:(0,o.createElement)(ln,null)}]},vn=e=>{const[t,n]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[a,l]=vt(e),{className:i="",controls:s,fullwidth:u,multiline:d,isToggleOnly:p,omit:h}=a,b=(0,m.applyFilters)("stackable.toolbar-control.controls",gn),f="string"==typeof s?b[s]:s,k=g()({"ugb-toolbar--full-width":u,"ugb-toolbar--multiline":d,"ugb-toolbar--small":e.isSmall}),_=void 0===e.value?t:e.value,y=void 0===e.onChange?n:e.onChange,w=f.every((t=>!(_?_===t.value:e.placeholder===t.value)));return(0,o.createElement)(gt,c({},l,{className:g()("ugb-advanced-toolbar-control",i,l.className)}),(0,o.createElement)(r.ButtonGroup,{children:f.map(((t,n)=>{if(h.includes(t.value))return null;const a=e.default||"",l=_?_===t.value:e.placeholder===t.value,r=l?"0":"-1",i={...(0,v.omit)(t,"controls","show"),onClick:()=>{p&&t.value===_||y(t.value!==_?t.value:a)},children:t.icon?null:t.custom||(0,o.createElement)("span",{className:"ugb-advanced-toolbar-control__text-button"},t.title)};return(0,o.createElement)(M,c({key:t.value},i,{label:t.title||e.label,tabIndex:w&&0===n?"0":r,disabled:"all"===e.disabled||e.disabled.includes(t.value),isPrimary:l,isSmall:e.isSmall,onKeyDown:e=>{const t=e.target;if(t)if(39===e.keyCode){const e=t.nextElementSibling||t.parentElement.firstElementChild;e.focus(),e.click()}else if(37===e.keyCode){const e=t.previousElementSibling||t.parentElement.lastElementChild;e.focus(),e.click()}}}))})),className:k}),(0,o.createElement)(ft,{allowReset:e.allowReset,value:_,default:e.default,onChange:y}))},bn=[];vn.defaultProps={controls:bn,multiline:!1,fullwidth:!0,isSmall:!1,isToggleOnly:!1,allowReset:!0,default:"",attribute:"",responsive:!1,hover:!1,value:void 0,onChange:void 0,placeholder:"",disabled:bn,omit:bn};const fn=(0,o.memo)(vn,v.isEqual),kn=e=>{let t=e.placeholder;const n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:21;return""===e?1:Math.round(parseFloat(e)/parseFloat(t)*10)/10};return"string"==typeof t&&(t=[t,n(t)]),(0,o.createElement)(qt,c({},e,{placeholder:t,onChangeUnit:t=>{""!==e.value&&("em"===t||"rem"===t?e.onChange(n(e.value)):"px"===t&&e.onChange(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:21;return""===e?21:Math.round(parseFloat(e)*t)}(e.value))),e.onChangeUnit(t)}}))};kn.defaultProps={label:(0,d.__)("Font Size",s.i18n),value:"",onChange:()=>{},unit:"px",onChangeUnit:()=>{},min:[0,0],max:[150,7],step:[1,.05],units:["px","em"],placeholder:""};const _n=e=>{const{loadingThemeFont:t,themeFonts:a,themeFontOptions:l}=(0,ne.select)("stackable/theme-fonts").getThemeFonts(),[r,i]=(0,o.useState)([]);(0,o.useEffect)((()=>{(async()=>{const{default:e}=await n.e(755).then(n.t.bind(n,1513,19));return e.map((e=>({label:e.family,value:e.family})))})().then((e=>{i(e)}))}),[]);const u=(0,o.useMemo)((()=>{const e=[{id:"system-fonts",title:(0,d.__)("System Fonts",s.i18n),options:Object.keys(H).map((e=>({label:H[e].label,value:e})))},{id:"modern-font-stacks",title:(0,d.__)("Modern Font Stacks",s.i18n),options:Object.keys(F).map((e=>({label:F[e].label,value:e})))},{id:"google-fonts",title:(0,d.__)("Google Fonts",s.i18n),options:r}];return a.length&&e.unshift({id:"theme-fonts",title:(0,d.__)("Theme Fonts",s.i18n),options:l}),(0,m.applyFilters)("stackable.font-family-control.options",e)}),[t,r]);return(0,o.createElement)(an,c({options:u,highlightValueOnFocus:!0},e,{onChange:t=>{a.includes(t)||r.some((e=>e.value===t&&(z(t),!0))),e.onChange(t)}}))};_n.defaultProps={onChange:()=>{},label:(0,d.__)("Font Family",s.i18n),value:""};function yn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function wn(e,t){if(e){if("string"==typeof e)return yn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?yn(e,t):void 0}}function En(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,a,l=[],r=!0,i=!1;try{for(n=n.call(e);!(r=(o=n.next()).done)&&(l.push(o.value),!t||l.length!==t);r=!0);}catch(e){i=!0,a=e}finally{try{r||null==n.return||n.return()}finally{if(i)throw a}}return l}}(e,t)||wn(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?Object(arguments[t]):{},o=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&o.push.apply(o,Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),o.forEach((function(t){Sn(e,t,n[t])}))}return e}function Cn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tn(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Mn(e,t,n){return t&&Tn(e.prototype,t),n&&Tn(e,n),e}function Nn(e){return Nn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Nn(e)}function In(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function On(e,t){if(t&&("object"===Nn(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return In(e)}function Bn(e){return Bn=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},Bn(e)}function Pn(e,t){return Pn=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},Pn(e,t)}function Dn(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Pn(e,t)}var Ln=n(1143),Rn=n.n(Ln);var An=n(5697),Hn=n.n(An),Fn=function(){function e(){Cn(this,e),Sn(this,"refs",{})}return Mn(e,[{key:"add",value:function(e,t){this.refs[e]||(this.refs[e]=[]),this.refs[e].push(t)}},{key:"remove",value:function(e,t){var n=this.getIndex(e,t);-1!==n&&this.refs[e].splice(n,1)}},{key:"isActive",value:function(){return this.active}},{key:"getActive",value:function(){var e=this;return this.refs[this.active.collection].find((function(t){return t.node.sortableInfo.index==e.active.index}))}},{key:"getIndex",value:function(e,t){return this.refs[e].indexOf(t)}},{key:"getOrderedRefs",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.active.collection;return this.refs[e].sort(Vn)}}]),e}();function Vn(e,t){return e.node.sortableInfo.index-t.node.sortableInfo.index}function jn(e,t){return Object.keys(e).reduce((function(n,o){return-1===t.indexOf(o)&&(n[o]=e[o]),n}),{})}var zn={end:["touchend","touchcancel","mouseup"],move:["touchmove","mousemove"],start:["touchstart","mousedown"]},$n=function(){if("undefined"==typeof window||"undefined"==typeof document)return"";var e=window.getComputedStyle(document.documentElement,"")||["-moz-hidden-iframe"],t=(Array.prototype.slice.call(e).join("").match(/-(moz|webkit|ms)-/)||""===e.OLink&&["","o"])[1];return"ms"===t?"ms":t&&t.length?t[0].toUpperCase()+t.substr(1):""}();function Un(e,t){Object.keys(t).forEach((function(n){e.style[n]=t[n]}))}function qn(e,t){e.style["".concat($n,"Transform")]=null==t?"":"translate3d(".concat(t.x,"px,").concat(t.y,"px,0)")}function Gn(e,t){e.style["".concat($n,"TransitionDuration")]=null==t?"":"".concat(t,"ms")}function Wn(e,t){for(;e;){if(t(e))return e;e=e.parentNode}return null}function Kn(e,t,n){return Math.max(e,Math.min(n,t))}function Jn(e){return"px"===e.substr(-2)?parseFloat(e):0}function Yn(e){var t=window.getComputedStyle(e);return{bottom:Jn(t.marginBottom),left:Jn(t.marginLeft),right:Jn(t.marginRight),top:Jn(t.marginTop)}}function Xn(e,t){var n=t.displayName||t.name;return n?"".concat(e,"(").concat(n,")"):e}function Qn(e,t){var n=e.getBoundingClientRect();return{top:n.top+t.top,left:n.left+t.left}}function Zn(e){return e.touches&&e.touches.length?{x:e.touches[0].pageX,y:e.touches[0].pageY}:e.changedTouches&&e.changedTouches.length?{x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY}:{x:e.pageX,y:e.pageY}}function eo(e){return e.touches&&e.touches.length||e.changedTouches&&e.changedTouches.length}function to(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{left:0,top:0};if(e){var o={left:n.left+e.offsetLeft,top:n.top+e.offsetTop};return e.parentNode===t?o:to(e.parentNode,t,o)}}function no(e,t,n){return e<n&&e>t?e-1:e>n&&e<t?e+1:e}function oo(e){var t=e.lockOffset,n=e.width,o=e.height,a=t,l=t,r="px";if("string"==typeof t){var i=/^[+-]?\d*(?:\.\d*)?(px|%)$/.exec(t);Rn()(null!==i,'lockOffset value should be a number or a string of a number followed by "px" or "%". Given %s',t),a=parseFloat(t),l=parseFloat(t),r=i[1]}return Rn()(isFinite(a)&&isFinite(l),"lockOffset value should be a finite. Given %s",t),"%"===r&&(a=a*n/100,l=l*o/100),{x:a,y:l}}function ao(e){var t=e.height,n=e.width,o=e.lockOffset,a=Array.isArray(o)?o:[o,o];Rn()(2===a.length,"lockOffset prop of SortableContainer should be a single value or an array of exactly two values. Given %s",o);var l=En(a,2),r=l[0],i=l[1];return[oo({height:t,lockOffset:r,width:n}),oo({height:t,lockOffset:i,width:n})]}function lo(e){return e instanceof HTMLElement?function(e){var t=window.getComputedStyle(e),n=/(auto|scroll)/;return["overflow","overflowX","overflowY"].find((function(e){return n.test(t[e])}))}(e)?e:lo(e.parentNode):null}function ro(e){var t=window.getComputedStyle(e);return"grid"===t.display?{x:Jn(t.gridColumnGap),y:Jn(t.gridRowGap)}:{x:0,y:0}}var io="BUTTON",so="INPUT",co="OPTION",uo="TEXTAREA",po="SELECT";function mo(e){var t,n="input, textarea, select, canvas, [contenteditable]",o=e.querySelectorAll(n),a=e.cloneNode(!0);return(t=a.querySelectorAll(n),function(e){if(Array.isArray(e))return yn(e)}(t)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||wn(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).forEach((function(e,t){"file"!==e.type&&(e.value=o[t].value),"radio"===e.type&&e.name&&(e.name="__sortableClone__".concat(e.name)),"CANVAS"===e.tagName&&o[t].width>0&&o[t].height>0&&e.getContext("2d").drawImage(o[t],0,0)})),a}function ho(e){return null!=e.sortableHandle}var go=function(){function e(t,n){Cn(this,e),this.container=t,this.onScrollCallback=n}return Mn(e,[{key:"clear",value:function(){null!=this.interval&&(clearInterval(this.interval),this.interval=null)}},{key:"update",value:function(e){var t=this,n=e.translate,o=e.minTranslate,a=e.maxTranslate,l=e.width,r=e.height,i={x:0,y:0},s={x:1,y:1},c=this.container,u=c.scrollTop,d=c.scrollLeft,p=c.scrollHeight,m=c.scrollWidth,h=0===u,g=p-u-c.clientHeight==0,v=0===d,b=m-d-c.clientWidth==0;n.y>=a.y-r/2&&!g?(i.y=1,s.y=10*Math.abs((a.y-r/2-n.y)/r)):n.x>=a.x-l/2&&!b?(i.x=1,s.x=10*Math.abs((a.x-l/2-n.x)/l)):n.y<=o.y+r/2&&!h?(i.y=-1,s.y=10*Math.abs((n.y-r/2-o.y)/r)):n.x<=o.x+l/2&&!v&&(i.x=-1,s.x=10*Math.abs((n.x-l/2-o.x)/l)),this.interval&&(this.clear(),this.isAutoScrolling=!1),0===i.x&&0===i.y||(this.interval=setInterval((function(){t.isAutoScrolling=!0;var e={left:s.x*i.x,top:s.y*i.y};t.container.scrollTop+=e.top,t.container.scrollLeft+=e.left,t.onScrollCallback(e)}),5))}}]),e}(),vo={axis:Hn().oneOf(["x","y","xy"]),contentWindow:Hn().any,disableAutoscroll:Hn().bool,distance:Hn().number,getContainer:Hn().func,getHelperDimensions:Hn().func,helperClass:Hn().string,helperContainer:Hn().oneOfType([Hn().func,"undefined"==typeof HTMLElement?Hn().any:Hn().instanceOf(HTMLElement)]),hideSortableGhost:Hn().bool,keyboardSortingTransitionDuration:Hn().number,lockAxis:Hn().string,lockOffset:Hn().oneOfType([Hn().number,Hn().string,Hn().arrayOf(Hn().oneOfType([Hn().number,Hn().string]))]),lockToContainerEdges:Hn().bool,onSortEnd:Hn().func,onSortMove:Hn().func,onSortOver:Hn().func,onSortStart:Hn().func,pressDelay:Hn().number,pressThreshold:Hn().number,keyCodes:Hn().shape({lift:Hn().arrayOf(Hn().number),drop:Hn().arrayOf(Hn().number),cancel:Hn().arrayOf(Hn().number),up:Hn().arrayOf(Hn().number),down:Hn().arrayOf(Hn().number)}),shouldCancelStart:Hn().func,transitionDuration:Hn().number,updateBeforeSortStart:Hn().func,useDragHandle:Hn().bool,useWindowAsScrollContainer:Hn().bool},bo={lift:[32],drop:[32],cancel:[27],up:[38,37],down:[40,39]},fo={axis:"y",disableAutoscroll:!1,distance:0,getHelperDimensions:function(e){var t=e.node;return{height:t.offsetHeight,width:t.offsetWidth}},hideSortableGhost:!0,lockOffset:"50%",lockToContainerEdges:!1,pressDelay:0,pressThreshold:5,keyCodes:bo,shouldCancelStart:function(e){return-1!==[so,uo,po,co,io].indexOf(e.target.tagName)||!!Wn(e.target,(function(e){return"true"===e.contentEditable}))},transitionDuration:300,useWindowAsScrollContainer:!1},ko=Object.keys(vo);function _o(e){Rn()(!(e.distance&&e.pressDelay),"Attempted to set both `pressDelay` and `distance` on SortableContainer, you may only use one or the other, not both at the same time.")}function yo(e,t){try{var n=e()}catch(e){return t(!0,e)}return n&&n.then?n.then(t.bind(null,!1),t.bind(null,!0)):t(!1,value)}var wo=(0,u.createContext)({manager:{}});function Eo(e){var t,n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{withRef:!1};return n=t=function(t){function n(e){var t;Cn(this,n),Sn(In(In(t=On(this,Bn(n).call(this,e)))),"state",{}),Sn(In(In(t)),"handleStart",(function(e){var n=t.props,o=n.distance,a=n.shouldCancelStart;if(2!==e.button&&!a(e)){t.touched=!0,t.position=Zn(e);var l=Wn(e.target,(function(e){return null!=e.sortableInfo}));if(l&&l.sortableInfo&&t.nodeIsChild(l)&&!t.state.sorting){var r=t.props.useDragHandle,i=l.sortableInfo,s=i.index,c=i.collection;if(i.disabled)return;if(r&&!Wn(e.target,ho))return;t.manager.active={collection:c,index:s},eo(e)||"A"!==e.target.tagName||e.preventDefault(),o||(0===t.props.pressDelay?t.handlePress(e):t.pressTimer=setTimeout((function(){return t.handlePress(e)}),t.props.pressDelay))}}})),Sn(In(In(t)),"nodeIsChild",(function(e){return e.sortableInfo.manager===t.manager})),Sn(In(In(t)),"handleMove",(function(e){var n=t.props,o=n.distance,a=n.pressThreshold;if(!t.state.sorting&&t.touched&&!t._awaitingUpdateBeforeSortStart){var l=Zn(e),r={x:t.position.x-l.x,y:t.position.y-l.y},i=Math.abs(r.x)+Math.abs(r.y);t.delta=r,o||a&&!(i>=a)?o&&i>=o&&t.manager.isActive()&&t.handlePress(e):(clearTimeout(t.cancelTimer),t.cancelTimer=setTimeout(t.cancel,0))}})),Sn(In(In(t)),"handleEnd",(function(){t.touched=!1,t.cancel()})),Sn(In(In(t)),"cancel",(function(){var e=t.props.distance;t.state.sorting||(e||clearTimeout(t.pressTimer),t.manager.active=null)})),Sn(In(In(t)),"handlePress",(function(e){try{var n=t.manager.getActive(),o=function(){if(n){var o=function(){var n=p.sortableInfo.index,o=Yn(p),a=ro(t.container),c=t.scrollContainer.getBoundingClientRect(),g=r({index:n,node:p,collection:m});if(t.node=p,t.margin=o,t.gridGap=a,t.width=g.width,t.height=g.height,t.marginOffset={x:t.margin.left+t.margin.right+t.gridGap.x,y:Math.max(t.margin.top,t.margin.bottom,t.gridGap.y)},t.boundingClientRect=p.getBoundingClientRect(),t.containerBoundingRect=c,t.index=n,t.newIndex=n,t.axis={x:l.indexOf("x")>=0,y:l.indexOf("y")>=0},t.offsetEdge=to(p,t.container),t.initialOffset=Zn(h?xn({},e,{pageX:t.boundingClientRect.left,pageY:t.boundingClientRect.top}):e),t.initialScroll={left:t.scrollContainer.scrollLeft,top:t.scrollContainer.scrollTop},t.initialWindowScroll={left:window.pageXOffset,top:window.pageYOffset},t.helper=t.helperContainer.appendChild(mo(p)),Un(t.helper,{boxSizing:"border-box",height:"".concat(t.height,"px"),left:"".concat(t.boundingClientRect.left-o.left,"px"),pointerEvents:"none",position:"fixed",top:"".concat(t.boundingClientRect.top-o.top,"px"),width:"".concat(t.width,"px")}),h&&t.helper.focus(),s&&(t.sortableGhost=p,Un(p,{opacity:0,visibility:"hidden"})),t.minTranslate={},t.maxTranslate={},h){var v=d?{top:0,left:0,width:t.contentWindow.innerWidth,height:t.contentWindow.innerHeight}:t.containerBoundingRect,b=v.top,f=v.left,k=v.width,_=b+v.height,y=f+k;t.axis.x&&(t.minTranslate.x=f-t.boundingClientRect.left,t.maxTranslate.x=y-(t.boundingClientRect.left+t.width)),t.axis.y&&(t.minTranslate.y=b-t.boundingClientRect.top,t.maxTranslate.y=_-(t.boundingClientRect.top+t.height))}else t.axis.x&&(t.minTranslate.x=(d?0:c.left)-t.boundingClientRect.left-t.width/2,t.maxTranslate.x=(d?t.contentWindow.innerWidth:c.left+c.width)-t.boundingClientRect.left-t.width/2),t.axis.y&&(t.minTranslate.y=(d?0:c.top)-t.boundingClientRect.top-t.height/2,t.maxTranslate.y=(d?t.contentWindow.innerHeight:c.top+c.height)-t.boundingClientRect.top-t.height/2);i&&i.split(" ").forEach((function(e){return t.helper.classList.add(e)})),t.listenerNode=e.touches?e.target:t.contentWindow,h?(t.listenerNode.addEventListener("wheel",t.handleKeyEnd,!0),t.listenerNode.addEventListener("mousedown",t.handleKeyEnd,!0),t.listenerNode.addEventListener("keydown",t.handleKeyDown)):(zn.move.forEach((function(e){return t.listenerNode.addEventListener(e,t.handleSortMove,!1)})),zn.end.forEach((function(e){return t.listenerNode.addEventListener(e,t.handleSortEnd,!1)}))),t.setState({sorting:!0,sortingIndex:n}),u&&u({node:p,index:n,collection:m,isKeySorting:h,nodes:t.manager.getOrderedRefs(),helper:t.helper},e),h&&t.keyMove(0)},a=t.props,l=a.axis,r=a.getHelperDimensions,i=a.helperClass,s=a.hideSortableGhost,c=a.updateBeforeSortStart,u=a.onSortStart,d=a.useWindowAsScrollContainer,p=n.node,m=n.collection,h=t.manager.isKeySorting,g=function(){if("function"==typeof c){t._awaitingUpdateBeforeSortStart=!0;var n=yo((function(){var t=p.sortableInfo.index;return Promise.resolve(c({collection:m,index:t,node:p,isKeySorting:h},e)).then((function(){}))}),(function(e,n){if(t._awaitingUpdateBeforeSortStart=!1,e)throw n;return n}));if(n&&n.then)return n.then((function(){}))}}();return g&&g.then?g.then(o):o()}}();return Promise.resolve(o&&o.then?o.then((function(){})):void 0)}catch(e){return Promise.reject(e)}})),Sn(In(In(t)),"handleSortMove",(function(e){var n=t.props.onSortMove;"function"==typeof e.preventDefault&&e.cancelable&&e.preventDefault(),t.updateHelperPosition(e),t.animateNodes(),t.autoscroll(),n&&n(e)})),Sn(In(In(t)),"handleSortEnd",(function(e){var n=t.props,o=n.hideSortableGhost,a=n.onSortEnd,l=t.manager,r=l.active.collection,i=l.isKeySorting,s=t.manager.getOrderedRefs();t.listenerNode&&(i?(t.listenerNode.removeEventListener("wheel",t.handleKeyEnd,!0),t.listenerNode.removeEventListener("mousedown",t.handleKeyEnd,!0),t.listenerNode.removeEventListener("keydown",t.handleKeyDown)):(zn.move.forEach((function(e){return t.listenerNode.removeEventListener(e,t.handleSortMove)})),zn.end.forEach((function(e){return t.listenerNode.removeEventListener(e,t.handleSortEnd)})))),t.helper.parentNode.removeChild(t.helper),o&&t.sortableGhost&&Un(t.sortableGhost,{opacity:"",visibility:""});for(var c=0,u=s.length;c<u;c++){var d=s[c],p=d.node;d.edgeOffset=null,d.boundingClientRect=null,qn(p,null),Gn(p,null),d.translate=null}t.autoScroller.clear(),t.manager.active=null,t.manager.isKeySorting=!1,t.setState({sorting:!1,sortingIndex:null}),"function"==typeof a&&a({collection:r,newIndex:t.newIndex,oldIndex:t.index,isKeySorting:i,nodes:s},e),t.touched=!1})),Sn(In(In(t)),"autoscroll",(function(){var e=t.props.disableAutoscroll,n=t.manager.isKeySorting;if(e)t.autoScroller.clear();else{if(n){var o=xn({},t.translate),a=0,l=0;return t.axis.x&&(o.x=Math.min(t.maxTranslate.x,Math.max(t.minTranslate.x,t.translate.x)),a=t.translate.x-o.x),t.axis.y&&(o.y=Math.min(t.maxTranslate.y,Math.max(t.minTranslate.y,t.translate.y)),l=t.translate.y-o.y),t.translate=o,qn(t.helper,t.translate),t.scrollContainer.scrollLeft+=a,void(t.scrollContainer.scrollTop+=l)}t.autoScroller.update({height:t.height,maxTranslate:t.maxTranslate,minTranslate:t.minTranslate,translate:t.translate,width:t.width})}})),Sn(In(In(t)),"onAutoScroll",(function(e){t.translate.x+=e.left,t.translate.y+=e.top,t.animateNodes()})),Sn(In(In(t)),"handleKeyDown",(function(e){var n=e.keyCode,o=t.props,a=o.shouldCancelStart,l=o.keyCodes,r=xn({},bo,void 0===l?{}:l);t.manager.active&&!t.manager.isKeySorting||!(t.manager.active||r.lift.includes(n)&&!a(e)&&t.isValidSortingTarget(e))||(e.stopPropagation(),e.preventDefault(),r.lift.includes(n)&&!t.manager.active?t.keyLift(e):r.drop.includes(n)&&t.manager.active?t.keyDrop(e):r.cancel.includes(n)?(t.newIndex=t.manager.active.index,t.keyDrop(e)):r.up.includes(n)?t.keyMove(-1):r.down.includes(n)&&t.keyMove(1))})),Sn(In(In(t)),"keyLift",(function(e){var n=e.target,o=Wn(n,(function(e){return null!=e.sortableInfo})).sortableInfo,a=o.index,l=o.collection;t.initialFocusedNode=n,t.manager.isKeySorting=!0,t.manager.active={index:a,collection:l},t.handlePress(e)})),Sn(In(In(t)),"keyMove",(function(e){var n=t.manager.getOrderedRefs(),o=n[n.length-1].node.sortableInfo.index,a=t.newIndex+e,l=t.newIndex;if(!(a<0||a>o)){t.prevIndex=l,t.newIndex=a;var r=no(t.newIndex,t.prevIndex,t.index),i=n.find((function(e){return e.node.sortableInfo.index===r})),s=i.node,c=t.containerScrollDelta,u=i.boundingClientRect||Qn(s,c),d=i.translate||{x:0,y:0},p=u.top+d.y-c.top,m=u.left+d.x-c.left,h=l<a,g=h&&t.axis.x?s.offsetWidth-t.width:0,v=h&&t.axis.y?s.offsetHeight-t.height:0;t.handleSortMove({pageX:m+g,pageY:p+v,ignoreTransition:0===e})}})),Sn(In(In(t)),"keyDrop",(function(e){t.handleSortEnd(e),t.initialFocusedNode&&t.initialFocusedNode.focus()})),Sn(In(In(t)),"handleKeyEnd",(function(e){t.manager.active&&t.keyDrop(e)})),Sn(In(In(t)),"isValidSortingTarget",(function(e){var n=t.props.useDragHandle,o=e.target,a=Wn(o,(function(e){return null!=e.sortableInfo}));return a&&a.sortableInfo&&!a.sortableInfo.disabled&&(n?ho(o):o.sortableInfo)}));var o=new Fn;return _o(e),t.manager=o,t.wrappedInstance=(0,u.createRef)(),t.sortableContextValue={manager:o},t.events={end:t.handleEnd,move:t.handleMove,start:t.handleStart},t}return Dn(n,t),Mn(n,[{key:"componentDidMount",value:function(){var e=this,t=this.props.useWindowAsScrollContainer,n=this.getContainer();Promise.resolve(n).then((function(n){e.container=n,e.document=e.container.ownerDocument||document;var o=e.props.contentWindow||e.document.defaultView||window;e.contentWindow="function"==typeof o?o():o,e.scrollContainer=t?e.document.scrollingElement||e.document.documentElement:lo(e.container)||e.container,e.autoScroller=new go(e.scrollContainer,e.onAutoScroll),Object.keys(e.events).forEach((function(t){return zn[t].forEach((function(n){return e.container.addEventListener(n,e.events[t],!1)}))})),e.container.addEventListener("keydown",e.handleKeyDown)}))}},{key:"componentWillUnmount",value:function(){var e=this;this.helper&&this.helper.parentNode&&this.helper.parentNode.removeChild(this.helper),this.container&&(Object.keys(this.events).forEach((function(t){return zn[t].forEach((function(n){return e.container.removeEventListener(n,e.events[t])}))})),this.container.removeEventListener("keydown",this.handleKeyDown))}},{key:"updateHelperPosition",value:function(e){var t=this.props,n=t.lockAxis,o=t.lockOffset,a=t.lockToContainerEdges,l=t.transitionDuration,r=t.keyboardSortingTransitionDuration,i=void 0===r?l:r,s=this.manager.isKeySorting,c=e.ignoreTransition,u=Zn(e),d={x:u.x-this.initialOffset.x,y:u.y-this.initialOffset.y};if(d.y-=window.pageYOffset-this.initialWindowScroll.top,d.x-=window.pageXOffset-this.initialWindowScroll.left,this.translate=d,a){var p=En(ao({height:this.height,lockOffset:o,width:this.width}),2),m=p[0],h=p[1],g={x:this.width/2-m.x,y:this.height/2-m.y},v={x:this.width/2-h.x,y:this.height/2-h.y};d.x=Kn(this.minTranslate.x+g.x,this.maxTranslate.x-v.x,d.x),d.y=Kn(this.minTranslate.y+g.y,this.maxTranslate.y-v.y,d.y)}"x"===n?d.y=0:"y"===n&&(d.x=0),s&&i&&!c&&Gn(this.helper,i),qn(this.helper,d)}},{key:"animateNodes",value:function(){var e=this.props,t=e.transitionDuration,n=e.hideSortableGhost,o=e.onSortOver,a=this.containerScrollDelta,l=this.windowScrollDelta,r=this.manager.getOrderedRefs(),i=this.offsetEdge.left+this.translate.x+a.left,s=this.offsetEdge.top+this.translate.y+a.top,c=this.manager.isKeySorting,u=this.newIndex;this.newIndex=null;for(var d=0,p=r.length;d<p;d++){var m=r[d].node,h=m.sortableInfo.index,g=m.offsetWidth,v=m.offsetHeight,b={height:this.height>v?v/2:this.height/2,width:this.width>g?g/2:this.width/2},f=c&&h>this.index&&h<=u,k=c&&h<this.index&&h>=u,_={x:0,y:0},y=r[d].edgeOffset;y||(y=to(m,this.container),r[d].edgeOffset=y,c&&(r[d].boundingClientRect=Qn(m,a)));var w=d<r.length-1&&r[d+1],E=d>0&&r[d-1];w&&!w.edgeOffset&&(w.edgeOffset=to(w.node,this.container),c&&(w.boundingClientRect=Qn(w.node,a))),h!==this.index?(t&&Gn(m,t),this.axis.x?this.axis.y?k||h<this.index&&(i+l.left-b.width<=y.left&&s+l.top<=y.top+b.height||s+l.top+b.height<=y.top)?(_.x=this.width+this.marginOffset.x,y.left+_.x>this.containerBoundingRect.width-b.width&&w&&(_.x=w.edgeOffset.left-y.left,_.y=w.edgeOffset.top-y.top),null===this.newIndex&&(this.newIndex=h)):(f||h>this.index&&(i+l.left+b.width>=y.left&&s+l.top+b.height>=y.top||s+l.top+b.height>=y.top+v))&&(_.x=-(this.width+this.marginOffset.x),y.left+_.x<this.containerBoundingRect.left+b.width&&E&&(_.x=E.edgeOffset.left-y.left,_.y=E.edgeOffset.top-y.top),this.newIndex=h):f||h>this.index&&i+l.left+b.width>=y.left?(_.x=-(this.width+this.marginOffset.x),this.newIndex=h):(k||h<this.index&&i+l.left<=y.left+b.width)&&(_.x=this.width+this.marginOffset.x,null==this.newIndex&&(this.newIndex=h)):this.axis.y&&(f||h>this.index&&s+l.top+b.height>=y.top?(_.y=-(this.height+this.marginOffset.y),this.newIndex=h):(k||h<this.index&&s+l.top<=y.top+b.height)&&(_.y=this.height+this.marginOffset.y,null==this.newIndex&&(this.newIndex=h))),qn(m,_),r[d].translate=_):n&&(this.sortableGhost=m,Un(m,{opacity:0,visibility:"hidden"}))}null==this.newIndex&&(this.newIndex=this.index),c&&(this.newIndex=u);var S=c?this.prevIndex:u;o&&this.newIndex!==S&&o({collection:this.manager.active.collection,index:this.index,newIndex:this.newIndex,oldIndex:S,isKeySorting:c,nodes:r,helper:this.helper})}},{key:"getWrappedInstance",value:function(){return Rn()(a.withRef,"To access the wrapped instance, you need to pass in {withRef: true} as the second argument of the SortableContainer() call"),this.wrappedInstance.current}},{key:"getContainer",value:function(){var e=this.props.getContainer;return"function"!=typeof e?(0,o.findDOMNode)(this):e(a.withRef?this.getWrappedInstance():void 0)}},{key:"render",value:function(){var t=a.withRef?this.wrappedInstance:null;return(0,u.createElement)(wo.Provider,{value:this.sortableContextValue},(0,u.createElement)(e,c({ref:t},jn(this.props,ko))))}},{key:"helperContainer",get:function(){var e=this.props.helperContainer;return"function"==typeof e?e():this.props.helperContainer||this.document.body}},{key:"containerScrollDelta",get:function(){return this.props.useWindowAsScrollContainer?{left:0,top:0}:{left:this.scrollContainer.scrollLeft-this.initialScroll.left,top:this.scrollContainer.scrollTop-this.initialScroll.top}}},{key:"windowScrollDelta",get:function(){return{left:this.contentWindow.pageXOffset-this.initialWindowScroll.left,top:this.contentWindow.pageYOffset-this.initialWindowScroll.top}}}]),n}(u.Component),Sn(t,"displayName",Xn("sortableList",e)),Sn(t,"defaultProps",fo),Sn(t,"propTypes",vo),n}var So={index:Hn().number.isRequired,collection:Hn().oneOfType([Hn().number,Hn().string]),disabled:Hn().bool},xo=Object.keys(So);function Co(e){var t,n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{withRef:!1};return n=t=function(t){function n(){var e,t;Cn(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return Sn(In(In(t=On(this,(e=Bn(n)).call.apply(e,[this].concat(a))))),"wrappedInstance",(0,u.createRef)()),t}return Dn(n,t),Mn(n,[{key:"componentDidMount",value:function(){this.register()}},{key:"componentDidUpdate",value:function(e){this.node&&(e.index!==this.props.index&&(this.node.sortableInfo.index=this.props.index),e.disabled!==this.props.disabled&&(this.node.sortableInfo.disabled=this.props.disabled)),e.collection!==this.props.collection&&(this.unregister(e.collection),this.register())}},{key:"componentWillUnmount",value:function(){this.unregister()}},{key:"register",value:function(){var e=this.props,t=e.collection,n=e.disabled,a=e.index,l=(0,o.findDOMNode)(this);l.sortableInfo={collection:t,disabled:n,index:a,manager:this.context.manager},this.node=l,this.ref={node:l},this.context.manager.add(t,this.ref)}},{key:"unregister",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.collection;this.context.manager.remove(e,this.ref)}},{key:"getWrappedInstance",value:function(){return Rn()(a.withRef,"To access the wrapped instance, you need to pass in {withRef: true} as the second argument of the SortableElement() call"),this.wrappedInstance.current}},{key:"render",value:function(){var t=a.withRef?this.wrappedInstance:null;return(0,u.createElement)(e,c({ref:t},jn(this.props,xo)))}}]),n}(u.Component),Sn(t,"displayName",Xn("sortableElement",e)),Sn(t,"contextType",wo),Sn(t,"propTypes",So),Sn(t,"defaultProps",{collection:0}),n}const To=Eo((e=>{let{children:t}=e;return(0,o.createElement)("div",{className:"ugb-sort-control__container"},t)})),Mo=Co((e=>{let{value:t,key:n,index:a,...l}=e;return(0,o.createElement)("div",c({className:"ugb-sort-control__item"},l),t)}));let No=!1;const Io=(0,o.memo)((e=>{const[t,n]=ut(e.attribute,e.responsive,e.hover),[a,l]=vt(e);let r=void 0===e.values?t:Array.isArray(e.values)?[...e.values]:"string"==typeof e.values?e.values.split(","):t;for(r=r?r.splice(0,e.num):(0,v.range)(e.num).map((e=>e+1));r.length<e.num;)r.push(r.length+1);const i=void 0===e.onChange?n:e.onChange,s=(0,v.range)(e.num).map((e=>(e+1).toString())),u=r.map((e=>e.toString()));return(0,o.createElement)(mt,c({},l,{className:g()(["ugb-sort-control",e.className,`ugb-sort-control--axis-${e.axis}`])}),(0,o.createElement)(To,c({},a,{onSortStart:()=>No=!0,onSortOver:t=>{let{newIndex:n}=t;e.onHover(n)},onSortEnd:e=>{let{oldIndex:t,newIndex:n}=e;No=!1;const o=((e,t,n)=>(e.splice(t<n?n+1:n,0,e[t]),e.splice(t<n?t:t+1,1),e))(r,t,n);(0,v.isEqual)(o.map((e=>e.toString())),s)?i("",{oldIndex:0,newIndex:0}):i([...o],{oldIndex:t,newIndex:n})},axis:e.axis}),r.map(((t,n)=>(0,o.createElement)(Mo,{key:n,index:n,value:t,onMouseEnter:()=>{No||e.onHover(n)},onMouseLeave:()=>{No||e.onHover(null)}})))),(0,o.createElement)(ft,{allowReset:e.allowReset,showReset:!(0,v.isEqual)(u,s),value:r,default:e.default,onChange:()=>i("",{oldIndex:0,newIndex:0})}))}));function Oo(e){this.value=e,this.subscribers=[],this.getValue=function(){return this.value},this.setValue=function(e){if(this.getValue()===e)return;this.value=e;const t=this;this.subscribers.forEach((function(e){e(t.value)}))},this.subscribe=function(e){this.subscribers.indexOf(e)>-1||this.subscribers.push(e)},this.unsubscribe=function(e){this.subscribers=this.subscribers.filter((function(t){return t!==e}))}}function Bo(e,t){void 0===t&&(t=null);const[,n]=(0,o.useState)(),a=Po.getState(e,t),l=a.getValue();function r(){n({})}return(0,o.useEffect)((function(){return a.subscribe(r),function(){a.unsubscribe(r)}})),[l,function(e){a.setValue(e)}]}Io.defaultProps={className:"",help:"",label:(0,d.__)("Column Arrangement",s.i18n),num:2,axis:"x",values:null,onChange:()=>{},onHover:()=>{},hasReset:!1};const Po=new function(){this.value={},this.init=function(e){for(const t in e)this.setState(t,e[t])},this.getState=function(e,t){return void 0===this.value[e]&&this.setState(e,t),this.value[e]},this.setState=function(e,t){this.value[e]=new Oo(t)}},Do=(0,o.createElement)(Ie,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(Ne,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})),Lo=(0,o.createElement)(Ie,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,o.createElement)(Ne,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})),Ro=()=>{},Ao=(0,v.debounce)((e=>{e&&e.getBoundingClientRect().top<200&&e.scrollIntoView({inline:"start",block:"start",behavior:"instant"})}),0,{leading:!1,trailing:!0}),Ho=(0,o.forwardRef)(((e,t)=>{let{isOpened:n,icon:a,title:l,isPremiumPanel:i,showModifiedIndicator:s,checked:u,hasToggle:d,onChange:p,setIsOpened:m,...h}=e;return l?(0,o.createElement)("h2",{className:"components-panel__body-title"},(0,o.createElement)(r.Button,c({className:"components-panel__body-toggle stk-panel","aria-expanded":n,ref:t},h),(0,o.createElement)("span",{"aria-hidden":"true"},(0,o.createElement)(r.Icon,{className:"components-panel__arrow",icon:n?Do:Lo})),d&&(0,o.createElement)(r.FormToggle,{className:"ugb-toggle-panel-form-toggle",checked:u,onClick:e=>{e.stopPropagation(),e.preventDefault(),u&&n||u||n||m((e=>!e)),p&&p(!u)},"aria-describedby":l}),l,(0,o.createElement)("span",{className:g()(["stk-panel-modified-indicator",{"stk--visible":s}])}),i&&(0,o.createElement)("div",{className:"stk-pulsating-circle"}),a&&(0,o.createElement)(r.Icon,{icon:a,className:"components-panel__icon",size:20}))):null})),Fo=(0,o.forwardRef)(((e,t)=>{let{buttonProps:n={},children:a,className:l,icon:r,initialOpen:i,onToggle:s=Ro,isOpen:u=null,title:d,id:p="",checked:m,hasToggle:h,onChange:v=Ro,isPremiumPanel:b=!1,showModifiedIndicator:f=!1}=e;const{name:k}=(0,ke.useBlockEditContext)(),[_,y]=Bo(`panelCache-${k}-${p}-${d}`,void 0!==i&&i),w=null===u?_:u,E=(0,o.useRef)(),S=g()("components-panel__body","ugb-toggle-panel-body",l,{"is-opened":w,[`ugb-panel--${p}`]:p,"stk--premium-panel":b});return(0,o.createElement)("div",{className:S,ref:(0,Ye.useMergeRefs)([E,t])},(0,o.createElement)(Ho,c({icon:r,isOpened:w,onClick:e=>{e.preventDefault();const t=!w;y(t),s(t),t?setTimeout((()=>{Ao(E.current)}),0):Ao(E.current)},title:d,checked:m,hasToggle:void 0===h?!!v:h,onChange:v,setIsOpened:y,isPremiumPanel:b,showModifiedIndicator:f},n)),"function"==typeof a?a({opened:!0}):a)}));Fo.displayName="PanelBody";const Vo=Fo,jo=(0,o.memo)((e=>(0,o.createElement)(Vo,e)));jo.defaultProps={id:"",className:"",title:(0,d.__)("Settings",s.i18n),checked:!1,onChange:null,initialOpen:!1,hasToggle:!1,onToggle:()=>{},isOpen:null};const zo=jo,$o="v3";let Uo=null,qo=[];const Go=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!Uo||e){const t=await Me()({path:"/stackable/v2/design_library"+(e?"/reset":""),method:"GET"});Uo=await t,e&&((0,m.doAction)("stackable.design-library.reset-cache"),qo=[])}return Uo[t||$o]},Wo=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(!qo[e]){const n=await Me()({path:`/stackable/v2/design/${t||$o}/${e}`,method:"GET"});qo[e]=await n}return qo[e]},Ko=async e=>{let{type:t="",block:n="",mood:o="",plan:a="",colors:l=[],categories:r=[],uikit:i="",search:s="",reset:c=!1,apiVersion:u=""}=e,d=Object.values(await Go(c,u));if(t&&(d=d.filter((e=>{let{type:n}=e;return n===t}))),n){const e=n.replace(/^\w+\//,"");d=d.filter((t=>{let{block:n}=t;return n.endsWith(`/${e}`)}))}return o&&(d=d.filter((e=>{let{mood:t}=e;return t===o}))),a&&(d=d.filter((e=>{let{plan:t}=e;return t===a}))),l&&l.length&&(d=d.filter((e=>{let{colors:t}=e;return t.some((e=>l.includes(e)))}))),r&&r.length&&(d=d.filter((e=>{let{categories:t}=e;return t.some((e=>r.includes(e)))}))),i&&(d=d.filter((e=>{let{uikit:t}=e;return t===i}))),s&&s.toLowerCase().replace(/\s+/," ").trim().split(" ").forEach((e=>{d=d.filter((t=>(0,m.applyFilters)("stackable.design-library.search-properties",["label","plan","tags","categories","colors"],u).some((n=>-1!==t[n].toString().toLowerCase().indexOf(e)))))})),d},Jo=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=await Go(!1,t),o=n[e];let a=await(0,m.applyFilters)("stackable.design-library.get-design",null,e,o,t);return!a&&o.template&&(a=await Wo(e,t)),a},Yo=e=>{const{designId:t,image:n,label:a,onClick:l,plan:i,isPro:s,apiVersion:c,isMultiSelectMode:u=!1,selectedNum:d=!1}=e,[p,m]=(0,o.useState)(!1),h=g()(["ugb-design-library-item"],{"ugb--is-busy":p,[`ugb--is-${i}`]:!s&&"free"!==i,"ugb-design-library-item--toggle":u,"ugb--is-toggled":u&&d});return(0,o.createElement)("div",{className:h,"data-selected-num":u?d:void 0},p&&(0,o.createElement)("span",{className:"ugb-design-library-item__spinner","data-testid":"spinner"},(0,o.createElement)(r.Spinner,null)),!s&&"free"!==i&&(0,o.createElement)("span",{className:"stk-pulsating-circle",role:"presentation"}),(0,o.createElement)("button",{className:"ugb-design-library-item__image",onClick:()=>{(s||"free"===i)&&(u?l(t):(m(!0),Jo(t,c).then((e=>{const t=m(!1);l(e,t)}))))}},!s&&"free"!==i&&(0,o.createElement)(It,{type:"design-library",showImage:!1,showHideNote:!1}),(0,o.createElement)("img",{src:n,alt:a,loading:"lazy"})),(0,o.createElement)("footer",null,(0,o.createElement)("span",null,a)))};Yo.defaultProps={designId:"",image:"",label:"",onClick:()=>{},plan:"free",isPro:s.isPro,premiumLabel:(0,d.__)("Go Premium",s.i18n),apiVersion:""};const Xo=Yo,Qo=e=>{const{className:t="",designs:n,isBusy:a,onSelect:l,onSelectMulti:i,apiVersion:c,isMultiSelectMode:u=!1,selectedDesigns:p=[]}=e,m=g()(["ugb-design-library-items",t],{[`ugb-design-library-items--columns-${e.columns}`]:!a&&e.columns});return(0,o.createElement)("div",{className:m},(n||[]).map(((e,t)=>{const n=!!u&&p.indexOf(e.id)+1;return(0,o.createElement)(Xo,{key:t,type:e.type,block:e.block,template:e.template,plan:e.plan,designId:e.id,image:e.image,label:e.label,apiVersion:c,isMultiSelectMode:u,selectedNum:n,onClick:function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;u?i&&i(t,n):l(t,e,n)}})})),a&&(0,o.createElement)("div",{className:"ugb-design-library-search__spinner","data-testid":"spinner"},(0,o.createElement)(r.Spinner,null)),!a&&!(n||[]).length&&(0,o.createElement)("p",{className:"components-base-control__help","data-testid":"nothing-found-note"},(0,d.__)("No designs found",s.i18n)))};Qo.defaultProps={designs:[],columns:1,onSelect:()=>{},isBusy:!1,apiVersion:""};const Zo=Qo;n(7385);const ea=[{value:"layout",title:(0,d.__)("Layout",s.i18n),label:(0,d.__)("Layout Tab",s.i18n),icon:"align-left"},{value:"style",title:(0,d.__)("Style",s.i18n),label:(0,d.__)("Style Tab",s.i18n),icon:"admin-appearance"},{value:"advanced",title:(0,d.__)("Advanced",s.i18n),label:(0,d.__)("Advanced Tab",s.i18n),icon:"admin-generic"}],ta=["layout","style","advanced"];class na extends o.Component{constructor(){super(...arguments),this.tabsToUse=this.props.tabs||ta,this.state={activeTab:this.props.initialTab?this.props.initialTab:this.tabsToUse[0]},this.onButtonPanelClick=this.onButtonPanelClick.bind(this),this.updateSidebarPanelTab=this.updateSidebarPanelTab.bind(this),this.select=this.select.bind(this),this.containerDiv=(0,o.createRef)(),this.suspendClickListener=!1,this.props.onTabFirstOpen(this.state.activeTab)}updateSidebarPanelTab(e){const t=this.containerDiv.current.closest(".components-panel");t&&setTimeout((()=>{var n;t&&(t.setAttribute("data-ugb-tab",e),null===(n=t.closest(".edit-post-sidebar, .edit-widgets-sidebar, .interface-complementary-area"))||void 0===n||n.classList.add("ugb--has-panel-tabs"))}),1)}componentDidMount(){this.updateSidebarPanelTab(this.state.activeTab),this.props.closeOtherPanels&&s.settings.stackable_auto_collapse_panels&&document.body.addEventListener("click",this.onButtonPanelClick)}componentWillUnmount(){const e=document.querySelector("[data-ugb-tab]");e&&(e.removeAttribute("data-ugb-tab"),e.closest(".edit-post-sidebar, .edit-widgets-sidebar, .interface-complementary-area").classList.remove("ugb--has-panel-tabs")),this.props.closeOtherPanels&&s.settings.stackable_auto_collapse_panels&&document.body.removeEventListener("click",this.onButtonPanelClick)}onButtonPanelClick(e){const t=e.target.closest(".components-panel__body-toggle.stk-panel");var n,o;t&&(0,m.applyFilters)("stackable.panel.tabs.panel-auto-close",!0,t)&&(this.suspendClickListener||(this.suspendClickListener=!0,n=t,[].forEach.call((null===(o=document.querySelector(".edit-post-sidebar, .edit-widgets-sidebar, .block-editor-block-inspector"))||void 0===o?void 0:o.querySelectorAll(".components-panel__body .components-panel__body-toggle.stk-panel"))||[],(e=>{0!==e.offsetHeight&&e.parentElement.parentElement.classList.contains("is-opened")&&n!==e&&(0,m.applyFilters)("stackable.panel.tabs.panel-auto-close",!0,e)&&e.click()})),this.suspendClickListener=!1))}select(e){this.setState({activeTab:e}),this.updateSidebarPanelTab(e),this.props.onClick(e)}render(){const e=g()([this.props.className,"components-panel__body","ugb-panel-tabs"]);return(0,o.createElement)("div",{className:e,style:this.props.style,ref:this.containerDiv},(0,o.createElement)("div",{className:"ugb-panel-tabs__wrapper"},(0,m.applyFilters)("stackable.inspector.tabs",ea).map(((e,t)=>{let{value:n,title:a,label:l,icon:i}=e;return this.tabsToUse.includes(n)?(0,o.createElement)("button",{key:t,onClick:()=>this.select(n),className:g()(["edit-post-sidebar__panel-tab",`ugb-tab--${n}`],{"is-active":this.state.activeTab===n}),"aria-label":l,"data-label":l},(0,o.createElement)(r.Icon,{icon:i}),a):null}))))}}na.defaultProps={className:"",style:{},closeOtherPanels:!0,initialTab:"",onClickPanel:()=>{},onClick:()=>{},tabs:null,onTabFirstOpen:()=>{}};const oa=na,aa=function(e){let{imageFile:t,imageHoverFile:n,imageWidth:a="",imageHeight:l="",label:r,isActive:i=!1}=e;const c=t?t.match(/https?:/i)?t:s.srcUrl?`${s.srcUrl}/${t}`:t:"",u=n?n.match(/https?:/i)?n:s.srcUrl?`${s.srcUrl}/${n}`:n:null;return(0,o.createElement)("span",{className:g()("ugb-design-panel-item",{"is-active":i})},u&&(0,o.createElement)("img",{className:"ugb-design-panel-item__hover-image",src:u,alt:r,width:a,height:l}),c&&(0,o.createElement)("img",{className:"ugb-design-panel-item__image",src:c,alt:r,width:a,height:l}),(0,o.createElement)("span",{className:"design-label"},r))},la=e=>{const t=e.options.filter((e=>!e.premium||s.isPro)).map((t=>({...t,label:(0,o.createElement)(aa,{imageFile:t.image,imageHoverFile:t.hoverImage,imageWidth:t.imageWidth,imageHeight:t.imageHeight,isPro:t.isPro,label:t.label,isActive:t.value===e.selected}),title:t.label,value:t.value}))),n=g()([e.className,"ugb-design-control-wrapper","components-base-control",`ugb-design-control--columns-${e.columns}`]);return(0,o.createElement)("div",{className:n},e.label&&(0,o.createElement)(it,c({label:e.label},e.helpTooltip)),(0,o.createElement)(r.RadioControl,c({},(0,v.omit)(e,["label"]),{className:"ugb-design-control",selected:e.selected,options:t,onChange:e.onChange})))};la.defaultProps={className:"",columns:2,selected:"",options:[],onChange:()=>{}};const ra=[{image:"dist/images/components-design-separator-control-wave-1.fe7d2e5.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Wave",s.i18n),1),value:"wave-1"},{image:"dist/images/components-design-separator-control-straight-1.fba0f99.png",label:(0,d.__)("Straight",s.i18n),value:"straight-1"},{image:"dist/images/components-design-separator-control-wave-2.bc3ff78.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Wave",s.i18n),2),value:"wave-2"},{image:"dist/images/components-design-separator-control-wave-3.9e204d5.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Wave",s.i18n),3),value:"wave-3"},{image:"dist/images/components-design-separator-control-wave-4.b82e3aa.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Wave",s.i18n),4),value:"wave-4"},{image:"dist/images/components-design-separator-control-slant-1.645f0b9.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Slant",s.i18n),1),value:"slant-1"},{image:"dist/images/components-design-separator-control-slant-2.50e3178.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Slant",s.i18n),2),value:"slant-2"},{image:"dist/images/components-design-separator-control-curve-1.277a020.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Curve",s.i18n),1),value:"curve-1"},{image:"dist/images/components-design-separator-control-curve-2.4b8c7da.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Curve",s.i18n),2),value:"curve-2"},{image:"dist/images/components-design-separator-control-curve-3.da5eb6d.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Curve",s.i18n),3),value:"curve-3"},{image:"dist/images/components-design-separator-control-rounded-1.85d0e25.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Rounded",s.i18n),1),value:"rounded-1"},{image:"dist/images/components-design-separator-control-rounded-2.6e57ec5.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Rounded",s.i18n),2),value:"rounded-2"},{image:"dist/images/components-design-separator-control-rounded-3.78cb6da.png",label:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Rounded",s.i18n),3),value:"rounded-3"}];(0,m.addFilter)("stackable.separator.edit.layouts","default",(e=>(0,v.uniqBy)([...e,...ra],"value")));(0,d.__)("Icon",s.i18n);const ia=function(e){return(0,o.createElement)("svg",c({"aria-hidden":"true","data-prefix":"fas","data-icon":"arrow-alt-to-bottom",className:"drop_svg__svg-inline--fa drop_svg__fa-arrow-alt-to-bottom drop_svg__fa-w-12",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 384 512"},e),(0,o.createElement)("path",{fill:"currentColor",d:"M360 480H24c-13.3 0-24-10.7-24-24v-24c0-13.3 10.7-24 24-24h336c13.3 0 24 10.7 24 24v24c0 13.3-10.7 24-24 24zM128 56v136H40.3c-17.8 0-26.7 21.5-14.1 34.1l152.2 152.2c7.5 7.5 19.8 7.5 27.3 0l152.2-152.2c12.6-12.6 3.7-34.1-14.1-34.1H256V56c0-13.3-10.7-24-24-24h-80c-13.3 0-24 10.7-24 24z"}))},sa=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=[...e.FamilyStylesByLicense.free,...t?e.FamilyStylesByLicense.pro.filter((t=>!e.FamilyStylesByLicense.free.includes(t))):[]];return n.map((t=>{let n="";return"duotone"===t.family?n="d":"classic"===t.family?n=t.style[0]:"sharp"===t.family&&(n="s"+t.style[0]),{className:`fa${n} fa-${e.id}`,prefix:`fa${n}`,iconName:e.id}}))};var ca=n(5112);let ua=null,da=null;const pa=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"ugb-custom-icon";return e.match(/(<svg[^>]*class=["'])/)?e.replace(/(<svg[^>]*class=["'])/,`$1${t} `):e.match(/(<svg)/)?e.replace(/(<svg)/,`$1 class="${t}"`):e},ma=e=>{let t=e.replace(/(^[\s\S]*?)(<svg)/gm,"$2").replace(/(<\/svg>)([\s\S]*)/g,"$1");const n=Math.floor(Math.random()*(new Date).getTime())%1e5;t=t.replace(/id="([^"]*)"/g,`id="$1-${n}"`),t=t.replace(/url\(#([^)]*)\)/g,`url(#$1-${n})`),t=t.replace(/href="#([^"]*)"/g,`href="#$1-${n}"`),-1!==t.indexOf("\x3c!--")&&(t=t.replace(/<!--[\s\S]*?-->/gm,""));for(let e=0;e<2;e++)t=t.replace(/\s*<g\s*>([\s\S]*?)<\/g>\s*/gm,"$1");return t=t.replace(/<svg([^>]*)width=(["'])[^"']*["']([^>]*)/g,"<svg$1$3").replace(/<svg([^>]*)height=(["'])[^"']*["']([^>]*)/g,"<svg$1$3"),t},ha=e=>{const[t,n]=(0,o.useState)(""),[a,l]=(0,o.useState)({faIcons:[],iconLibrary:[]}),[i,c]=(0,o.useState)(!1),[u,p]=(0,o.useState)(!1),h=e.returnSVGValue;(0,o.useEffect)((()=>{let e=!0;return clearTimeout(ua),ua=setTimeout((()=>{e&&(c(!0),async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"icon",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.fontAwesomeSearchProIcons;const n=`{ search(version: "${(s.iconsFaKit?s.iconsFaProKitVersion:s.iconsFaFreeKitVersion||"6.5.1")||"6.5.1"}", first: 50, query: "${(e||"info").replace(/["'\\]/g,"")}") {\n\t\t\tid,\n\t\t\tFamilyStylesByLicense {\n\t\t\t\tfree { style, family },\n\t\t\t\t${t?"pro { style, family }":""}\n\t\t\t}\n\t\t} }`,o=(await fetch("https://api.fontawesome.com",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({query:n})}).then((e=>e.json()))).data.search.reduce(((e,n)=>(sa(n,t).forEach((t=>{e.push(t)})),e)),[]),a=(0,m.applyFilters)("stackable.global-settings.inspector.icon-library.search-icons",null,e);return{faIcons:o,iconLibrary:a}}(t).then((t=>{e&&l(t)})).finally((()=>{e&&c(!1)})))}),500),()=>{e=!1,clearTimeout(ua)}}),[t]),(0,o.useEffect)((()=>{da||(da=wp.data.select("core/block-editor").getSettings().mediaUpload),u?wp.data.dispatch("core/block-editor").updateSettings({mediaUpload:null}):wp.data.select("core/block-editor").getSettings().mediaUpload!==da&&wp.data.dispatch("core/block-editor").updateSettings({mediaUpload:da})}),[u]);const v=g()(["ugb-icon-popover__label-container"],{"ugb-icon--has-settings":s.isPro,"ugb-icon--has-upload":h,"ugb-icon--has-reset":e.allowReset}),b=(0,m.applyFilters)("stackable.global-settings.inspector.icon-library.icons",o.Fragment),f=(0,o.createElement)("div",{className:"stk-icon-search-popover-container"},(0,o.createElement)(ca.FileDrop,{onFrameDragEnter:()=>p(!0),onFrameDragLeave:()=>p(!1),onFrameDrop:()=>p(!1),onDrop:t=>{if(!h||!t.length)return void p(!1);if("image/svg+xml"!==t[0].type)return void p(!1);const n=new FileReader;n.onload=function(t){p(!1);const n=ma(pa(t.target.result));(0,m.doAction)("stackable.icon-search-popover.svg-upload",n),e.onChange(n),e.onClose()},n.readAsText(t[0])}},(0,o.createElement)("div",{className:v},(0,o.createElement)(r.TextControl,{className:"ugb-icon-popover__input",value:t,onChange:n,placeholder:(0,d.__)("Type to search icon",s.i18n)}),s.isPro&&(0,o.createElement)(M,{className:"ugb-icon-popover__settings-button",icon:"admin-generic",href:s.settingsUrl+"#icon-settings",target:"_settings",iconSize:"16",label:(0,d.__)("Icon Settings",s.i18n),showTooltip:!0,isSmall:!0,isSecondary:!0}),h&&(0,o.createElement)(M,{onClick:t=>{t.preventDefault();const n=document.createElement("input");n.accept="image/svg+xml",n.type="file",n.onchange=t=>{const n=t.target.files;if(!n.length)return void p(!1);const o=new FileReader;o.onload=function(t){p(!1);const n=ma(pa(t.target.result));(0,m.doAction)("stackable.icon-search-popover.svg-upload",n),e.onChange(n),e.onClose()},o.readAsText(n[0])},n.click()},isSmall:!0,isPrimary:!0,className:"components-range-control__upload"},(0,d.__)("Upload SVG",s.i18n)),e.allowReset&&(0,o.createElement)(M,{onClick:()=>{e.onChange(e.defaultValue||""),e.onClose()},isSmall:!0,isSecondary:!0,className:"components-range-control__reset"},(0,d.__)("Clear icon",s.i18n))),(0,o.createElement)("div",{className:"ugb-icon-popover__iconlist"},i&&(0,o.createElement)(r.Spinner,null),!i&&(0,o.createElement)(b,{icons:a.iconLibrary,onChange:e.onChange,onClose:e.onClose}),!i&&a.faIcons.map(((t,n)=>{let{prefix:a,iconName:l}=t;const r=`${a}-${l}`;return(0,o.createElement)("button",{key:n,className:`components-button ugb-prefix--${a} ugb-icon--${l}`,onClick:async()=>{if(e.returnSVGValue&&e.returnIconName){let t=ls(a,l);t||(await rs(a,l),t=ls(a,l)),e.onChange(ma(t),a,l)}else if(e.returnSVGValue){let t=ls(a,l);t||(await rs(a,l),t=ls(a,l)),e.onChange(ma(t))}else e.onChange(r,a,l);e.onClose()}},(0,o.createElement)(vl,{prefix:a,iconName:l}))})),!i&&!a.faIcons.length&&!a.iconLibrary.length&&(0,o.createElement)("p",{className:"components-base-control__help"},(0,d.__)("No matches found",s.i18n))),h&&u&&(0,o.createElement)("div",{className:"ugb-icon-popover__drop-indicator"},(0,o.createElement)(ia,{height:"40",width:"40"}),(0,d.__)("Drop your SVG here",s.i18n))));return e.__hasPopover?(0,o.createElement)(nt,{className:"ugb-icon-popover",onClose:e.onClose,onEscape:e.onClose,onClickOutside:e.__deprecatedOnClickOutside,position:e.__deprecatedPosition,anchorRef:e.__deprecatedAnchorRef,ref:e.__deprecateUseRef},(0,o.createElement)(r.PanelBody,null,f)):f},ga=()=>{};ha.defaultProps={onChange:ga,onClose:ga,returnSVGValue:!0,allowReset:!0,__deprecatedAnchorRef:void 0,__deprecatedPosition:"center",__deprecatedOnClickOutside:ga,__hasPopover:!1};const va=(0,o.memo)((e=>{const[t,n]=(0,o.useState)(!1),a=(0,o.useRef)(null);return(0,o.useEffect)((()=>{if(t){const e=e=>{var t,o,l,r;null!==(t=window.wp)&&void 0!==t&&null!==(o=t.media)&&void 0!==o&&null!==(l=o.frame)&&void 0!==l&&null!==(r=l.el)&&void 0!==r&&r.clientHeight||e.target.closest(".ugb-button-icon-control__popover")||(null==a?void 0:a.current)===e.target.closest(".ugb-button-icon-control__edit")||e.target.closest(".components-color-picker")||e.target.closest(".react-autosuggest__suggestions-container")||e.target.closest(".components-dropdown__content")||n(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)}}),[t]),(0,o.createElement)(_l,{help:e.help,label:e.label,id:"ugb-button-icon-control",className:g()("ugb-button-icon-control",e.className),allowReset:!0,showReset:e.allowReset||!!e.onToggle&&e.checked,onReset:()=>{e.onReset(),e.onToggle&&e.onToggle(!1)},hasLabel:!e.onToggle},e.onToggle&&(0,o.createElement)(r.ToggleControl,{label:e.label,checked:e.checked,onChange:e.onToggle}),(0,o.createElement)("div",{className:"ugb-button-icon-control__wrapper"},(0,o.createElement)(M,{onClick:()=>n((e=>!e)),className:"ugb-button-icon-control__edit",label:(0,d.__)("Edit",s.i18n),isSecondary:!0,icon:"edit",ref:a}),t&&(0,o.createElement)(nt,{className:"ugb-button-icon-control__popover",focusOnMount:"container",onEscape:()=>n(!1)},(0,o.createElement)(r.PanelBody,null,(void 0!==e.popoverLabel?e.popoverLabel:e.label)&&(0,o.createElement)("h2",{className:"components-panel__body-title"},e.popoverLabel||e.label),e.children))))}));va.defaultProps={help:"",label:"",popoverLabel:void 0,className:"",allowReset:!1,onReset:()=>{},checked:!1,onToggle:void 0};(0,d.__)("Typography",s.i18n);const ba=e=>(0,o.createElement)(o.Fragment,null,e.onChangeBorderType&&(0,o.createElement)(fn,{label:(0,d.__)("Borders",s.i18n),controls:[{value:"",title:(0,d.__)("None",s.i18n)},{value:"solid",title:(0,d.__)("Solid",s.i18n)},{value:"dashed",title:(0,d.__)("Dashed",s.i18n)},{value:"dotted",title:(0,d.__)("Dotted",s.i18n)}],className:"ugb-border-controls__border-type-toolbar",value:e.borderType,onChange:e.onChangeBorderType,fullwidth:!0,isSmall:!0}),e.onChangeBorderWidth&&e.borderType&&(0,o.createElement)(ka,{label:(0,d.__)("Border Width",s.i18n),units:["px"],min:0,max:99,step:1,sliderMax:5,defaultLocked:!0,valueDesktop:{top:e.borderWidthTop,right:e.borderWidthRight,bottom:e.borderWidthBottom,left:e.borderWidthLeft},onChangeDesktop:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeBorderWidth({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):"",borderType:e.borderType?void 0:"solid"})},valueTablet:{top:e.tabletBorderWidthTop,right:e.tabletBorderWidthRight,bottom:e.tabletBorderWidthBottom,left:e.tabletBorderWidthLeft},onChangeTablet:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeTabletBorderWidth({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):""})},valueMobile:{top:e.mobileBorderWidthTop,right:e.mobileBorderWidthRight,bottom:e.mobileBorderWidthBottom,left:e.mobileBorderWidthLeft},onChangeMobile:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeMobileBorderWidth({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):""})},placeholder:"1",placeholderTop:"1",placeholderLeft:"1",placeholderBottom:"1",placeholderRight:"1"}),e.onChangeBorderColor&&e.borderType&&(0,o.createElement)(Et,{value:e.borderColor,onChange:t=>{e.onChangeBorderColor({color:t,borderType:e.borderType?void 0:"solid"})},label:(0,d.__)("Border Color",s.i18n)}));ba.defaultProps={onResetBorder:()=>{},borderType:"",onChangeBorderType:()=>{},borderWidthTop:"",borderWidthRight:"",borderWidthBottom:"",borderWidthLeft:"",tabletBorderWidthTop:"",tabletBorderWidthRight:"",tabletBorderWidthBottom:"",tabletBorderWidthLeft:"",mobileBorderWidthTop:"",mobileBorderWidthRight:"",mobileBorderWidthBottom:"",mobileBorderWidthLeft:"",onChangeBorderWidth:()=>{},onChangeTabletBorderWidth:()=>{},onChangeMobileBorderWidth:()=>{},borderColor:"",onChangeBorderColor:()=>{}};const fa=e=>{const t=(0,v.pick)(e,["label","units","screens","defaultLocked","enableTop","enableRight","enableBottom","enableLeft","className","sliderMax","sliderMin"]),n=Array.isArray(e.min)?e.min:[e.min,e.min,e.min],a=Array.isArray(e.max)?e.max:[e.max,e.max,e.max],l=Array.isArray(e.sliderMin)?e.sliderMin:[e.sliderMin,e.sliderMin,e.sliderMin],r=Array.isArray(e.sliderMax)?e.sliderMax:[e.sliderMax,e.sliderMax,e.sliderMax],i=Array.isArray(e.step)?e.step:[e.step,e.step,e.step];return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Va,{screen:"desktop"},(0,o.createElement)(Ha,c({},t,{min:n[0],max:a[0],sliderMin:l[0],sliderMax:r[0],step:i[0],top:e.valueDesktop.top,right:e.valueDesktop.right,bottom:e.valueDesktop.bottom,left:e.valueDesktop.left,unit:e.valueDesktopUnit||e.units[0],onChange:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeDesktop({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):""})},onChangeUnit:t=>e.onChangeDesktopUnit(t),placeholder:e.placeholder,placeholderTop:e.placeholderTop,placeholderLeft:e.placeholderLeft,placeholderBottom:e.placeholderBottom,placeholderRight:e.placeholderRight}))),(0,o.createElement)(Va,{screen:"tablet"},(0,o.createElement)(Ha,c({},t,{min:n[1],max:a[1],sliderMin:l[1],sliderMax:r[1],step:i[1],top:e.valueTablet.top,right:e.valueTablet.right,bottom:e.valueTablet.bottom,left:e.valueTablet.left,unit:e.valueTabletUnit||e.units[0],onChange:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeTablet({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):""})},onChangeUnit:t=>e.onChangeTabletUnit(t)}))),(0,o.createElement)(Va,{screen:"mobile"},(0,o.createElement)(Ha,c({},t,{min:n[2],max:a[2],sliderMin:l[2],sliderMax:r[2],step:i[2],top:e.valueMobile.top,right:e.valueMobile.right,bottom:e.valueMobile.bottom,left:e.valueMobile.left,unit:e.valueMobileUnit||e.units[0],onChange:t=>{let{top:n,right:o,bottom:a,left:l}=t;e.onChangeMobile({top:n||0===n?parseInt(n,10):"",right:o||0===o?parseInt(o,10):"",bottom:a||0===a?parseInt(a,10):"",left:l||0===l?parseInt(l,10):""})},onChangeUnit:t=>e.onChangeMobileUnit(t)}))))};fa.defaultProps={label:(0,d.__)("Spacing",s.i18n),defaultLocked:!0,units:["px","em","%","vw"],screens:["desktop","tablet","mobile"],min:0,max:1/0,sliderMax:null,sliderMin:null,step:1,enableTop:!0,enableRight:!0,enableBottom:!0,enableLeft:!0,valueDesktop:{},valueTablet:{},valueMobile:{},valueDesktopUnit:"",valueTabletUnit:"",valueMobileUnit:"",onChangeDesktop:()=>{},onChangeTablet:()=>{},onChangeMobile:()=>{},onChangeDesktopUnit:()=>{},onChangeTabletUnit:()=>{},onChangeMobileUnit:()=>{},placeholderTop:"60",placeholderLeft:"35",placeholderBottom:"60",placeholderRight:"35",placeholder:""};const ka=fa,_a=e=>{const{initialOpen:t,...n}=e,[a,l]=(0,o.useState)(t),r=g()(["ugb-pro-control-button__wrapper"],{"ugb-pro-control-button--hidden":!a});return(0,o.createElement)("div",{className:"components-base-control"},(0,o.createElement)("button",{className:"ugb-pro-control-more-dots",onClick:()=>l((e=>!e))},(0,o.createElement)("div",{className:"ugb-pro-control-more-dots__dot stk-pulsating-circle"}),(0,o.createElement)("div",{className:"ugb-pro-control-more-dots__dot stk-pulsating-circle"}),(0,o.createElement)("div",{className:"ugb-pro-control-more-dots__dot stk-pulsating-circle"})),(0,o.createElement)("div",{className:r},(0,o.createElement)(It,n)))};_a.defaultProps={initialOpen:!1};const ya=e=>{var t;const n="all"===e.screens?["desktop","tablet","mobile"]:e.screens,a=(null===(t=e.units)||void 0===t?void 0:t.map((e=>({value:e}))))||[];return(0,o.createElement)("div",{className:"ugb-base-control-multi-label"},(0,o.createElement)(it,c({label:e.label},e.helpTooltip)),(0,o.createElement)(Ae,{screens:n}),(0,o.createElement)("div",{className:"ugb-base-control-multi-label__units"},(0,o.createElement)(Be,{className:"stk-control-unit-toggle",value:e.unit,options:a,onChange:t=>e.onChangeUnit(t),labelPosition:"left",buttonLabel:(0,d.__)("Unit",s.i18n),hasLabels:!1,hasColors:!1}),e.afterButton))};ya.defaultProps={label:"",units:["px"],unit:"px",onChangeUnit:()=>{},screens:["desktop"],afterButton:null};const wa=ya;class Ea extends o.Component{constructor(){super(...arguments);const e=this.getEnabledValues(),t=!e.length||e.every((t=>t===e[0])),n=e.every((e=>""===e));this.state={locked:n?this.props.defaultLocked:t},this.onToggleLock=this.onToggleLock.bind(this),this.onChangeTop=this.onChangeTop.bind(this),this.onChangeRight=this.onChangeRight.bind(this),this.onChangeBottom=this.onChangeBottom.bind(this),this.onChangeLeft=this.onChangeLeft.bind(this)}getEnabledValues(){return[...this.props.enableTop?[this.props.top]:[],...this.props.enableRight?[this.props.right]:[],...this.props.enableBottom?[this.props.bottom]:[],...this.props.enableLeft?[this.props.left]:[]]}getEnabledLocations(){return[...this.props.enableTop?["top"]:[],...this.props.enableRight?["right"]:[],...this.props.enableBottom?["bottom"]:[],...this.props.enableLeft?["left"]:[]]}filterOnlyEnabled(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,v.pick)(e,this.getEnabledLocations())}onToggleLock(){this.setState({locked:!this.state.locked})}onChangeTop(e){const t=e.target.value,n=t||0===t?t:"";this.state.locked?this.props.onChange(this.filterOnlyEnabled({top:n,right:n,bottom:n,left:n})):this.props.onChange({...this.getEnabledValues(),top:n})}onChangeRight(e){const t=e.target.value,n=t||0===t?t:"";this.state.locked?this.props.onChange(this.filterOnlyEnabled({top:n,right:n,bottom:n,left:n})):this.props.onChange({...this.getEnabledValues(),right:n})}onChangeBottom(e){const t=e.target.value,n=t||0===t?t:"";this.state.locked?this.props.onChange(this.filterOnlyEnabled({top:n,right:n,bottom:n,left:n})):this.props.onChange({...this.getEnabledValues(),bottom:n})}onChangeLeft(e){const t=e.target.value,n=t||0===t?t:"";this.state.locked?this.props.onChange(this.filterOnlyEnabled({top:n,right:n,bottom:n,left:n})):this.props.onChange({...this.getEnabledValues(),left:n})}render(){const{instanceId:e}=this.props,t=`ugb-four-number-control-${e}__item-`;return(0,o.createElement)(r.BaseControl,{help:this.props.help,className:g()("ugb-four-number-control",this.props.className)},(0,o.createElement)(wa,{label:this.props.label,units:this.props.units,unit:this.props.unit,onChangeUnit:this.props.onChangeUnit,screens:this.props.screens}),(0,o.createElement)("div",{className:"ugb-four-number-control__wrapper"},(0,o.createElement)("label",{className:"ugb-four-number-control__label",htmlFor:`${t}-top`},(0,o.createElement)("input",{id:`${t}-top`,type:"number",onChange:this.onChangeTop,"aria-label":(0,d.__)("Top",s.i18n),value:this.props.top,placeholder:this.props.enableTop?"":(0,d.__)("auto",s.i18n),disabled:!this.props.enableTop}),(0,o.createElement)("span",null,(0,d.__)("Top",s.i18n))),(0,o.createElement)("label",{className:"ugb-four-number-control__label",htmlFor:`${t}-right`},(0,o.createElement)("input",{id:`${t}-right`,type:"number",onChange:this.onChangeRight,"aria-label":this.props.label,value:this.props.right,placeholder:this.props.enableRight?"":(0,d.__)("auto",s.i18n),disabled:!this.props.enableRight}),(0,o.createElement)("span",null,(0,d.__)("Right",s.i18n))),(0,o.createElement)("label",{className:"ugb-four-number-control__label",htmlFor:`${t}-bottom`},(0,o.createElement)("input",{id:`${t}-bottom`,type:"number",onChange:this.onChangeBottom,"aria-label":this.props.label,value:this.props.bottom,placeholder:this.props.enableBottom?"":(0,d.__)("auto",s.i18n),disabled:!this.props.enableBottom}),(0,o.createElement)("span",null,(0,d.__)("Bottom",s.i18n))),(0,o.createElement)("label",{className:"ugb-four-number-control__label",htmlFor:`${t}-left`},(0,o.createElement)("input",{id:`${t}-left`,type:"number",onChange:this.onChangeLeft,"aria-label":this.props.label,value:this.props.left,placeholder:this.props.enableLeft?"":(0,d.__)("auto",s.i18n),disabled:!this.props.enableLeft}),(0,o.createElement)("span",null,(0,d.__)("Left",s.i18n))),(0,o.createElement)(M,{className:this.state.locked?"ugb--is-locked":"",onClick:this.onToggleLock,icon:(0,o.createElement)(r.Dashicon,{icon:this.state.locked?"admin-links":"editor-unlink",size:"16"})})))}}Ea.defaultProps={onChange:()=>{},defaultLocked:!0,top:"",right:"",bottom:"",left:"",units:["px"],unit:"px",onChangeUnit:()=>{},screens:["desktop"],enableTop:!0,enableRight:!0,enableBottom:!0,enableLeft:!0},(0,Ye.withInstanceId)(Ea);const Sa=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{opacity:.06,d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555D63"}),(0,o.createElement)("path",{d:"M0 6V0h6v2L5 3H3v2L2 6H0zM16 10v6h-6v-2l1-1h2v-2l1-1h2zM10 0h6v6h-2l-1-1V3h-2l-1-1V0zM6 16H0v-6h2l1 1v2h2l1 1v2z",fill:"#555D63"}))},xa=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{opacity:.06,d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555D63"}),(0,o.createElement)("path",{d:"M16 10v6h-6v-2l1-1h2v-2l1-1h2z",fill:"#555D63"}))},Ca=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{opacity:.06,d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555D63"}),(0,o.createElement)("path",{d:"M6 16H0v-6h2l1 1v2h2l1 1v2z",fill:"#555D63"}))},Ta=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{opacity:.06,d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555D63"}),(0,o.createElement)("path",{d:"M10 0h6v6h-2l-1-1V3h-2l-1-1V0z",fill:"#555D63"}))},Ma=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{opacity:.06,d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555D63"}),(0,o.createElement)("path",{d:"M0 6V0h6v2L5 3H3v2L2 6H0z",fill:"#555D63"}))},Na=function(e){return(0,o.createElement)("svg",c({id:"all_svg__Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",xmlSpace:"preserve"},e),(0,o.createElement)("style",null,".all_svg__st0{fill:#555d63}"),(0,o.createElement)("path",{className:"all_svg__st0",d:"M13 11.5v-7l2.3-1.9c.3-.2.7-.1.7.1v10.5c0 .2-.3.3-.7.2L13 11.5zM3 4.5v7L.7 13.4c-.3.2-.7.1-.7-.1V2.7c0-.2.3-.3.7-.2l2.3 2zM11.5 3h-7L2.6.7c-.2-.3-.1-.7.1-.7h10.5c.2 0 .3.3.2.7L11.5 3zM4.5 13h7l1.9 2.3c.2.2.1.7-.2.7H2.7c-.2 0-.3-.3-.2-.7l2-2.3z"}))},Ia=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{d:"M3.8 13h8.4l2.2 2.3c.2.2.1.7-.2.7H1.8c-.3 0-.4-.3-.2-.7L3.8 13z",fill:"#555d63"}))},Oa=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{d:"M3 3.8v8.4L.7 14.3c-.3.3-.7.2-.7-.1V1.9c0-.4.4-.5.7-.3L3 3.8z",fill:"#555d63"}))},Ba=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{d:"M13 12.2V3.8l2.3-2.2c.3-.2.7-.1.7.2v12.4c0 .3-.3.4-.7.2L13 12.2z",fill:"#555d63"}))},Pa=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{d:"M1.6.7c-.2-.3-.1-.7.2-.7h12.4c.3 0 .4.3.2.7L12.2 3H3.8L1.6.7z",fill:"#555d63"}))},Da=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",fill:"#555d63"}))},La=function(e){return(0,o.createElement)("svg",c({id:"vertical_svg__Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",xmlSpace:"preserve"},e),(0,o.createElement)("style",null,".vertical_svg__st1{fill:#555d63}"),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{className:"vertical_svg__st1",d:"M3.8 13h8.4l2.2 2.3c.2.2.1.7-.2.7H1.8c-.3 0-.4-.3-.2-.7L3.8 13zM1.6.7c-.2-.3-.1-.7.2-.7h12.4c.3 0 .4.3.2.7L12.2 3H3.8L1.6.7z"}))},Ra=function(e){return(0,o.createElement)("svg",c({id:"horizontal_svg__Layer_1",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16",xmlSpace:"preserve"},e),(0,o.createElement)("style",null,".horizontal_svg__st1{fill:#555d63}"),(0,o.createElement)("path",{d:"M0 0v16h16V0H0zm13 13H3V3h10v10z",opacity:.06,fill:"#555d63"}),(0,o.createElement)("path",{className:"horizontal_svg__st1",d:"M3 3.8v8.4L.7 14.3c-.3.3-.7.2-.7-.1V1.9c0-.4.4-.5.7-.3L3 3.8zM13 12.2V3.8l2.3-2.2c.3-.2.7-.1.7.2v12.4c0 .3-.3.4-.7.2L13 12.2z"}))},Aa=(0,o.memo)((e=>{var t;const[n,a]=ut(e.attribute,e.responsive,e.hover,e.valueCallback,e.changeCallback),[l,i]=vt(e),[u]=Ee();let p=n||{top:e.defaultTop,right:e.defaultRight,bottom:e.defaultBottom,left:e.defaultLeft};(void 0!==e.top||void 0!==e.right||void 0!==e.bottom||void 0!==e.left)&&(p={top:void 0!==e.top?e.top:e.enableTop?e.defaultTop:void 0,right:void 0!==e.right?e.right:e.enableRight?e.defaultRight:void 0,bottom:void 0!==e.bottom?e.bottom:e.enableBottom?e.defaultBottom:void 0,left:void 0!==e.left?e.left:e.enableLeft?e.defaultLeft:void 0});const m=void 0===e.onChange?a:e.onChange,h=e.enableTop&&""===p.top&&e.enableRight&&""===p.right&&e.enableBottom&&""===p.bottom&&e.enableLeft&&""===p.left,v=e.enableTop?p.top:e.enableRight?p.right:e.enableBottom?p.bottom:p.left,[b,f]=(0,o.useState)(h?e.defaultLocked:((e,t,n)=>{let o=!0;return o=(!e.enableTop||t.top===n)&&o,o=(!e.enableRight||t.right===n)&&o,o=(!e.enableBottom||t.bottom===n)&&o,o=(!e.enableLeft||t.left===n)&&o,o})(e,p,v)),k=g()(["ugb-four-range-control__lock"],{"ugb--is-locked":e.hasLock&&b});i.after=e.hasLock&&(0,o.createElement)(M,{className:k,onClick:()=>f(!b),variation:"tertiary",icon:b?e.isCorner?(0,o.createElement)(Sa,null):(0,o.createElement)(Na,null):(0,o.createElement)(Da,null),label:b?(0,d.__)("Individual sides",s.i18n):(0,d.__)("All sides",s.i18n)});const _=!(null===(t=e.units)||void 0===t||!t.length),y=Se(`${e.attribute}Unit`,e.responsive,e.hover),{unit:w,_valueDesktop:E,_valueTablet:S,_unitDesktop:x,_unitTablet:C}=ve((t=>({unit:t[y],_valueDesktop:t[`${e.attribute}`],_valueTablet:t[`${e.attribute}Tablet`],_unitDesktop:t[`${e.attribute}Unit`],_unitTablet:t[`${e.attribute}UnitTablet`]})));if(_){const t=e.units.indexOf(w)<0?0:e.units.indexOf(w);Array.isArray(e.min)&&(l.min=e.min[t]),Array.isArray(e.max)&&(l.max=e.max[t]),Array.isArray(e.sliderMin)&&(l.sliderMin=e.sliderMin[t]),Array.isArray(e.sliderMax)&&(l.sliderMax=e.sliderMax[t]),Array.isArray(e.step)&&(l.step=e.step[t]),Array.isArray(e.placeholder)&&(l.placeholder=e.placeholder[t]),l.initialPosition=""!==e.initialPosition?e.initialPosition:e.placeholder,0!==t&&(l.initialPosition="",l.placeholder=e.placeholder)}else l.initialPosition=""!==e.initialPosition?e.initialPosition:e.placeholder;const T=oe(),N=S&&""!==S&&S.top&&""!==S.top,I=S&&""!==S&&S.right&&""!==S.right,O=S&&""!==S&&S.bottom&&""!==S.bottom,B=S&&""!==S&&S.left&&""!==S.left,P=e.enableTop?S&&""!==S&&S.top&&""!==S.top:e.enableRight?S&&""!==S&&S.right&&""!==S.right:e.enableBottom?S&&""!==S&&S.bottom&&""!==S.bottom:S&&""!==S&&S.left&&""!==S.left,D=E&&""!==E&&E.top&&""!==E.top,L=E&&""!==E&&E.right&&""!==E.right,R=E&&""!==E&&E.bottom&&""!==E.bottom,A=E&&""!==E&&E.left&&""!==E.left,H=e.enableTop?E&&""!==E&&E.top&&""!==E.top:e.enableRight?E&&""!==E&&E.right&&""!==E.right:e.enableBottom?E&&""!==E&&E.bottom&&""!==E.bottom:E&&""!==E&&E.left&&""!==E.left,{desktop:F,tablet:V}=e.enableTop?{desktop:null==E?void 0:E.top,tablet:null==S?void 0:S.top}:e.enableRight?{desktop:null==E?void 0:E.right,tablet:null==S?void 0:S.right}:e.enableBottom?{desktop:null==E?void 0:E.bottom,tablet:null==S?void 0:S.bottom}:{desktop:null==E?void 0:E.left,tablet:null==S?void 0:S.left},j=t=>{m({top:e.enableTop?t:p.top,right:e.enableRight?t:p.right,bottom:e.enableBottom?t:p.bottom,left:e.enableLeft?t:p.left})},z=e=>{m({top:e,right:p.right,bottom:p.bottom,left:p.left})},$=e=>{m({top:p.top,right:e,bottom:p.bottom,left:p.left})},U=e=>{m({top:p.top,right:p.right,bottom:e,left:p.left})},q=e=>{m({top:p.top,right:p.right,bottom:p.bottom,left:e})},G=e=>{m({top:e,right:p.right,bottom:e,left:p.left})},W=e=>{m({top:p.top,right:e,bottom:p.bottom,left:e})};return(0,o.createElement)(gt,i,b&&!e.vhMode&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)($t,c({},l,{value:v,onChange:j,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&P?w===C?V:"":"Mobile"!==T&&"Tablet"!==T||!H?l.initialPosition:w===x?F:"",placeholder:"normal"!==u?"":"Mobile"===T&&P?w===C?V:"":"Mobile"!==T&&"Tablet"!==T||!H?l.placeholder:w===x?F:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:v,default:e.defaultTop,onChange:j})),b&&e.vhMode&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:(0,d.__)("Top and Bottom",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},(0,o.createElement)(La,null))),(0,o.createElement)($t,c({},l,{value:p.top,onChange:G,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&N?w===C?S.top:"":"Mobile"!==T&&"Tablet"!==T||!D?l.initialPosition:w===x?E.top:"",placeholder:"normal"!==u?"":"Mobile"===T&&N?w===C?S.top:"":"Mobile"!==T&&"Tablet"!==T||!D?void 0===e.placeholderTop?l.placeholder:e.placeholderTop:w===x?E.top:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.top,default:e.defaultTop,onChange:G})),(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:(0,d.__)("Left and Right",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},(0,o.createElement)(Ra,null))),(0,o.createElement)($t,c({},l,{value:p.left,onChange:W,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&B?w===C?S.left:"":"Mobile"!==T&&"Tablet"!==T||!A?l.initialPosition:w===x?E.left:"",placeholder:"normal"!==u?"":"Mobile"===T&&B?w===C?S.left:"":"Mobile"!==T&&"Tablet"!==T||!A?void 0===e.placeholderLeft?l.placeholder:e.placeholderLeft:w===x?E.left:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.left,default:e.defaultLeft,onChange:W}))),!b&&(0,o.createElement)(o.Fragment,null,e.enableTop&&(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:e.isCorner?(0,d.__)("Top Left",s.i18n):(0,d.__)("Top",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},e.isCorner?(0,o.createElement)(Ma,null):(0,o.createElement)(Pa,null))),(0,o.createElement)($t,c({},l,{value:p.top,onChange:z,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&N?w===C?S.top:"":"Mobile"!==T&&"Tablet"!==T||!D?l.initialPosition:w===x?E.top:"",placeholder:"normal"!==u?"":"Mobile"===T&&N?w===C?S.top:"":"Mobile"!==T&&"Tablet"!==T||!D?void 0===e.placeholderTop?l.placeholder:e.placeholderTop:w===x?E.top:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.top,default:e.defaultTop,onChange:z})),e.enableRight&&(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:e.isCorner?(0,d.__)("Top Right",s.i18n):(0,d.__)("Right",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},e.isCorner?(0,o.createElement)(Ta,null):(0,o.createElement)(Ba,null))),(0,o.createElement)($t,c({},l,{value:p.right,onChange:$,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&I?w===C?S.right:"":"Mobile"!==T&&"Tablet"!==T||!L?l.initialPosition:w===x?E.right:"",placeholder:"normal"!==u?"":"Mobile"===T&&I?w===C?S.right:"":"Mobile"!==T&&"Tablet"!==T||!L?void 0===e.placeholderRight?l.placeholder:e.placeholderRight:w===x?E.right:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.right,default:e.defaultRight,onChange:$})),e.enableBottom&&(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:e.isCorner?(0,d.__)("Bottom Left",s.i18n):(0,d.__)("Bottom",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},e.isCorner?(0,o.createElement)(Ca,null):(0,o.createElement)(Ia,null))),(0,o.createElement)($t,c({},l,{value:p.bottom,onChange:U,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&O?w===C?S.bottom:"":"Mobile"!==T&&"Tablet"!==T||!R?l.initialPosition:w===x?E.bottom:"",placeholder:"normal"!==u?"":"Mobile"===T&&O?w===C?S.bottom:"":"Mobile"!==T&&"Tablet"!==T||!R?void 0===e.placeholderBottom?l.placeholder:e.placeholderBottom:w===x?E.bottom:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.bottom,default:e.defaultBottom,onChange:U})),e.enableLeft&&(0,o.createElement)("div",{className:"ugb-four-range-control__range"},(0,o.createElement)(r.Tooltip,{text:e.isCorner?(0,d.__)("Bottom Right",s.i18n):(0,d.__)("Left",s.i18n)},(0,o.createElement)("span",{className:"ugb-four-range-control__icon"},e.isCorner?(0,o.createElement)(xa,null):(0,o.createElement)(Oa,null))),(0,o.createElement)($t,c({},l,{value:p.left,onChange:q,allowReset:!1,initialPosition:"normal"!==u?"":"Mobile"===T&&B?w===C?S.left:"":"Mobile"!==T&&"Tablet"!==T||!A?l.initialPosition:w===x?E.left:"",placeholder:"normal"!==u?"":"Mobile"===T&&B?w===C?S.left:"":"Mobile"!==T&&"Tablet"!==T||!A?void 0===e.placeholderLeft?l.placeholder:e.placeholderLeft:w===x?E.left:""})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:p.left,default:e.defaultLeft,onChange:q}))))}),v.isEqual);Aa.defaultProps={defaultLocked:!0,hasLock:!0,enableTop:!0,enableRight:!0,enableBottom:!0,enableLeft:!0,defaultTop:"",defaultRight:"",defaultBottom:"",defaultLeft:"",placeholder:"",placeholderTop:"",placeholderRight:"",placeholderBottom:"",placeholderLeft:"",initialPosition:"",vhMode:!1,allowReset:!0,default:"",attribute:"",responsive:!1,hover:!1,top:void 0,right:void 0,bottom:void 0,left:void 0,onChange:void 0,isCorner:!1};const Ha=(0,o.memo)(Aa),Fa=e=>{const t=oe(),n=o.Children.toArray(e.children).map((n=>(0,o.cloneElement)(n,{screens:e.screens,screen:t.toLowerCase()}))),a=t?(null==t?void 0:t.toLowerCase())===e.screen:"desktop"===e.screen.toLowerCase(),l=!e.screens.includes(t.toLowerCase())&&!a&&"Desktop"===t;return(0,o.createElement)(o.Fragment,null,(a||l)&&n)};Fa.defaultProps={screen:"desktop",screens:["desktop","tablet","mobile"]};const Va=Fa,ja=[{value:"h1",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),1),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M8.5 13H5.8V8.9H2.7V13H0V3h2.7v3.7h3.1V3h2.7v10zM16 13h-2.7V7.7 5.9c-.2.2-.4.4-.6.7l-1.1.9-1.4-1.7L13.6 3H16v10z"}))}),null)},{value:"h2",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),2),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M7.7 12.7h-2v-4H2v4H0V3.4h2v3.7h3.7V3.4h2v9.3zM16 12.7H9.4v-1.4L11.8 9c.7-.7 1.1-1.2 1.4-1.5.2-.3.4-.5.5-.8.1-.2.1-.5.1-.7 0-.4-.1-.7-.3-.8-.2-.2-.5-.3-.8-.3-.4 0-.7.1-1 .3-.3.2-.7.4-1.1.7L9.4 4.6c.5-.4.8-.7 1.1-.8.3-.2.6-.3 1-.4.4-.1.8-.1 1.2-.1.6 0 1.1.1 1.6.3.5.2.8.5 1 .9s.4.8.4 1.3c0 .4-.1.8-.2 1.2-.2.4-.4.8-.7 1.2s-.9 1-1.7 1.7L11.9 11v.1H16v1.6z"}))}),null)},{value:"h3",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),3),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M7.7 12.6H6.2V8.4H2v4.2H.5V3.3H2v3.8h4.2V3.3h1.5v9.3zM15.2 5.5c0 .6-.2 1.1-.5 1.5-.3.4-.8.7-1.4.8v.1c.7.1 1.3.3 1.6.7.4.4.6.9.6 1.5 0 .9-.3 1.6-.9 2.1-.6.5-1.5.7-2.6.7-1 0-1.9-.2-2.5-.5v-1.3c.4.2.8.3 1.2.5.4.1.8.2 1.2.2.7 0 1.2-.1 1.6-.4.4-.3.5-.7.5-1.2s-.2-.9-.6-1.1-1-.3-1.8-.3h-.8V7.3h.8c1.4 0 2.2-.5 2.2-1.5 0-.4-.1-.7-.4-.9-.3-.2-.6-.3-1.1-.3-.4 0-.7 0-1 .1-.3.1-.7.3-1.1.6l-.7-1c.8-.6 1.8-.9 2.9-.9.9 0 1.6.2 2.1.6.4.2.7.8.7 1.5z"}))}),null)},{value:"h4",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),4),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M7.5 12.5H6v-4H1.9v4H.5v-9h1.4v3.7H6V3.5h1.4l.1 9zM15.5 10.5h-1.2v2h-1.4v-2h-4V9.4l4-5.9h1.4v5.8h1.2v1.2zm-2.6-1.2V7.1c0-.8 0-1.5.1-2-.1.3-.3.6-.5 1l-2.2 3.2h2.6z"}))}),null)},{value:"h5",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),5),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M7.7 12.1H6.3V8.4H2.4v3.8H1V3.8h1.4v3.4h3.9V3.8h1.4v8.3zM12.2 6.9c.9 0 1.5.2 2 .7.5.4.8 1 .8 1.8 0 .9-.3 1.6-.9 2.1-.6.5-1.4.7-2.4.7-.9 0-1.7-.1-2.2-.4v-1.2c.3.2.7.3 1.1.4.4.1.8.1 1.1.1.6 0 1.1-.1 1.4-.4.3-.3.5-.7.5-1.2 0-1-.6-1.5-1.9-1.5H11c-.3 0-.5.1-.7.1l-.7-.3.4-4h4.4V5h-3.2L11 7.1c.1 0 .3 0 .5-.1.2 0 .4-.1.7-.1z"}))}),null)},{value:"h6",title:(0,d.sprintf)((0,d._x)("%s %d","Nth Title",s.i18n),(0,d.__)("Heading",s.i18n),6),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M7.6 12.1H6.3V8.4H2.6v3.7H1.2V3.8h1.3v3.4h3.8V3.8h1.3v8.3zM9.2 8.6c0-3.2 1.3-4.8 3.9-4.8.4 0 .8 0 1 .1V5c-.3-.1-.6-.1-1-.1-.9 0-1.5.2-2 .7-.4.5-.7 1.2-.7 2.3h.1c.2-.3.4-.5.7-.7.3-.2.7-.2 1.1-.2.7 0 1.3.2 1.7.7s.6 1.1.6 1.9c0 .9-.2 1.5-.7 2.1-.5.5-1.1.8-2 .8-.6 0-1.1-.1-1.5-.4-.4-.3-.8-.7-1-1.3 0-.8-.2-1.5-.2-2.2zm2.9 2.5c.4 0 .8-.2 1-.4.2-.3.4-.7.4-1.3 0-.5-.1-.9-.4-1.1-.2-.3-.6-.4-1-.4-.3 0-.6.1-.8.2-.2.1-.4.3-.6.5-.1.2-.2.4-.2.6 0 .5.2 1 .4 1.3.4.4.7.6 1.2.6z"}))}),null)},{value:"p",title:(0,d.__)("Paragraph",s.i18n),icon:(0,o.createElement)((function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 16"},e),(0,o.createElement)("path",{d:"M4.5 3h3.1c2.2 0 3.9.8 3.9 3.1 0 2.2-1.6 3.2-3.8 3.2H6.3V13H4.5V3zm3.1 4.9c1.5 0 2.2-.6 2.2-1.8 0-1.3-.8-1.7-2.3-1.7H6.3v3.5h1.3z"}))}),null)}],za=ja.filter((e=>{let{value:t}=e;return"p"!==t}));(0,o.memo)((e=>(0,o.createElement)(fn,c({},e,{className:"ugb-heading-buttons-control",controls:e.hasP?ja:za,placeholder:ja[1].value,default:ja[1].value})))).defaultProps={label:(0,d.sprintf)((0,d._x)("%s HTML Tag","component",s.i18n),(0,d.__)("Title",s.i18n)),value:void 0,hasP:!0};const $a=[{value:"left",title:(0,d.__)("Align Left",s.i18n),icon:"editor-alignleft"},{value:"center",title:(0,d.__)("Align Center",s.i18n),icon:"editor-aligncenter"},{value:"right",title:(0,d.__)("Align Right",s.i18n),icon:"editor-alignright"},{value:"justify",title:(0,d.__)("Justified",s.i18n),icon:"editor-justify"}];$a.filter((e=>"justify"!==e.value));(0,d.__)("Align",s.i18n);const Ua=wp.codeEditor,qa=e=>{const[t,n]=(0,o.useState)(e.value),[a,l]=(0,o.useState)(null),r=(0,o.useRef)(),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0px";const[n,a]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{const n=new IntersectionObserver((t=>{let[o]=t;o.isIntersecting&&(a(o.isIntersecting),n.unobserve(e.current))}),{rootMargin:t});return e.current&&n.observe(e.current),()=>{e.current&&n.unobserve(e.current)}})),n}(r),s=(0,o.useMemo)((()=>(0,v.uniqueId)("ugb-code-textarea-")),[]),c=(0,o.useRef)((0,v.debounce)((t=>{e.onChange(t)}),500,{leading:!0})),u=(0,o.useCallback)((e=>{const t=e.getValue?e.getValue():e;n(t),c.current(t)}),[]);return(0,o.useEffect)((()=>{if(Ua.initialize&&i){const t=(0,Ua.initialize)(s,Y()(Ua.defaultSettings,e.editorSettings));l(t),t.codemirror.on("change",u)}return()=>{var e;null==c||null===(e=c.current)||void 0===e||e.cancel(),null==a||a.codemirror.off("change",u)}}),[i]),(0,o.createElement)("textarea",{ref:r,className:"ugb-code-textarea",id:s,value:t,onChange:u})};qa.defaultProps={value:"",onChange:()=>{},editorSettings:{}};const Ga=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M18.2 0H1.8C.8 0 0 .8 0 1.8v16.5c0 1 .8 1.8 1.8 1.8h16.3c1 0 1.8-.8 1.8-1.8V1.8c.1-1-.7-1.8-1.7-1.8zM4.5 10.6h1.8l-1.8 1.8v-1.8zm3.1-1.2H4.5V7.7h4.8L7.6 9.4zm2.9-2.9h-6V5H12l-1.5 1.5zM3.3 15H1.5V5h1.7v10zm1.2-.8L13.7 5h1.8v.8L6.3 15H4.5v-.8zm11-4.8h-1.8l1.8-1.8v1.8zm-3.1 1.2h3.1v1.7h-4.8l1.7-1.7zm-2.9 2.9h6V15H8l1.5-1.5zM16.8 5h1.7v10h-1.7V5zM1.5 1.8c0-.2.1-.3.3-.3h16.3c.2 0 .3.1.3.3v1.3c0 .2-.1.3-.3.3H1.8c-.2 0-.3-.1-.3-.3V1.8zm17 16.4c0 .2-.1.3-.3.3H1.8c-.2 0-.3-.1-.3-.3v-1.3c0-.2.1-.3.3-.3h16.3c.2 0 .3.1.3.3v1.3zm-14.9-.7c0 .3-.2.5-.5.5s-.5-.2-.5-.5.3-.5.5-.5.5.2.5.5zm13.8 0c0 .3-.2.5-.5.5s-.5-.2-.5-.5.2-.5.5-.5.5.2.5.5zM2.6 2.5c0-.3.3-.5.5-.5s.5.2.5.5-.2.5-.5.5-.5-.2-.5-.5zm13.8 0c0-.3.2-.5.5-.5s.5.2.5.5-.2.5-.5.5-.5-.2-.5-.5z"}))},Wa=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},e),(0,o.createElement)("path",{d:"M10.9 20h-9c-1 0-1.8-.8-1.8-1.8v-5.1c0-1 .8-1.8 1.8-1.8h9c1 0 1.8.8 1.8 1.8v5.1c0 1-.8 1.8-1.8 1.8zm-9.1-7.4c-.3 0-.5.2-.5.5v5.1c0 .3.2.5.5.5h9c.3 0 .5-.2.5-.5v-5.1c0-.3-.2-.5-.5-.5h-9zM18.2 8.7h-9c-1 0-1.8-.8-1.8-1.8V1.8C7.4.8 8.2 0 9.2 0h9c1 0 1.8.8 1.8 1.8v5.1c0 1-.8 1.8-1.8 1.8zM9.1 1.3c-.3 0-.5.2-.5.5v5.1c0 .3.2.5.5.5h9c.3 0 .5-.2.5-.5V1.8c0-.3-.2-.5-.5-.5h-9z"}))};function Ka(e){return(0,o.cloneElement)(e,{fill:"url(#stackable-gradient)",className:`stk-stackable-icon-gradient ${e.props.className||""}`})}l((()=>{if(document.querySelector("svg.ugb-stackable-gradient"))return;const e=document.createElement("DIV");e.setAttribute("style","height: 0; width: 0; overflow: hidden;"),e.setAttribute("aria-hidden","true"),document.querySelector("body").appendChild(e),i(e).render((0,o.createElement)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"ugb-stackable-gradient",height:"0",width:"0",style:{opacity:0}},(0,o.createElement)("defs",null,(0,o.createElement)("linearGradient",{id:"stackable-gradient"},(0,o.createElement)("stop",{offset:"0%",stopColor:"#8c33da",stopOpacity:"1"}),(0,o.createElement)("stop",{offset:"100%",stopColor:"#f34957",stopOpacity:"1"})))))}));const Ja=(0,o.createElement)(r.Icon,{icon:function(){return Ka((0,o.createElement)(Ga,{width:"20",height:"20",className:"components-menu-items__item-icon"}))}}),Ya=(0,o.createElement)(r.Icon,{icon:function(){return Ka((0,o.createElement)(Wa,{width:"20",height:"20",className:"components-menu-items__item-icon"}))}}),Xa=(0,Ye.compose)([(0,ne.withSelect)(((e,t)=>{let{clientIds:n}=t;const{getBlockRootClientId:o,getBlocksByClientId:a,canInsertBlockType:l}=e("core/block-editor"),r="ugb/container",i=l(r,n&&n.length>0?o(n[0]):void 0),s=a(n),c=1===s.length&&s[0]&&s[0].name===r;return{isGroupable:i&&s.length&&!c,isUngroupable:c&&!!s[0].innerBlocks.length,blocksSelection:s,groupingBlockName:r}})),(0,ne.withDispatch)(((e,t)=>{let{clientIds:n,onToggle:o=v.noop,blocksSelection:a=[]}=t;const{replaceBlocks:l}=e("core/block-editor");return{onConvertToGroup(){if(!a.length)return;const e=a.map((e=>(0,_e.cloneBlock)(e))),t=(0,_e.createBlock)("ugb/container",{},e);l(n,t),o()},onConvertFromGroup(){if(!a.length)return;const e=a[0].innerBlocks;e.length&&(l(n,e),o())}}}))])((function(e){let{onConvertToGroup:t,onConvertFromGroup:n,isGroupable:a=!1,isUngroupable:l=!1}=e;return(0,ne.select)("core/block-editor").getSelectedBlockClientIds?(0,o.createElement)(o.Fragment,null,a&&(0,o.createElement)(ke.BlockSettingsMenuControls,null,(e=>{let{onClose:n}=e;return(0,o.createElement)(r.MenuItem,{icon:Ja,onClick:()=>{t(),n()}},(0,d.__)("Group into Container",s.i18n))})),l&&(0,o.createElement)(ke.BlockSettingsMenuControls,null,(e=>{let{onClose:t}=e;return(0,o.createElement)(r.MenuItem,{icon:Ya,onClick:()=>{n(),t()}},(0,d.__)("Ungroup from Container",s.i18n))}))):null})),Qa=((0,ne.withSelect)((e=>{const{getSelectedBlockClientIds:t}=e("core/block-editor");return t?{clientIds:t()}:{}}))(Xa),e=>{let{clientId:t}=e;const[n,a]=(0,o.useState)(!1),[l,i]=(0,o.useState)(!1),{blockName:s,getJSONBlockAttributes:c}=(0,ne.useSelect)((e=>{const{getBlockName:n}=e("core/block-editor");return{blockName:t&&n(t)?n(t).replace(/^\w+\//g,""):"",getJSONBlockAttributes:()=>{const{getBlockName:n}=e("core/block-editor"),{getBlockType:o}=e("core/blocks");if(!t)return{};const a=e("core/block-editor").getBlockAttributes(t),l=n(t).replace(/^\w+\//g,""),r=o(n(t)).attributes,i=Object.keys(a).reduce(((e,t)=>(a[t]!==(r[t]?r[t].default:"")&&(e[t]=a[t]),e)),{});return i.uniqueClass=void 0,JSON.stringify((0,m.applyFilters)(`stackable.${l}.design.filtered-block-attributes`,i),null,4)}}}));return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(ke.BlockSettingsMenuControls,null,(e=>{let{onClose:t}=e;return(0,o.createElement)(r.MenuItem,{icon:"editor-code",onClick:()=>{a(!0),i(!1),setTimeout((()=>document.querySelector(".ugb-modal-get-block-attributes-button textarea").select()),100),t()}},"Get / Set Block Attributes")})),n&&(0,o.createElement)(r.Modal,{title:`${(0,v.startCase)(s)} Block Attributes`,focusOnMount:!0,className:"ugb-modal-get-block-attributes-button",onRequestClose:()=>{l&&(e=>{if(t)try{const n=JSON.parse(e);hs(n,t)}catch(e){console.error(e)}})(document.querySelector(".ugb-modal-get-block-attributes-button textarea").value),a(!1)}},(0,o.createElement)("p",null,'Copy or modify the attributes of the block directly. Use only double quotes "'),(0,o.createElement)("textarea",{onChange:()=>i(!0)},c())))});(0,ne.withSelect)((e=>{const{getSelectedBlockClientId:t}=e("core/block-editor");return t?{clientId:t()}:{}}))(Qa);const Za=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50 50"},e),(0,o.createElement)("path",{d:"M38 12H12v26h26V12z"}))},el=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50 50"},e),(0,o.createElement)("path",{d:"M12.5 12.5H0V0h12.5v12.5zM31.2 0H18.8v12.5h12.5V0zM50 0H37.5v12.5H50V0zM12.5 18.8H0v12.5h12.5V18.8zm18.7 0H18.8v12.5h12.5V18.8zm18.8 0H37.5v12.5H50V18.8zM12.5 37.5H0V50h12.5V37.5zm18.7 0H18.8V50h12.5V37.5zm18.8 0H37.5V50H50V37.5z"}))},tl=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 50 50"},e),(0,o.createElement)("path",{d:"M21.1 5.3H5.3v15.8h15.8V5.3zm23.6 0H28.9v15.8h15.8V5.3zM21.1 28.9H5.3v15.8h15.8V28.9zm23.6 0H28.9v15.8h15.8V28.9z"}))},nl=e=>"wireframes"===e.uikit.toLowerCase(),ol=e=>{const[t,n]=(0,o.useState)([]),[a,l]=(0,o.useState)([]),[r,i]=(0,o.useState)([]),[c,u]=ts("stk__design_library__block-list__selected",""),{viewBy:p,apiVersion:m}=e;(0,o.useEffect)((()=>{Go(m).then((e=>{const t=Object.keys(e).reduce(((t,n)=>{const o=e[n],{categories:a,uikit:l}=o;return void 0!==t.uikits[l]||nl(o)||(t.uikits[l]={id:l,label:o.uikit,plan:o.plan,count:0}),nl(o)?a.forEach((e=>{void 0===t.wireframes[e]&&(t.wireframes[e]={id:e,label:e,count:0})})):a.forEach((e=>{void 0===t.categories[e]&&(t.categories[e]={id:e,label:e,count:0})})),t}),{uikits:{},categories:{},wireframes:{}});let o=["label"];s.isPro||(o=["plan","label"]);const a=(0,v.sortBy)(Object.values(t.uikits),o),r=(0,v.sortBy)(Object.values(t.categories),"label");r.unshift({id:"all",label:(0,d.__)("All",s.i18n),count:0});const c=(0,v.sortBy)(Object.values(t.wireframes),"label");c.unshift({id:"all",label:(0,d.__)("All",s.i18n),count:0}),n(a),l(r),i(c)}))}),[m]),(0,o.useEffect)((()=>{if(!t.length||!a.length||!r.length)return;const o=t.reduce(((e,t)=>(e[t.id]={...t,count:0},e)),{}),c=a.reduce(((e,t)=>(e[t.id]={...t,count:0},e)),{}),u=r.reduce(((e,t)=>(e[t.id]={...t,count:0},e)),{});e.designs.forEach((e=>{nl(e)?e.categories.forEach((e=>{e&&u[e]&&u[e].count++})):(e.uikit&&o[e.uikit]&&o[e.uikit].count++,e.categories.forEach((e=>{e&&c[e]&&c[e].count++})))}));let p=["label"];s.isPro||(p=["plan","label"]),n((0,v.sortBy)(Object.values(o),p)),c.all&&(c.all.count=e.designs.filter((e=>!nl(e))).length,c.all.label="    ");const m=(0,v.sortBy)(Object.values(c),"label");m[0]&&(m[0].label=(0,d.__)("All",s.i18n)),l(m),u.all&&(u.all.count=e.designs.filter(nl).length,u.all.label="    ");const h=(0,v.sortBy)(Object.values(u),"label");h[0]&&(h[0].label=(0,d.__)("All",s.i18n)),i(h)}),[e.designs.length,JSON.stringify(t),JSON.stringify(a),JSON.stringify(r)]),(0,o.useEffect)((()=>{t.length&&a.length&&r.length&&u("uikit"===p?t[0].id:"all")}),[p]),(0,o.useEffect)((()=>{e.onSelect(c)}),[c]);const h="uikit"===p?t:"category"===p?a:r;return(0,o.createElement)("ul",{className:"ugb-block-list"},h.reduce(((e,t)=>{const{id:n,label:a,count:l,plan:r}=t,i=g()(["stk-design-library__sidebar-item"],{"is-active":c===n,"is-disabled":!s.isPro&&"premium"===r});return e.push((0,o.createElement)("li",{key:n},(0,o.createElement)("div",{className:i,"data-count":l,onClick:()=>u(n),onKeyPress:e=>{13===e.keyCode&&(void 0).click()},role:"button",tabIndex:0,"aria-pressed":c===n?"true":"false"},a,(0,o.createElement)("span",{className:"ugb-block-list__count","data-testid":`${n}-count`},l)))),e}),[]))};ol.defaultProps={select:"",onSelect:()=>{},apiVersion:"",designs:[]};const al=ol,ll=e=>{const[t,n]=(0,o.useState)(e.search),[a,l]=(0,o.useState)(3),[i,c]=(0,o.useState)(!0),[u,p]=(0,o.useState)(!1),[m,h]=(0,o.useState)(!1),[v,b]=(0,o.useState)([]),[f,k]=(0,o.useState)([]),[_,y]=(0,o.useState)(!1),[w,E]=(0,o.useState)(""),[S,x]=ts("stk__design_library__block-list__view_by","uikit"),[C,T]=ts("stk__design_library_dev_mode",!1),[N,I]=(0,o.useState)([]),[O,B]=(0,o.useState)([]),[P,D]=(0,o.useState)(t),[L,R]=(0,o.useState)(null);return(0,o.useEffect)((()=>{L&&(clearTimeout(L),R(null)),R(setTimeout((()=>{D(t)}),500))}),[t]),(0,o.useEffect)((()=>{const e=document.querySelector(".ugb-modal-design-library__search input");e&&e.focus()}),[]),(0,o.useEffect)((()=>{u&&(I([]),B([])),Ko({search:P,reset:u,apiVersion:e.apiVersion}).then((e=>{I(e)})).finally((()=>{p(!1)}))}),[P,u,e.apiVersion]),(0,o.useEffect)((()=>{c(!0),Ko({apiVersion:e.apiVersion,search:P,uikit:"wireframe"===S?"Wireframes":"uikit"===S?w:"",categories:["category","wireframe"].includes(S)&&"all"!==w?[w]:[]}).then((e=>{B(e)})).finally((()=>{c(!1)}))}),[w,S,u,P,e.apiVersion]),(0,o.createElement)(r.Modal,{title:(0,o.createElement)(o.Fragment,null,(0,d.__)("Stackable Design Library",s.i18n),(0,o.createElement)(fn,{className:"stk-design-library-tabs",controls:[{value:"category",title:(0,d.__)("Block Designs",s.i18n)},{value:"uikit",title:(0,d.__)("UI Kits",s.i18n)},{value:"wireframe",title:(0,d.__)("Wireframes",s.i18n)}],value:S,onChange:x,fullwidth:!1,isToggleOnly:!0,allowReset:!1}),e.hasVersionSwitcher&&(0,o.createElement)(fn,{controls:[{value:"",title:(0,d.__)("Latest Design Library",s.i18n)},{value:"v2",title:(0,d.__)("V2 Design Library",s.i18n)}],value:e.apiVersion,onChange:e.onChangeApiVersion,isSmall:!0,fullwidth:!1,isToggleOnly:!0,allowReset:!1})),className:g()("ugb-modal-design-library",{"ugb-modal-design-library--is-multiselect":m}),onRequestClose:e.onClose},(0,o.createElement)("div",{className:"ugb-modal-design-library__wrapper"},(0,o.createElement)("aside",{className:"ugb-modal-design-library__sidebar"},(0,o.createElement)(r.TextControl,{className:"ugb-modal-design-library__search",placeholder:(0,d.__)("E.g. light, dark, red, minimalist…",s.i18n),value:t,onChange:e=>n(e),"data-testid":"input-search",type:"search"}),(0,o.createElement)("div",{className:"ugb-modal-design-library__filters"},(0,o.createElement)(al,{apiVersion:e.apiVersion,designs:N,viewBy:S,onSelect:e=>E(e)}))),(0,o.createElement)("aside",{className:"ugb-modal-design-library__topbar"},(0,o.createElement)(M,{label:(0,d.__)("Select Multiple",s.i18n),className:g()("ugb-modal-design-library__select","stk-circular-button",{"stk--is-active":m}),onClick:()=>h(!m)},(0,d.__)("Select",s.i18n)),m&&(0,o.createElement)(M,{label:(0,d.__)("Deselect All",s.i18n),className:"ugb-modal-design-library__deselect stk-circular-button",disabled:!v.length,onClick:()=>b([])},(0,d.__)("Deselect All",s.i18n)),s.devMode&&(0,o.createElement)(r.ToggleControl,{className:"ugb-modal-design-library__dev-mode",label:"Dev Mode",checked:C,onChange:e=>{(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=await Me()({path:"/stackable/v2/design_library_dev_mode/",method:"POST",data:{devmode:e}});return await t})(e).then((()=>{p(!0)})),T(e)}}),(0,o.createElement)(M,{icon:"image-rotate",label:(0,d.__)("Refresh Library",s.i18n),className:"ugb-modal-design-library__refresh",onClick:()=>p(!0)}),(0,o.createElement)(M,{icon:(0,o.createElement)(Za,{width:"18",height:"18"}),className:2===a?"is-active":"",label:(0,d.__)("Large preview",s.i18n),onClick:()=>l(2)}),(0,o.createElement)(M,{icon:(0,o.createElement)(tl,{width:"18",height:"18"}),className:3===a?"is-active":"",label:(0,d.__)("Medium preview",s.i18n),onClick:()=>l(3)}),(0,o.createElement)(M,{icon:(0,o.createElement)(el,{width:"18",height:"18"}),className:4===a?"is-active":"",label:(0,d.__)("Small preview",s.i18n),onClick:()=>l(4)})),(0,o.createElement)("div",{className:"ugb-modal-design-library__designs"},(0,o.createElement)(Zo,{className:`stk-design-library__item-${S}`,columns:a,onSelect:e.onSelect,isBusy:i,designs:O,apiVersion:e.apiVersion,isMultiSelectMode:m,selectedDesigns:v,onSelectMulti:t=>{const n=[...v],o=[...f];if(n.includes(t)){const e=n.indexOf(t);n.splice(e,1),b(n),o.splice(e,1),k(o)}else n.push(t),b(n),o.push(O.find((e=>e.id===t))),k(o),Jo(t,e.apiVersion)}})),m&&(0,o.createElement)("aside",{className:"ugb-modal-design-library__footer"},(0,o.createElement)("div",null,(0,d.sprintf)((0,d.__)("(%d) Selected",s.i18n),v.length)),(0,o.createElement)(M,{label:(0,d.__)("Add Designs",s.i18n),className:"ugb-modal-design-library__add-multi",disabled:!v.length||_,onClick:()=>{y(!0);const t=v.map((t=>Jo(t,e.apiVersion)));Promise.all(t).then((t=>{setTimeout((()=>{e.onSelect(t,f,(()=>y(!1)))}))}))}},(0,d.__)("Add Designs",s.i18n),_&&(0,o.createElement)(r.Spinner,null)))))};ll.defaultProps={search:"",onClose:()=>{},onSelect:()=>{},hasVersionSwitcher:!1,apiVersion:"",onChangeApiVersion:()=>{}};let rl=[];class il extends o.Component{constructor(){super(...arguments),this.state={isLoading:0===rl.length,termList:rl}}componentWillMount(){this.state.termList.length||(this.isStillMounted=!0,this.fetchRequest=Me()({path:(0,Ce.addQueryArgs)(`/stackable/${"v"+(this.props.stkVersion||"2")}/terms`,{per_page:-1})}).then((e=>{this.isStillMounted&&(rl=e,this.setState({termList:e,isLoading:!1}))})).catch((()=>{this.isStillMounted&&this.setState({termList:[],isLoading:!1})})))}componentWillUnmount(){this.isStillMounted=!1}render(){const e=[],t=[],n=[];let a="";const{taxonomy:l,allowReset:i}=this.props;Object.keys(this.state.termList).forEach((o=>{const{label:a,taxonomies:l}=this.state.termList[o];"wp_block"!==o&&(e.push({label:a,value:o}),o===this.props.postType&&Object.keys(l).forEach(((e,o)=>{const{label:a,terms:r}=l[e];t.push({label:a,value:e});const i=!this.props.taxonomyType&&0===o;(e===this.props.taxonomyType||i)&&Object.keys(r).forEach((e=>{n.push({name:r[e].name,value:r[e].term_id})}))})))}));const c=n.map((e=>e.name));let u=""!==l?l.split(",").map((e=>{const{name:t}=(0,v.find)(n,(t=>t.value===parseInt(e)))||{};return t})).filter((e=>e)):void 0;if((0,v.compact)(u).length||(u=void 0),t.length){const e=this.props.taxonomyType||t[0].value,n=t.filter((t=>{let{value:n}=t;return e===n}));a=n.length?n[0].label:t[0].label}return this.state.isLoading?(0,o.createElement)("div",{className:"stk-taxonomy-control__spinner"},(0,o.createElement)(r.Spinner,null)):(0,o.createElement)("div",{className:"stk-taxonomy-control"},s.isPro&&(0,o.createElement)(Wt,{label:(0,d.__)("Post Type",s.i18n),options:e,value:this.props.postType,allowReset:i,onChange:e=>{const t=Object.keys(this.state.termList[e].taxonomies);this.props.onChangePostType(e),this.props.onChangeTaxonomyType(t.length?t[0]:""),this.props.onChangeTaxonomy("")},default:"post"}),t.length>0&&(0,o.createElement)(Wt,{label:(0,d.__)("Filter by Taxonomy",s.i18n),options:t,value:this.props.taxonomyType,allowReset:i,onChange:e=>{this.props.onChangeTaxonomyType(e),this.props.onChangeTaxonomy("")},default:"category"}),t.length>0&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Wt,{label:(0,d.__)("Taxonomy Filter Type",s.i18n),allowReset:i,options:[{label:(0,d.__)("Included In",s.i18n),value:"__in"},{label:(0,d.__)("Not In",s.i18n),value:"__not_in"}],value:this.props.taxonomyFilterType,onChange:this.props.onChangeTaxonomyFilterType,default:"__in"}),(0,o.createElement)(Tl,{label:a,suggestions:c,value:u,onChange:e=>{var t;const o=null==e||null===(t=e.map)||void 0===t?void 0:t.call(e,(e=>{const{value:t}=(0,v.find)(n||[],(t=>t.name===e))||{};return t}));this.props.onChangeTaxonomy((0,v.compact)(o||[]).join(","))}})))}}il.defaultProps={postType:"post",onChangePostType:()=>{},taxonomyType:"category",onChangeTaxonomyType:()=>{},taxonomy:"",onChangeTaxonomy:()=>{},allowReset:!1};const sl={Edit:e=>{const{blockProps:t,render:n,mainClass:a,blockTag:l,...r}=e,{blockName:i}=t,{anchor:s="",design:u,blockTag:d="div"}=t.attributes,p=g()([e.className],(0,m.applyFilters)(`stackable.${i}.main-block.classes`,{"ugb-main-block":a},t)),h=g()(["ugb-inner-block"],(0,m.applyFilters)(`stackable.${i}.main-block.inner-classes`,{},t)),v=(0,m.applyFilters)(`stackable.${i}.main-block.extraProps`,r,t),b=""!==s?s:void 0,f=d||l||"div";return(0,o.createElement)(f,c({},v,{id:b,className:p}),t.styles,(0,m.applyFilters)(`stackable.${i}.edit.output.outer`,null,u,t),n&&(0,o.createElement)("div",{className:h},(0,m.applyFilters)(`stackable.${i}.edit.output.before`,null,u,t),(0,o.createElement)("div",{className:"ugb-block-content"},n(t)),(0,m.applyFilters)(`stackable.${i}.edit.output.after`,null,u,t)))}};sl.Edit.defaultProps={styles:null,mainClass:!0},sl.Save=e=>{const{blockProps:t,render:n,mainClass:a,blockTag:l,...r}=e,{blockName:i}=t,{anchor:s="",design:u,blockTag:d="div"}=t.attributes,p=g()([e.className],(0,m.applyFilters)(`stackable.${i}.main-block.classes`,{"ugb-main-block":a},t)),h=g()(["ugb-inner-block"],(0,m.applyFilters)(`stackable.${i}.main-block.inner-classes`,{},t)),v=(0,m.applyFilters)(`stackable.${i}.main-block.extraProps`,r,t),b=""!==s?s:void 0,f=d||l||"div";return(0,o.createElement)(f,c({},v,{id:b,className:p}),t.styles,(0,m.applyFilters)(`stackable.${i}.save.output.outer`,null,u,t),n&&(0,o.createElement)("div",{className:h},(0,m.applyFilters)(`stackable.${i}.save.output.before`,null,u,t),(0,o.createElement)("div",{className:"ugb-block-content"},n(t)),(0,m.applyFilters)(`stackable.${i}.save.output.after`,null,u,t)))},sl.Save.defaultProps={styles:null,mainClass:!0};const cl=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-10 210h1620V105.2H-10z"}))},ul=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#straight-1-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"straight-1-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M-10 121.6h1620v-16.4H-10z"}))},dl={"wave-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-1_svg__st2",d:"M1341.4 48.9c-182.4 0-254.2 80.4-429.4 80.4-117.8 0-209.7-67.5-393.5-67.5-142.2 0-212.6 38.8-324.6 38.8S-10 64.7-10 64.7V210h1620V102c-110.6-40.2-181-53.1-268.6-53.1z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-1-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-1-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"wave-1-shadow_svg__st2",d:"M1341.4 48.9c-182.4 0-254.2 80.4-429.4 80.4-117.8 0-209.7-67.5-393.5-67.5-142.2 0-212.6 38.8-324.6 38.8S-10 64.7-10 64.7v10s91.9 35.9 203.9 35.9 182.4-38.8 324.6-38.8c183.8 0 275.7 67.5 393.5 67.5 175.2 0 247-80.4 429.4-80.4 87.6 0 158 12.9 268.6 53.1v-10c-110.6-40.2-181-53.1-268.6-53.1z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1341.4 151.4C1159 151.4 1087.2 71 912 71c-117.8 0-209.7 67.5-393.5 67.5-142.2 0-212.6-38.8-324.6-38.8S-10 135.6-10 135.6v75.9h1620V98.3c-110.6 40.2-181 53.1-268.6 53.1z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-1-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-1-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1341.4 151.4C1159 151.4 1087.2 71 912 71c-117.8 0-209.7 67.5-393.5 67.5-142.2 0-212.6-38.8-324.6-38.8S-10 135.6-10 135.6v10s91.9-35.9 203.9-35.9 182.4 38.8 324.6 38.8C702.3 148.5 794.2 81 912 81c175.2 0 247 80.4 429.4 80.4 87.6 0 158-12.9 268.6-53.1v-10c-110.6 40.2-181 53.1-268.6 53.1z"}))}}},"wave-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-2_svg__st2",d:"M1432.2 67.4c-88.8-16.7-156-5.3-204 8.5s-147.1 62.2-223.1 73.9c-75.4 11.6-164-7.5-275-27.9S571 88.1 456 98.1c-119.7 10.4-224.7 52-294.4 73-94.5 28.5-171.6-3-171.6-3V210h1620V91.6c-53 10-86.8-7.1-177.8-24.2z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-2-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-2-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"wave-2-shadow_svg__st2",d:"M1432.2 67.4c-88.8-16.7-156-5.3-204 8.5s-147.1 62.2-223.1 73.9c-75.4 11.6-164-7.5-275-27.9S571 88.1 456 98.1c-119.7 10.4-224.7 52-294.4 73-94.5 28.5-171.6-3-171.6-3v10s77.1 31.5 171.6 3c69.7-21 174.7-62.6 294.4-73 115-10 163.1 3.4 274.1 23.8 111 20.4 199.6 39.5 275 27.9 76-11.7 175.1-60.1 223.1-73.9s115.2-25.2 204-8.5c91 17.1 124.8 34.2 177.8 24.2v-10c-53 10-86.8-7.1-177.8-24.2z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1432.2 156.2c-88.8 16.7-156 5.3-204-8.5s-147.1-62.2-223.1-73.9c-75.4-11.6-164 7.5-275 27.9S571 135.5 456 125.5c-119.7-10.4-224.7-52-294.4-73C67.1 24-10 55.5-10 55.5v155.2h1620V132c-53-10-86.8 7.1-177.8 24.2z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-2-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-2-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1432.2 156.2c-88.8 16.7-156 5.3-204-8.5s-147.1-62.2-223.1-73.9c-75.4-11.6-164 7.5-275 27.9S571 135.5 456 125.5c-119.7-10.4-224.7-52-294.4-73C67.1 24-10 55.5-10 55.5v10s77.1-31.5 171.6-3c69.7 21 174.7 62.6 294.4 73 115 10 163.1-3.4 274.1-23.8 111-20.4 199.6-39.5 275-27.9 76 11.7 175.1 60.1 223.1 73.9s115.2 25.2 204 8.5c91-17.1 124.8-34.2 177.8-24.2v-10c-53-10-86.8 7.1-177.8 24.2z"}))}}},"wave-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-3_svg__st2",d:"M1413.6 161.4c-157.9 0-338.2-37.7-495.1-67.4-215.6-40.8-328.1-44.6-418.2-41.1S317 73.4 188.4 102-10 136.2-10 136.2v74.2h1620v-68.5s-68.8 19.5-196.4 19.5z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-3-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-3-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"wave-3-shadow_svg__st2",d:"M1413.6 161.4c-157.9 0-338.2-37.7-495.1-67.4-215.6-40.8-328.1-44.6-418.2-41.1S317 73.4 188.5 102-10 136.2-10 136.2v10s69.9-5.7 198.5-34.3 221.7-45.7 311.8-49.1 202.6.3 418.2 41.1c156.9 29.7 337.2 67.4 495.1 67.4 127.6 0 196.4-19.4 196.4-19.4v-10s-68.8 19.5-196.4 19.5z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1413.6 39.3c-157.9 0-338.2 37.7-495.1 67.4-215.6 40.8-328.1 44.6-418.2 41.1S317 127.3 188.4 98.7-10 64.5-10 64.5v150.2h1620V58.8s-68.8-19.5-196.4-19.5z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-3-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-3-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1413.6 39.3c-157.9 0-338.2 37.7-495.1 67.4-215.6 40.8-328.1 44.6-418.2 41.1S317 127.3 188.4 98.7C59.8 70.1-10 64.5-10 64.5v10s69.8 5.6 198.4 34.2c128.6 28.6 221.8 45.6 311.9 49.1s202.6-.3 418.2-41.1c156.9-29.7 337.2-67.4 495.1-67.4 127.6 0 196.4 19.5 196.4 19.5v-10s-68.8-19.5-196.4-19.5z"}))}}},"wave-4":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 209.7v-51.2s-81.4-33.3-241.4-23.1c-157.4 10-247.9 49.6-340.9 49.4-93.1-.1-121-14.6-174.2-33.6-75.7-27.1-166.9-27.2-185.5-25.1-18.7 2.1-67.8 5.5-106-19.3-38.2-24.7-94.8-55.4-209.2-43.1C238.1 76.3 80 111.8-10 55.7v154h1620z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-4-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-4-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1368.6 135.4c-157.4 10-247.9 49.6-340.9 49.4-93.1-.1-121-14.6-174.2-33.6-75.7-27.1-166.9-27.2-185.5-25.1-18.7 2.1-67.8 5.5-106-19.3-38.2-24.7-94.8-55.4-209.2-43.1C238.1 76.3 80 111.8-10 55.7v10c90 56.1 248.1 20.6 362.8 8 114.4-12.3 171 18.4 209.2 43.1 38.2 24.8 87.3 21.4 106 19.3 18.6-2.1 109.8-2 185.5 25.1 53.2 19 81.1 33.5 174.2 33.6 93 .2 183.5-39.4 340.9-49.4 160-10.2 241.4 23.1 241.4 23.1v-10s-81.4-33.3-241.4-23.1z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 208.9V79.2s-81.4 33.3-241.4 23.1c-157.4-10-247.9-49.6-340.9-49.4-93.1.1-121 14.6-174.2 33.6-75.7 27.1-166.9 27.2-185.5 25.1-18.7-2.1-67.8-5.5-106 19.3-38.2 24.7-94.8 55.4-209.2 43.1C238.1 161.4 80 125.9-10 182v26.9h1620z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#wave-4-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"wave-4-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1368.6 102.3c-157.4-10-247.9-49.6-340.9-49.4-93.1.1-121 14.6-174.2 33.6-75.7 27.1-166.9 27.2-185.5 25.1-18.7-2.1-67.8-5.5-106 19.3-38.2 24.7-94.8 55.4-209.2 43.1C238.1 161.4 80 125.9-10 182v10c90-56.1 248.1-20.6 362.8-8 114.4 12.3 171-18.4 209.2-43.1 38.2-24.8 87.3-21.4 106-19.3 18.6 2.1 109.8 2 185.5-25.1 53.2-19 81.1-33.5 174.2-33.6 93-.2 183.5 39.4 340.9 49.4 160 10.2 241.4-23.1 241.4-23.1v-10s-81.4 33.3-241.4 23.1z"}))}}},"curve-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-1_svg__st2",d:"M1610 177.3C1423 122.9 1133.3 88 808 88c-334.7 0-631.8 37-818 94.1v28h1620v-32.8z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-1-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-1-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"curve-1-shadow_svg__st2",d:"M808 87.9c-334.7 0-631.8 37-818 94.1v10c186.2-57.1 483.3-94.1 818-94.1 325.3 0 615 34.9 802 89.3v-10c-187-54.3-476.7-89.3-802-89.3z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 78.3c-187 54.4-476.7 89.3-802 89.3-334.7 0-631.8-37-818-94.1v136.8h1620v-132z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-1-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-1-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M808 167.6c-334.7 0-631.8-37-818-94.1v10c186.2 57.1 483.3 94.1 818 94.1 325.3 0 615-34.9 802-89.3v-10c-187 54.4-476.7 89.3-802 89.3z"}))}}},"curve-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-2_svg__st2",d:"M-10 207.6h1620S1430.8 23.8 1138.3 23.8C884 23.8 234.9 140.1-10 197.9v9.7z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-2-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-2-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"curve-2-shadow_svg__st2",d:"M1138.3 36.2c246.1 0 412 130.1 458.4 171.4h13.3S1430.8 23.8 1138.3 23.8C884 23.8 172 156.9-10 197.9v9.7H2.2C201.7 163.7 889.3 36.2 1138.3 36.2z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-10 207.4h1620S1608.1.1 1604.2 3.7c-32.8 30.8-203.9 178.2-465.9 178.2C884 181.9 234.9 65.6-10 7.8v199.6z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-2-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-2-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1604.2 13.7c3.3-3.1 5.2 152.3 5.7 193.7h.1S1608.1.1 1604.2 3.7c-32.8 30.8-203.9 178.2-465.9 178.2C884 181.9 234.9 65.6-10 7.8v10c244.9 57.8 894 174.1 1148.3 174.1 262 0 433.1-147.5 465.9-178.2z"}))}}},"curve-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-3_svg__st1",d:"M-6.7 13.4S456 171.1 876.1 171.1 1606.4 16 1606.4 16v192.6H-6.7V13.4z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-3-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-3-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"curve-3-shadow_svg__st1",d:"M876.1 171.1C456 171.1-6.7 13.4-6.7 13.4v13.7S456 184.8 876.1 184.8s730.3-155.1 730.3-155.1V16s-310.3 155.1-730.3 155.1z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-6.6 177.6S456.1 40.3 876.2 40.3s730.3 135.1 730.3 135.1v33.5H-6.6v-31.3z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#curve-3-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"curve-3-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M876.2 40.3C456.1 40.3-6.5 177.6-6.5 177.6v10S456.2 50.3 876.3 50.3s730.3 135.1 730.3 135.1v-10c0-.1-310.3-135.1-730.4-135.1z"}))}}},"slant-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"slant-1_svg__st2",d:"M-10 210h1620V.5L-10 183.9z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#slant-1-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"slant-1-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"slant-1-shadow_svg__st2",d:"M-10 183.9v10L1610 10.5V.5z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 210H-10V.5l1620 183.4z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#slant-1-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"slant-1-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M-10 .5v10l1620 183.4v-10z"}))}}},"slant-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"slant-2_svg__st1",d:"M1610 39.2V209H-10V39.2l810 118.9 810-118.9z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#slant-2-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"slant-2-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"slant-2-shadow_svg__st1",d:"M1610 39.2v12L800 170.1-10 51.2v-12l810 118.9 810-118.9z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 160v53.2H-10V160L800 41.1 1610 160z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#slant-2-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"slant-2-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M800 41.1L-10 160v10L800 51.1 1610 170v-10z"}))}}},"straight-1":{default:{shape:cl,shadow:ul},inverted:{shape:cl,shadow:ul}},"rounded-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"rounded-1_svg__st1",d:"M1602.9 209l-9.3-117.7c-3.8-48.1-46.3-84.4-94.4-80.6L-8 131.2V209h1610.9z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-1-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-1-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"rounded-1-shadow_svg__st1",d:"M1491.2 20.7c48.1-3.8 90.6 32.5 94.4 80.6l8.5 107.7h8.8l-9.3-117.7c-3.8-48.1-46.3-84.4-94.4-80.6L-8 131.2v9.4L1491.2 20.7z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1604.5-2.7l-10.9 104.5c-3.8 48.1-46.3 84.4-94.4 80.6L-8 61.8v148.5h1613.9l-1.4-213z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-1-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-1-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1596.6 111.7l8.2-78-.2-36.4-10.9 104.5c-3.8 48.1-46.3 84.4-94.4 80.6L-8 61.8v10l1507.2 120.5c48.1 3.8 93.6-32.5 97.4-80.6z"}))}}},"rounded-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"rounded-2_svg__st2",d:"M1609.5 133.2h-9.5c0-10.9-8.9-19.8-19.8-19.8H19.8C8.9 113.3 0 122.2 0 133.2l-10-.2v77h1619.5v-76.8z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-2-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-2-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{className:"rounded-2-shadow_svg__st2",d:"M1600 133.2c0-10.9-8.9-19.8-19.8-19.8H19.8C8.9 113.3 0 122.2 0 133.2V210h9v-66.8c0-10.9 8.9-19.8 19.8-19.8h1542.4c10.9 0 19.8 8.9 19.8 19.8V210h9v-76.8z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1609.5 128.5h-9.5c0 10.9-8.9 19.8-19.8 19.8H19.8C8.9 148.4 0 139.5 0 128.5l-10 .2v83.1h1619.5v-83.3z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-2-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-2-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1600 128.5c0 10.9-8.9 19.8-19.8 19.8H19.8C8.9 148.4 0 139.5 0 128.5l-10 .2v10l10-.2c0 11 8.9 19.9 19.8 19.8h1560.4c10.9 0 19.8-8.9 19.8-19.8h9.5v-10h-9.5z"}))}}},"rounded-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610.5 189.1s-128.2-82.6-138.2-89.2c-17.1-11.3-37.2-15.5-87-18.1-49.7-2.6-753.6-40.7-783.9-42.3-30.3-1.5-81.1-1.3-121.1 8.2S-9.5 163.2-9.5 163.2v50h1620v-24.1z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-3-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-3-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1472.3 99.9c-17.1-11.3-37.2-15.5-87-18.1-49.7-2.6-753.6-40.7-783.9-42.3-30.3-1.5-81.1-1.3-121.1 8.2S-9.5 163.2-9.5 163.2v10s449.8-106 489.8-115.5 90.8-9.7 121.1-8.2c30.3 1.6 734.2 39.7 783.9 42.3 49.8 2.6 69.9 6.8 87 18.1 10 6.6 138.2 89.2 138.2 89.2v-10s-128.2-82.6-138.2-89.2z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610.5 24.1s-128.2 82.6-138.2 89.2c-17.1 11.3-37.2 15.5-87 18.1-49.7 2.6-753.6 40.7-783.9 42.3-30.3 1.5-81.1 1.3-121.1-8.2S-9.5 50-9.5 50v158.9h1620V24.1z"}))},shadow:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200",filter:"url(#rounded-3-inverted-shadow_svg__a)",enableBackground:"new 0 0 1600 200"},e),(0,o.createElement)("filter",{id:"rounded-3-inverted-shadow_svg__a"},(0,o.createElement)("feGaussianBlur",{in:"SourceAlpha",stdDeviation:4}),(0,o.createElement)("feComponentTransfer",null,(0,o.createElement)("feFuncA",{type:"linear",slope:.4})),(0,o.createElement)("feMerge",null,(0,o.createElement)("feMergeNode",null),(0,o.createElement)("feMergeNode",{in:"SourceGraphic"}))),(0,o.createElement)("path",{d:"M1472.3 113.3c-17.1 11.3-37.2 15.5-87 18.1-49.7 2.6-753.6 40.7-783.9 42.3-30.3 1.5-81.1 1.3-121.1-8.2S-9.5 50-9.5 50v10s449.8 106 489.8 115.5 90.8 9.7 121.1 8.2c30.3-1.6 734.2-39.7 783.9-42.3 49.8-2.6 69.9-6.8 87-18.1 10-6.6 138.2-89.2 138.2-89.2v-10s-128.2 82.6-138.2 89.2z"}))}}}},pl=e=>{const{shape:t,shadow:n}=dl[e.design||"wave-1"][e.inverted?"inverted":"default"],a=g()([e.className,"ugb-separator-wrapper"]);return(0,o.createElement)("div",{className:a},e.shadow&&(0,o.createElement)(n,{className:"ugb-separator__shadow",preserveAspectRatio:"none","aria-hidden":"true"}),(0,o.createElement)(t,{className:"ugb-separator__layer-1",preserveAspectRatio:"none","aria-hidden":"true"}),e.children)};pl.defaultProps={className:"",design:"wave-1",shadow:!1,inverted:!1},pl.Save=e=>(0,o.createElement)(pl,e);const ml=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n="";return n=t?hl(e,{"aria-label":t,role:"img"},["aria-hidden"]):hl(e,{"aria-hidden":"true"},["aria-label","role"]),n},hl=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];const o=ns(e);return o?(Object.keys(t).forEach((e=>{o.setAttribute(e,t[e])})),n.forEach((e=>{o.removeAttribute(e)})),o.outerHTML):""},gl=(0,o.memo)((e=>{const{svgAttrsToAdd:t={width:"32",height:"32"},svgAttrsToRemove:n=["id","data-name"]}=e,[a,l]=(0,o.useState)(0),i=()=>{l(a+1)},s=(0,v.pick)(e,["className","color","fill","style"]);if("string"==typeof e.value&&e.value.match(/^<svg/)){let a=ml(e.value,e.ariaLabel);return a=hl(a,t,n),(0,o.createElement)(o.RawHTML,s,e.prependRenderString+a)}const c=e.value?e.value.replace(/-.*$/,""):e.prefix,u=e.value?e.value.replace(/^.*?-/,""):e.iconName;if(c&&u){const a=ls(c,u);if(!a)return rs(c,u).then(i),(0,o.createElement)(r.Spinner,null);let l=ml(a,e.ariaLabel);return l=hl(l,t,n),(0,o.createElement)(o.RawHTML,s,e.prependRenderString+l)}const d=ls("far","smile");if(!d)return rs("far","smile").then(i),(0,o.createElement)(r.Spinner,null);let p=ml(d,e.ariaLabel);return p=hl(p,t,n),(0,o.createElement)(o.RawHTML,s,e.prependRenderString+p)}));gl.Content=e=>{const t=(0,v.pick)(e,["className","color","fill","style"]),{prependRenderString:n=""}=e;if("string"==typeof e.value&&e.value.match(/^<svg/)){let a=ml(e.value,e.ariaLabel);return a=hl(a,{width:"32",height:"32"}),(0,o.createElement)(o.RawHTML,t,n+a)}const a=e.value?e.value.replace(/-.*$/,""):e.prefix,l=e.value?e.value.replace(/^.*?-/,""):e.iconName,r=ls(a,l);let i=ml(r,e.ariaLabel);return i=hl(i,{width:"32",height:"32"}),(0,o.createElement)(o.RawHTML,t,n+i)},gl.defaultProps={ariaLabel:"",prefix:"",iconName:"",value:"",prependRenderString:""};const vl=gl,bl=e=>e.match(/^<svg(.*?)<\/svg>$/g)?e:e.match(/<svg/)?(e.match(/<svg.*?<\/svg>/g)||[e])[0]:e,fl=e=>{const{...t}=e;return t.value=(0,o.useMemo)((()=>"string"===e.value?bl(e.value):e.value),[e.value]),(0,o.createElement)(vl,t)};fl.Content=e=>{const{...t}=e;return t.value="string"===e.value?bl(e.value):e.value,(0,o.createElement)(vl.Content,t)};const kl=e=>{const t=g()(["stk-inspector-control",e.className],{"stk-inspector-control--allow-reset":e.allowReset,"stk--is-small":e.isSmall}),n=null!==e.showReset?e.showReset:void 0!==e.value&&e.value!==e.defaultValue&&e.value!==e.placeholder;return(0,o.createElement)(r.BaseControl,{help:e.help,className:t},e.hasLabel&&(0,o.createElement)(wa,{label:e.label,units:e.units,unit:e.unit,onChangeUnit:e.onChangeUnit,screens:e.screens,afterButton:e.afterButton,helpTooltip:e.helpTooltip}),e.children,e.allowReset&&n&&(0,o.createElement)(M,{className:g()("stk-inspector-control__reset-button",{"stk-control__reset-button--no-modified":!e.hasPanelModifiedIndicator}),isSmall:!0,isTertiary:!0,"aria-label":(0,d.__)("Reset",s.i18n),onClick:()=>{e.onReset?e.onReset():e.onChange(e.defaultValue)},icon:(0,o.createElement)(r.Dashicon,{icon:"image-rotate"})}))};kl.defaultProps={className:"",help:"",id:"",screens:["desktop"],units:null,unit:"px",onChangeUnit:()=>{},value:"",onChange:()=>{},allowReset:!1,showReset:null,defaultValue:"",onReset:null,isLinked:!0,onLink:()=>{},afterButton:null,isSmall:!1,hasLabel:!0,hasPanelModifiedIndicator:!0};const _l=kl,yl=function(e){return(0,o.createElement)("svg",c({fill:"none",xmlns:"http://www.w3.org/2000/svg"},e),(0,o.createElement)("path",{d:"M16.667 12.222v2.963a1.482 1.482 0 01-1.482 1.482H4.815a1.482 1.482 0 01-1.482-1.482v-2.963M13.704 7.037L10 3.333 6.296 7.037M10 3.333v8.89",stroke:"#293056",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}))};(0,o.memo)((e=>{const t=Se(`${e.attribute}Id`,e.responsive,e.hover),n=Se(`${e.attribute}Url`,e.responsive,e.hover),a=Se(`${e.attribute}HeightAttribute`,e.responsive,e.hover),l=Se(`${e.attribute}WidthAttribute`,e.responsive,e.hover),r=Se(`${e.attribute}Alt`,e.responsive,e.hover),i=ve((e=>({[t]:e[t],[n]:e[n]}))),u=be(),p=void 0!==e.onChange?e.onChange:e=>{u({[t]:e.id,[n]:e.url,[a]:e.width||"",[l]:e.height||"",[r]:e.alt||""})},[m,h]=vt(e),v=Ot({onChange:e=>p({url:e,id:"",width:"",height:"",alt:""}),value:i[n]}),b=void 0!==e.imageId?e.imageId:i[t],f=void 0!==e.imageURL?e.imageURL:v.value||i[n],k=f&&f.match(/(mp4|webm|ogg)$/i)?"video":"image";return(0,o.createElement)(gt,c({},h,{valueCheckAttribute:e.attribute+"Url",className:g()("ugb-image-control",e.className)}),f&&(0,o.createElement)(ke.MediaUpload,{onSelect:p,allowedTypes:e.allowedTypes,value:b,render:e=>(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"ugb-image-preview-wrapper"},"video"===k&&(0,o.createElement)("video",{className:"ugb-image-preview",autoPlay:!0,muted:!0,loop:!0,src:f,onClick:e.open,onKeyDown:t=>{13===t.keyCode&&e.open()}}),"image"===k&&(0,o.createElement)("img",{className:"ugb-image-preview",draggable:"false",src:f,onClick:e.open,onKeyDown:t=>{13===t.keyCode&&e.open()},alt:(0,d.__)("preview",s.i18n)})))}),(0,o.createElement)(At,c({enable:e.isDynamic,hasPanelModifiedIndicator:e.hasPanelModifiedIndicator,type:"image-url"},v),(0,o.createElement)(ke.MediaUpload,{onSelect:p,allowedTypes:e.allowedTypes,value:b,render:e=>(0,o.createElement)(o.Fragment,null,(0,o.createElement)(M,{className:"ugb-image-upload",onClick:e.open,icon:(0,o.createElement)(yl,{viewBox:"0 0 20 20"}),isSecondary:!0,onKeyDown:t=>{13===t.keyCode&&e.open()}},(0,o.createElement)("span",{className:"ugb-image-upload__label"},f?(0,d.__)("Replace",s.i18n):(0,d.__)("Upload",s.i18n)," ")))})),(0,o.createElement)(ft,{allowReset:e.allowReset&&!e.dynamic,value:f,default:e.default,onChange:()=>{p({url:"",id:"",height:"",width:"",alt:""})},hasPanelModifiedIndicator:e.hasPanelModifiedIndicator}))})).defaultProps={label:"",attribute:"",allowedTypes:["image"],responsive:!1,hover:!1,isDynamic:!0,value:void 0,onChange:void 0,allowReset:!0,hasPanelModifiedIndicator:!0};const wl=(0,o.memo)((e=>{const{help:t}=e,n=`inspector-toggle-control-${(0,Ye.useInstanceId)(wl)}`,[a,l]=ut(e.attribute,e.responsive,e.hover,e.changeCallback),i=void 0===e.checked?a:e.checked,s=void 0===e.onChange?l:e.onChange;let u,d;t&&(u=n+"__help",d=(0,v.isFunction)(t)?t(i):t);const p=g()(["components-toggle-control","stk-toggle-control","stk-control",e.className]);return(0,o.createElement)(_l,{id:n,help:d,className:p,allowReset:!0,value:i,showReset:e.defaultValue?i!==e.defaultValue:i,onChange:s,hasLabel:!1,defaultValue:e.defaultValue},(0,o.createElement)(r.FormToggle,{id:n,checked:i,onChange:e=>s(e.target.checked),"aria-describedby":u}),(0,o.createElement)("label",{htmlFor:n,className:"components-toggle-control__label"},(0,o.createElement)(it,c({label:e.label},e.helpTooltip))))}));wl.defaultProps={className:"",allowReset:!1,showReset:null,defaultValue:"",attribute:"",responsive:!1,hover:!1,checked:void 0,onChange:void 0};const El=wl,Sl={x:.5,y:.5},xl=e=>{var t;const[n,a]=ut(e.attribute,e.responsive,e.hover),[l,i]=vt(e);l.url=Bt(l.url),null!==(t=l.url)&&void 0!==t&&t.includes("http")||(l.url=void 0);const s=void 0===e.value?n:e.value;return(0,o.createElement)(gt,c({},i,{className:g()("stk-advanced-focal-point-control",e.className)}),(0,o.createElement)(r.FocalPointPicker,c({},l,{value:s||Sl,onChange:void 0===e.onChange?a:e.onChange})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:s,default:e.default,onChange:void 0===e.onChange?a:e.onChange}))};xl.defaultProps={className:"",url:"",allowReset:!0,default:"",attribute:"",responsive:!1,hover:!1,value:void 0,onChange:void 0},(0,o.memo)(xl);const Cl=e=>{const[t,n]=ut(e.attribute,e.responsive,e.hover),[a,l]=vt(e),{...i}=a,s=void 0===e.value?t:e.value,u=void 0===e.onChange?n:e.onChange;return(0,o.createElement)(gt,c({},l,{className:g()("stk-advanced-token-field",e.className)}),(0,o.createElement)(r.FormTokenField,c({},i,{value:s||[],onChange:u})),(0,o.createElement)(ft,{allowReset:e.allowReset,value:s,default:e.default,onChange:u}))};Cl.defaultProps={className:"",allowReset:!0,default:"",attribute:"",value:void 0,onChange:void 0};const Tl=Cl;(0,d.__)("Blur",s.i18n),(0,d.__)("Brightness",s.i18n),(0,d.__)("Contrast",s.i18n),(0,d.__)("Grayscale",s.i18n),(0,d.__)("Hue Rotate",s.i18n),(0,d.__)("Invert",s.i18n),(0,d.__)("Opacity",s.i18n),(0,d.__)("Saturate",s.i18n),(0,d.__)("Sepia",s.i18n),(0,o.createElement)(Ie,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(Ne,{d:"M18 11.2h-5.2V6h-1.6v5.2H6v1.6h5.2V18h1.6v-5.2H18z"}));(0,d.__)("Add Column",s.i18n);const Ml=[{component:El,key:"inset",props:{label:(0,d.__)("Inset",s.i18n)},default:!1},{component:qt,key:"horizontalOffset",props:{label:(0,d.__)("Horizontal Offset",s.i18n),placeholder:0,sliderMin:-100,sliderMax:100},format:"%spx",default:"0px"},{component:qt,key:"verticalOffset",props:{label:(0,d.__)("Vertical Offset",s.i18n),placeholder:0,sliderMin:-100,sliderMax:100},format:"%spx",default:"0px"},{component:qt,key:"blur",props:{label:(0,d.__)("Blur",s.i18n),placeholder:0,sliderMin:0,sliderMax:100},format:"%spx",default:"0px"},{component:qt,key:"shadowSpread",props:{label:(0,d.__)("Shadow Spread",s.i18n),placeholder:0,sliderMin:0,sliderMax:100},format:"%spx",default:"0px",show:e=>!e.isFilter},{component:Et,key:"shadowColor",props:{label:(0,d.__)("Shadow Color",s.i18n)},default:"#000000"}],Nl=e=>{const[t,n]=(0,o.useState)({}),[a,l]=(0,o.useState)({}),[r,i]=ut(e.attribute,e.responsive,e.hover),[u,p]=vt(e),m=void 0===e.value?r:e.value,h=void 0===e.onChange?i:e.onChange,g=(e,t,n,o)=>{if(e){let a=e.trim();a.startsWith("inset")?(t.inset=!0,a=a.replace(/^inset\s*/,"")):t.inset=!1;const[l,r,i,s,c]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;const n=e.split(" "),o=[];for(let e=0;e<t-1;e++)n.length?o.push(n.shift()):o.push("");return o.push(n.join(" ")),o}(a,o?4:5);t.horizontalOffset=isNaN(parseInt(l))?0:parseInt(l),t.verticalOffset=isNaN(parseInt(r))?0:parseInt(r),t.blur=isNaN(parseInt(i))?0:parseInt(i),t.shadowSpread=isNaN(parseInt(s))?0:parseInt(s),t.shadowColor=c||"",o&&(t.shadowSpread="",t.shadowColor=s),n({...t})}};return(0,o.useEffect)((()=>{g(m,t,n,e.isFilter)}),[m,e.isFilter]),(0,o.useEffect)((()=>{g(e.placeholder,a,l,e.isFilter)}),[e.placeholder,e.isFilter]),(0,o.createElement)(nt,{placement:"top-start",className:"shadow-control__popover",anchorRect:e.anchorRect,onEscape:e.onEscape},(0,o.createElement)("div",{className:"components-panel__body is-opened"},(0,o.createElement)(gt,c({},p,{label:(0,d.__)("Advanced Shadow Options",s.i18n),boldLabel:!0}),Ml.map((l=>{if(!e.hasInset&&"inset"===l.key)return null;const r={...l.props},i=l.component;return l.show&&!l.show(e.parentProps)?null:("inset"===l.key&&(r.checked=!!t[l.key]),"shadowColor"===l.key&&(r.default=a[l.key]||"",r.value=t[l.key]||a[l.key]||""),(0,o.createElement)(i,c({key:l.key,allowReset:!0,value:t[l.key]||""},r,{placeholder:a[l.key]||"",onChange:o=>{const a=(l.changeCallback||(e=>e))(o);t[l.key]=a,n({...t}),h(((e,t)=>(0,v.compact)(Ml.map((n=>{const{key:o}=n;return"inset"===o?t[o]?"inset":"":!n.show||n.show(e)?n.format&&void 0!==t[o]&&""!==t[o]?(0,d.sprintf)(n.format,t[o]):t[o]||n.default||"":void 0}))).join(" "))(e.parentProps,t))}})))})))))};Nl.defaultProps={hasInset:!0,isFilter:!1};(0,o.memo)((e=>{var t;const{options:n,label:a,...l}=e,i=n||(0,m.applyFilters)("stackable.shadows",["none","0 0 0 1px rgba(120, 120, 120, 0.1)","0 0 0 2px rgba(120, 120, 120, 0.1)","0 5px 5px 0 rgba(18, 63, 82, 0.035)","0px 2px 20px rgba(153, 153, 153, 0.2)","0 5px 30px -10px rgba(18, 63, 82, 0.3)","0px 10px 30px rgba(0, 0, 0, 0.05)","7px 5px 30px rgba(72, 73, 121, 0.15)","0px 10px 60px rgba(0, 0, 0, 0.1)","0px 70px 90px -20px rgba(72, 73, 121, 0.30)"]),u=(0,o.useRef)(null),[p,h]=(0,o.useState)(!1),g=e=>e?-1===i.indexOf(e)?"custom":i.indexOf(e):"",[v,b]=ut(e.attribute,e.responsive,e.hover,g,(e=>""!==e?i[e]:e)),f=void 0===e.value?v:e.value,[k,_]=vt(l);return(0,o.useEffect)((()=>{const e=e=>{p&&(e.target.closest(".shadow-control__popover")||e.target.closest(".stk-shadow-control__more-button")||e.target.closest(".components-color-picker")||e.target.closest(".react-autosuggest__suggestions-container")||e.target.closest(".components-dropdown__content")||h(!1))};return document.body.addEventListener("mousedown",e),()=>document.body.removeEventListener("mousedown",e)}),[p]),(0,o.useEffect)((()=>{}),[f,p]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(qt,c({},k,_,{attribute:e.attribute,label:a,value:f,onChange:void 0===e.onChange?b:e.onChange,min:0,max:i.length-1,allowReset:!0,helpTooltip:e.helpTooltip,hover:e.hover,placeholder:"custom"===f?(0,d.__)("Custom",s.i18n):g(e.placeholder),after:(0,o.createElement)(r.Button,{className:"stk-shadow-control__more-button",ref:u,isSmall:!0,isTertiary:!0,isPressed:p||"custom"===f,label:(0,d.__)("Shadow Settings",s.i18n),onClick:()=>h(!p),icon:(0,o.createElement)(r.Dashicon,{icon:"admin-generic"})})})),p&&(0,o.createElement)(Nl,c({},_,{anchorRect:null===(t=u.current)||void 0===t?void 0:t.getBoundingClientRect(),attribute:e.attribute,responsive:e.responsive,placeholder:e.placeholder,hover:e.hover,parentProps:e,hasInset:e.hasInset,isFilter:e.isFilter,onEscape:()=>h(!1),value:e.shadowFilterValue,onChange:e.shadowFilterOnChange})))}),v.isEqual).defaultProps={attribute:"",label:(0,d.__)("Shadow / Outline",s.i18n),placeholder:"",options:null,valueCallback:null,changeCallback:null,isFilter:!1,hasInset:!0,helpTooltip:{video:"general-shadow",title:(0,d.__)("Shadow/Outline",s.i18n),description:(0,d.__)("Adjusts the intensity of the shadow/outline of the block and the appearance of the block border",s.i18n)}};const Il=wp.keycodes,Ol=(0,o.memo)((e=>{const{option:t,isActive:n,onSelect:a}=e,l=e=>{t.disabled||n||a(e)},r=t.icon;return(0,o.createElement)("div",{className:g()("stk-style-control__option","stk-block-editor-block-styles__item",{"is-active":n,"is-disabled":t.disabled}),onClick:()=>l(t),onKeyDown:e=>{Il.ENTER!==e.keyCode&&Il.SPACE!==e.keyCode||(e.preventDefault(),l(t))},role:"button",tabIndex:"0","aria-label":t.label||""},(0,o.createElement)("div",{className:"stk-block-editor-block-styles__item-preview stk-style-control__image"},t.image||r&&(0,o.createElement)(r,{className:"stk-style-control__image__icon"})),t.label&&(0,o.createElement)("div",{className:"stk-block-editor-block-styles__item-label"},t.label))}));Ol.defaultProps={option:{},isActive:!1,onSelect:()=>{}};const Bl=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return Object.keys(e).reduce(((o,a)=>({...o,[ds(a,t,t,n?".editor-styles-wrapper":"")]:e[a]})),{})},Pl=e=>Object.keys(e).reduce(((t,n)=>{const o=Object.keys(e[n]).reduce(((t,o)=>{const a=e[n][o];if(void 0===a)return t;const l=o.replace(/^(--?)?(.*?$)/,((e,t,n)=>`${t||""}${(0,v.kebabCase)(n)}`));return t+"\n\t"+l+": "+a+";"}),"");return t.push("\n\n"+n+" {"+o+"\n}"),t}),[]),Dl=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["desktop"],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1024,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:768;const o=(0,v.sortBy)("string"==typeof e?e.split(",").map((e=>e.trim())):e);return(0,v.isEqual)(o,["desktop","tablet"])?"@media screen and (min-width: "+n+"px)":(0,v.isEqual)(o,["desktop"])?"@media screen and (min-width: "+t+"px)":(0,v.isEqual)(o,["mobile","tablet"])?"@media screen and (max-width: "+(t-1)+"px)":(0,v.isEqual)(o,["tablet"])?"@media screen and (min-width: "+n+"px) and (max-width: "+(t-1)+"px)":(0,v.isEqual)(o,["mobile"])?"@media screen and (max-width: "+(n-1)+"px)":null},Ll=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1024,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:768,a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;const r=[];Object.keys(e).forEach((t=>{if(["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","ie11","editor","saveOnly","custom"].includes(t)){const n=t;Object.keys(e[n]).forEach((t=>{Object.keys(e[n][t]).forEach((o=>void 0===e[n][t][o]?delete e[n][t][o]:{})),Object.keys(e[n][t]).length||delete e[n][t]})),Object.keys(e[n]).length||delete e[n]}else Object.keys(e[t]).forEach((n=>void 0===e[t][n]?delete e[t][n]:{})),Object.keys(e[t]).length||delete e[t]}));const i=(0,v.omit)(e,["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","ie11","editor","saveOnly","custom"]);if(Object.keys(i).length){const e=Bl(i,t,a);Array.prototype.push.apply(r,Pl(e))}if(void 0!==e.desktopTablet){const l=Bl(e.desktopTablet,t,a),i=Pl(l);i&&(a?i.forEach((e=>{r.push("\n\n"+Dl(["desktop","tablet"],n,o)+" {\n"+e+" }")})):r.push(`\n\n${Dl(["desktop","tablet"],n,o)} {\n${i.join("")} }`))}if(void 0!==e.desktopOnly){const l=Bl(e.desktopOnly,t,a),i=Pl(l);i&&(a?i.forEach((e=>{r.push("\n\n"+Dl(["desktop"],n,o)+" {\n"+e+" }")})):r.push(`\n\n${Dl(["desktop"],n,o)} {\n${i.join("")} }`))}if(void 0!==e.tablet){const l=Bl(e.tablet,t,a),i=Pl(l);i&&(a?i.forEach((e=>{r.push("\n\n"+Dl(["mobile","tablet"],n,o)+" {\n"+e+" }")})):r.push(`\n\n${Dl(["mobile","tablet"],n,o)} {\n${i.join("")} }`))}if(void 0!==e.tabletOnly){const l=Bl(e.tabletOnly,t,a),i=Pl(l);i&&(a?i.forEach((e=>{r.push("\n\n"+Dl(["tablet"],n,o)+" {\n"+e+" }")})):r.push(`\n\n${Dl(["tablet"],n,o)} {\n${i.join("")} }`))}if(void 0!==e.mobile){const l=Bl(e.mobile,t,a),i=Pl(l);i&&(a?i.forEach((e=>{r.push("\n\n"+Dl(["mobile"],n,o)+" {\n"+e+" }")})):r.push(`\n\n${Dl(["mobile"],n,o)} {\n${i.join("")} }`))}if(void 0!==e.ie11){const n=Bl(e.ie11,t,a),o=Pl(n);o&&r.push(`\n\n@media screen and (-ms-high-contrast: active), screen and (-ms-high-contrast: none) {\n${o.join("")} }`)}return void 0!==e.custom&&Array.prototype.push.apply(r,Pl(e.custom)),a&&void 0!==e.editor&&!l&&Array.prototype.push.apply(r,Ll(e.editor,t,n,o,a,l++)),a||void 0===e.saveOnly||l||Array.prototype.push.apply(r,Ll(e.saveOnly,t,n,o,a,l++)),r},Rl=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Desktop";const n=[(0,v.omit)(e,["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","editor","ie11","saveOnly"])];return"Desktop"===t?(n.push(e.desktopTablet||{}),n.push(e.desktopOnly||{}),e.editor&&(n.push((0,v.omit)(e.editor,["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","editor","ie11","saveOnly"])),n.push(e.editor.desktopTablet||{}),n.push(e.editor.desktopOnly||{}))):"Tablet"===t?(n.push(e.desktopTablet||{}),n.push(e.tablet||{}),n.push(e.tabletOnly||{}),e.editor&&(n.push((0,v.omit)(e.editor,["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","editor","ie11","saveOnly"])),n.push(e.editor.desktopTablet||{}),n.push(e.editor.tablet||{}),n.push(e.editor.tabletOnly||{}))):(n.push(e.tablet||{}),n.push(e.mobile||{}),e.editor&&(n.push((0,v.omit)(e.editor,["desktopTablet","desktopOnly","tablet","tabletOnly","mobile","editor","ie11","saveOnly"])),n.push(e.editor.tablet||{}),n.push(e.editor.mobile||{}))),n.forEach((e=>{Object.keys(e).forEach((t=>{Object.keys(e[t]).forEach((n=>void 0===e[t][n]?delete e[t][n]:{})),Object.keys(e[t]).length||delete e[t]}))})),Y().all(n)},Al=(0,o.memo)((e=>{const{breakTablet:t=1024,breakMobile:n=768,styleFunc:a=(()=>{})}=e,l=oe(),{clientId:r}=(0,ke.useBlockEditContext)(),i=ve(),s=is(i.uniqueId),c=a({...i,clientId:r}).map((e=>{const o=Rl(e,l);return Ll(o,s,t,n,!0)}));return c?c.map(((e,t)=>(0,o.createElement)("style",{key:t},e))):null}));Al.Content=e=>{const{breakTablet:t=1024,breakMobile:n=768,styleFunc:a=(()=>{}),attributes:l={}}=e,r=is(l.uniqueId),i=Ll(Hl(a(l)),r,t,n,!1);return i&&i.length?(0,o.createElement)("style",null,cs(i.join(""))):null},Al.displayName="Style";const Hl=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];const n=Y().all(e);return t?Vl(n):n};Al.addAttributes=e=>{e.add({attributes:{generatedCss:{type:"string",source:"html",selector:".stk-block > style:not(.stk-custom-css),\n\t\t\t\t\t .stk-block > * > style:not(.stk-custom-css)",default:""}},versionAdded:"3.0.3",versionDeprecated:""})};const Fl=(0,o.memo)((e=>{var t;const{version:n,versionAdded:a,versionDeprecated:l,styles:r,breakTablet:i,breakMobile:s}=e,c=oe(),u=ve((e=>e.uniqueId)),d=is(u),{clientId:p}=(0,ke.useBlockEditContext)();let m=d;if(!d){const e=ps(p);m=is(e)}const h=(null===(t=m)||void 0===t?void 0:t.replace("stk-",""))||"",g=Wi(h),v=g&&!m.match(/-[\d]$/g)?m+`-${g}`:m,b=(0,o.useMemo)((()=>n?K()(n,a)>=0&&(!l||-1===K()(n,l)):!!l),[n,a,l]),f=Bi((()=>{if(!b)return"";const e=Rl(r,c);return Ll(Vl(e),v,i,s).join("")}),[b,r,c,v,i,s]),k=Bt(f);return b&&f&&v&&k?(0,o.createElement)("style",null,k):null}));Fl.defaultProps={styles:null,blockUniqueClassName:"",breakTablet:1024,breakMobile:768,doImportant:!0,deviceType:"",version:"",versionAdded:"",versionDeprecated:!1},Fl.Content=e=>{const{version:t,versionAdded:n,versionDeprecated:a,styles:l,blockUniqueClassName:r,breakTablet:i,breakMobile:s}=e;if(!e.blockUniqueClassName)return null;if(!(t?K()(t,n)>=0&&(!a||-1===K()(t,a)):a))return null;const c=Ll(Vl(l),r,i,s);return c?(0,o.createElement)(o.Fragment,null,cs(c.join(""))):null},Fl.Content.defaultProps={styles:null,blockUniqueClassName:"",breakTablet:1024,breakMobile:768,doImportant:!0,version:"",versionAdded:"",versionDeprecated:!1};const Vl=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return"object"!=typeof e?Ji(e,t):Object.keys(e).reduce(((n,o)=>({...n,[o]:Vl(e[o],t)})),{})},jl=(0,o.forwardRef)(((e,t)=>{const{children:n,className:a,tagName:l,...r}=e,i=g()(["stk-link",a]),s=l||"a";return(0,o.createElement)(s,c({ref:t,className:i,href:"a"===l?"javascript:void(0)":void 0},r),n)}));jl.defaultProps={className:"",tagName:"a"},jl.Content=e=>{const t=g()(["stk-link",e.className]),n=(0,v.omit)(e,["target","rel","tagName"]),a=e.tagName||"a";e.target&&(n.target=e.target);const l=(e.rel||"").split(" ").filter((e=>!!e));return"_blank"===e.target&&(l.includes("noreferrer")||l.push("noreferrer"),l.includes("noopener")||l.push("noopener")),l.length&&(n.rel=l.join(" ")),(0,o.createElement)(a,c({},n,{className:t}))},jl.Content.defaultProps={className:"",target:"",rel:"",href:"",tagName:"a"};const zl=[.25,.33333,.5,.66666,.75,1],$l=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1],Ul=function(e,t,n){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"right",a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];const l=e.slice(0,t).reduce(((e,t)=>e+t),0),r=l+e[t],i=a?$l:zl;return i.map((e=>{const t=n*e;return"right"===o?t-l:r-t}))},ql=zl.map((e=>100*e)),Gl=$l.map((e=>100*e)),Wl=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=t?Gl:ql;return e.map((e=>{let t=e;return n.some((n=>Math.abs(e-n)<.2&&(t=n,!0))),t}))},Kl=(0,o.createContext)({showColumnTooltip:!1,setShowColumnTooltip:()=>{}}),Jl=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512"},e),(0,o.createElement)("path",{d:"M207 381.5L12.7 187.1c-9.4-9.4-9.4-24.6 0-33.9l22.7-22.7c9.4-9.4 24.5-9.4 33.9 0l154.7 154 154.7-154c9.4-9.3 24.5-9.3 33.9 0l22.7 22.7c9.4 9.4 9.4 24.6 0 33.9L241 381.5c-9.4 9.4-24.6 9.4-33.9 0z"}))},Yl={Desktop:5,Tablet:10,Mobile:10},Xl=e=>(Math.trunc(10*e)/10).toFixed(1),Ql=e=>{const{clientId:t}=(0,ke.useBlockEditContext)(),{getEditorDom:n}=(0,ne.useSelect)("stackable/editor-dom"),{isFirstBlock:a,isLastBlock:l,isOnlyBlock:i,adjacentBlocks:s,blockIndex:c,parentBlock:u}=(0,ne.useSelect)((e=>{const{getBlockOrder:n,getBlockRootClientId:o,getBlock:a}=e("core/block-editor"),l=o(t),r=a(l),i=r.innerBlocks,s=n(l).indexOf(t);return{isFirstBlock:0===s,isLastBlock:s===i.length-1,isOnlyBlock:1===i.length,adjacentBlocks:i,blockIndex:s,parentBlock:r}}),[t]),d=!e.context["stackable/innerBlockOrientation"],p=!!e.context["stackable/columnWrapDesktop"],m=oe(),[h,b]=(0,o.useState)([]),[f,k]=(0,o.useState)(""),[_,y]=(0,o.useState)([]),[w,E]=(0,o.useState)(2e3),[S,x]=(0,o.useState)(""),[C,T]=(0,o.useState)(null),M="Desktop"===m,N="Tablet"===m,[I,O]=(0,o.useState)(null==s?void 0:s.length);(0,o.useEffect)((()=>{I&&null!=s&&s.length?(I!==(null==s?void 0:s.length)&&(e.onResetDesktop(),O(s.length)),(null==s?void 0:s.length)<I&&1===(null==s?void 0:s.length)&&e.onResetTabletMobile()):O(null==s?void 0:s.length)}),[s]),(0,o.useEffect)((()=>{u&&M&&!p&&F()}),[p]);const[B,P]=(0,o.useState)(!1);(0,o.useEffect)((()=>(P(!0),()=>{P(!1)})),[]);const D=te();(0,o.useEffect)((()=>{T(null)}),[D]);const L=g()(["stk-column-resizeable",e.className]),R={top:!1,right:"Desktop"===m?!i&&(!l||p):!i,bottom:!1,left:"Desktop"===m&&!i&&!a&&!p,topRight:!1,bottomRight:!1,bottomLeft:!1,topLeft:!1},A=null==u?void 0:u.clientId,H=(0,o.useRef)(void 0),F=()=>{const t=(0,ne.select)("core/block-editor").getBlock(A);if(!t)return;const n=H.current=t.innerBlocks;let o=0;const a=n.map((e=>{let{attributes:t}=e;return o+=t.columnWidth,t.columnWidth}));o<100&&o>0&&(a[a.length-1]+=100-o,(0,v.isEqual)(a.map((e=>0|e)),[33,33,33])?e.onChangeDesktop([33.33,33.33,33.33]):e.onChangeDesktop(a))},[V,j]=(0,o.useState)(!1),[z,$]=(0,o.useState)(!1),{showColumnTooltip:U,setShowColumnTooltip:q}=(0,o.useContext)(Kl);return(0,o.useEffect)((()=>{V||(z&&!U?q(t):z||U!==t||q(!1))}),[U,q,z,V,t]),(0,o.createElement)(r.ResizableBox,{enable:!!e.isHovered&&R,minWidth:"30",minHeight:"30",maxWidth:w,className:L,showHandle:!(!d||!e.isHovered)&&e.showHandle,snap:C,snapGap:20,onResizeStart:(e,o)=>{const a=(0,ne.select)("core/block-editor").getBlock(A),{columnGap:l,columnGapTablet:r,columnGapMobile:i}=(null==a?void 0:a.attributes)||{},s=l||0,u=n(),d=H.current=a.innerBlocks;if(M&&!p){const e=s*(d.length-1),t=u.querySelector(`[data-block="${A}"] .stk-inner-blocks`).clientWidth-e,n=d.every((e=>{let{attributes:t}=e;return!t.columnWidth})),a=d.map((e=>{let{clientId:o,attributes:a}=e;if(n)return 1*t/d.length;if(a.columnWidth)return t*a.columnWidth/100;const l=u.querySelector(`[data-block="${o}"]`);return(null==l?void 0:l.clientWidth)||0}));b(a);const l="right"===o?c+1:c-1,r=a[c]+(a[l]-30);E(r)}else{const e=d.map((e=>{let{attributes:t}=e;return t.columnWidthTablet||t.columnWidth||100/d.length}));b(e);const n=u.querySelector(`[data-block="${t}"]`),o=(null==n?void 0:n.clientWidth)||0;k(o);const a=u.querySelector(`[data-block="${A}"]`),l=(null==a?void 0:a.clientWidth)||0;E(l)}$(!0)},onResize:(e,n,o,a)=>{let l=[];const r=H.current;if(M&&!p){const e=[...h],t=h.reduce(((e,t)=>e+t),0),o="right"===n?c+1:c-1;e[o]-=a.width,e[c]+=a.width,l=(e||[]).map((e=>parseFloat(Xl(e/t*100)))),l=Wl(l,D);let i=l.reduce(((e,t)=>e+t),0);100!==i&&(l[o]=parseFloat(Xl(l[o]+100-i))),i=l.reduce(((e,t)=>e+t),0),100!==i&&(l[o]=l[o]+100-i),y(l);const s=l.map(((e,t)=>`.editor-styles-wrapper [data-block][data-block="${r[t].clientId}"] {\n\t\t\t\t\tflex: 1 1 ${e}% !important;\n\t\t\t\t\tmax-width: ${e}% !important;\n\t\t\t\t}\n\t\t\t\t[data-block="${r[t].clientId}"] .stk-resizable-column__size-tooltip {\n\t\t\t\t\t--width: '${Xl(e)}%' !important;\n\t\t\t\t}`)).join("");x(s),C||T({x:Ul(e,c,t,n,D)})}else{const e=f+a.width;l=(0,v.clamp)(parseFloat(Xl(e/w*100)),0,100),l=Wl([l],D)[0],y(l);const o=[...h];o[c]=l;const r=(e=>{const t=[];return e.reduce(((e,n)=>((0,v.sum)(e[e.length-1])+n>100?e.push([n]):e[e.length-1].push(n),t.push(e.length),e)),[[]]),t})(o),i=r.filter((e=>e===r[c])).length,s=`.editor-styles-wrapper [data-block][data-block="${t}"] {\n\t\t\t\t\tflex: 1 1 calc(${l}% - var(--stk-column-gap, 0px) * ${i-1} / ${i} ) !important;\n\t\t\t\t\tmax-width: calc(${l}% - var(--stk-column-gap, 0px) * ${i-1} / ${i} ) !important;\n\t\t\t\t}\n\t\t\t\t[data-block="${t}"] .stk-resizable-column__size-tooltip {\n\t\t\t\t\t--width: '${Xl(l)}%' !important;\n\t\t\t\t}`;x(s),C||T({x:Ul([100],0,w,n,D)})}},onResizeStop:(t,n,o,a)=>{const l=H.current;if(a.width)if(M&&!p)(0,v.isEqual)(_.map((e=>0|e)),[33,33,33])?e.onChangeDesktop([33.33,33.33,33.33]):e.onChangeDesktop(_);else if(M){const t=l.map((e=>{let{attributes:t}=e;return t.columnWidth||100/l.length}));t[c]=_,e.onChangeDesktopWrap(_,t,c)}else if(N){const t=l.map((e=>{let{attributes:t}=e;return t.columnWidthTablet||t.columnWidth||100/l.length}));t[c]=_,e.onChangeTablet(_,t,c)}else{const t=l.map((e=>{let{attributes:t}=e;return t.columnWidthMobile||100}));t[c]=_,e.onChangeMobile(_,t,c)}S&&setTimeout((()=>{B&&x("")}),400),T(null),$(!1),H.current=void 0}},d&&(0,o.createElement)(Zl,{isVisible:!i,adjacentBlocks:s,isOnlyBlock:i,blockIndex:c,isLastBlock:l,isFirstBlock:a,value:M?e.columnWidth:N?e.columnWidthTablet||e.columnWidth:e.columnWidthMobile,onChange:t=>{if(""!==t&&t<Yl[m])return;const n=(0,ne.select)("core/block-editor").getBlock(A).innerBlocks;if(M&&!p){const o=n.every((e=>{let{attributes:t}=e;return!t.columnWidth})),a=n.map((e=>{let{attributes:t}=e;return o?100/n.length:t.columnWidth})),l=n.length-1!==c?c+1:c-1,r=a[c]+(a[l]-5),i=(0,v.clamp)(t,Yl.Desktop,r),s=i-a[c];a[l]-=s,a[c]=i,e.onChangeDesktop(a)}else if(M){const o=n.map((e=>{let{attributes:t}=e;return t.columnWidth||100/n.length})),a=t?(0,v.clamp)(t,Yl[m],100):"";o[c]=a,e.onChangeDesktopWrap(a,o,c)}else if(N){const o=n.map((e=>{let{attributes:t}=e;return t.columnWidthTablet||t.columnWidth||100/n.length})),a=t?(0,v.clamp)(t,Yl[m],100):"";o[c]=a,e.onChangeTablet(a,o,c)}else{const o=n.map((e=>{let{attributes:t}=e;return t.columnWidthMobile||100})),a=t?(0,v.clamp)(t,Yl[m],100):"";o[c]=a,e.onChangeMobile(a,o,c)}},onTogglePopup:e=>{j(e),e?q(t):z||U!==t||q(!1)},tooltipProps:{onMouseEnter:()=>$(!0),onMouseLeave:()=>$(!1)}}),S&&(0,o.createElement)("style",null,S),e.children)},Zl=(0,o.memo)((e=>{const{adjacentBlocks:t,isOnlyBlock:n,blockIndex:a,isLastBlock:l,isFirstBlock:r}=e,i=oe(),[u,p]=(0,o.useState)(!1),[m,h]=(0,o.useState)(""),[g,b]=(0,o.useState)(""),f=(0,o.useRef)(),k=(0,o.useRef)();let _="";if(void 0!==t&&!e.value&&!m)if("Desktop"===i||"Tablet"===i){const e=Xl(100/t.length);_="33.3"===e.toString()?33.33:e}else _=100;const y=(e.value?Xl(parseFloat(e.value)):"")||m||_,w=y!==(0,d.__)("Auto",s.i18n)?`'${y}%'`:`'${y}'`;return(0,o.useEffect)((()=>{e.onTogglePopup&&e.onTogglePopup(u),u&&(h(e.value),b(e.value||(_!==(0,d.__)("Auto",s.i18n)?_:"")),setTimeout((()=>{var e;null===(e=f.current)||void 0===e||e.querySelector("input").select()}),1))}),[u]),(0,o.useEffect)((()=>{var e;const t=()=>{p(!0)};return null===(e=k.current)||void 0===e||e.addEventListener("openColumnInputPopup",t),()=>{var e;null===(e=k.current)||void 0===e||e.removeEventListener("openColumnInputPopup",t)}}),[k.current]),(0,o.createElement)(o.Fragment,null,!n&&u&&(0,o.createElement)(nt,{className:"stk-resizable-column__popup",anchorRef:k.current,position:"bottom right",onFocusOutside:e=>{e.relatedTarget!==k.current&&p(!1)},onEscape:()=>p(!1)},(0,o.createElement)("div",{ref:f},(0,o.createElement)(Ft,{label:(0,d.__)("Column",s.i18n),className:"stk-resizable-column__input",value:g,allowReset:!1,onChange:t=>{const n="Desktop"===i?m:"",o=(0,v.clamp)(t,0,100)||n;""===o&&h(""),e.onChange(o),b(t)},onKeyDown:e=>{9===e.keyCode&&(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"right";if(window.CustomEvent)if("right"===e){const e=l?0:a+1,t=k.current.closest(".stk-row").querySelectorAll(".stk-resizable-column__size-tooltip")[e];null==t||t.dispatchEvent(new window.CustomEvent("openColumnInputPopup"))}else{const e=r?t.length-1:a-1,n=k.current.closest(".stk-row").querySelectorAll(".stk-resizable-column__size-tooltip")[e];null==n||n.dispatchEvent(new window.CustomEvent("openColumnInputPopup"))}}(e.shiftKey?"left":"right"),e.stopPropagation(),e.preventDefault())},placeholder:m||_||e.value}))),!n&&(0,o.createElement)("div",c({},e.tooltipProps,{className:"stk-resizable-column__size-tooltip",ref:k,style:{"--width":w},onMouseDown:e=>{p(!u),e.preventDefault()},onKeyDown:e=>{13===e.keyCode&&p(!u)},role:"button",tabIndex:"0"}),(0,o.createElement)(Jl,{fill:"currentColor",width:"10"})))})),er=()=>{};Zl.defaultProps={isVisible:!0,value:"",onChange:er,tooltipProps:{},onTogglePopup:null},Ql.defaultProps={className:"",context:null,showHandle:!0,columnWidth:"",columnWidthTablet:"",columnWidthMobile:"",isHovered:!1,onChangeDesktop:er,onChangeTablet:er,onChangeMobile:er,onResetDesktop:er},(0,o.memo)(Ql);(0,d.__)("Image",s.i18n);const tr={Desktop:24,Tablet:24,Mobile:16},nr=(0,v.range)(50,1001,50),or=(0,v.range)(8,1001,8),ar={bottom:{height:"100%"}},lr={top:!1,right:!1,bottom:!0,left:!1},rr=e=>{const{deviceType:t}=e,{name:n}=(0,ke.useBlockEditContext)(),[a,l]=(0,o.useState)(null),[i,s]=(0,o.useState)(null),[c,u]=(0,o.useState)(!1),d=(0,o.useRef)(null),[p,h]=(0,o.useState)(nr),v=te();(0,o.useEffect)((()=>{h(null)}),[v]);const b=(0,m.applyFilters)("stackable.resizable-bottom-margin.default",tr[t],n),f=g()(["stk-resizable-bottom-margin"],{"stk--is-resizing":null!==a,"stk--is-tiny":(""!==e.value?e.value:b)<5}),k=e.value||0===e.value?e.unit:"px",_=e.value||0===e.value?e.value:b,y=e=>{if("%"===k&&d.current){const t=d.current.getParentSize().width;return{height:_/100*t+e,delta:e/t*100}}return{height:_,delta:e}};return(0,o.createElement)(r.ResizableBox,{ref:d,className:f,minHeight:"0",handleStyles:ar,enable:lr,size:{height:y(0).height},snap:p,snapGap:5,onResizeStart:()=>{d.current&&d.current.resizable&&d.current.resizable.closest(".wp-block")?s(d.current.resizable.getBoundingClientRect().top-d.current.resizable.closest(".wp-block").getBoundingClientRect().top):s(null),l(_),u(!0)},onResize:(e,t,n,o)=>{let a=o.height;if("%"===k){const{height:e,delta:t}=y(a);a=t,n.style.height=`${e}px`}null!==i&&(n.style.top=`${i}px`),l(_+a),p||h(function(){return{y:arguments.length>0&&void 0!==arguments[0]&&arguments[0]?or:nr}}(v))},onResizeStop:(t,n,o)=>{o.style.top="",e.onChange(parseInt(a,10)===parseInt(b,10)&&"px"===k?"":parseInt(a,10)),l(null),u(!1)}},e.previewSelector&&c&&(0,o.createElement)("style",null,`.editor-styles-wrapper ${e.previewSelector} { margin-bottom: ${a}${k} !important; }`),(0,o.createElement)("span",{className:"stk-resizable-bottom-margin__label"},`${c?parseInt(a,10):_}${k}`))};rr.defaultProps={previewSelector:"",value:"",onChange:()=>{}};const{Slot:ir,Fill:sr}=(0,r.createSlotFill)("StackableLayoutPanel"),cr=e=>{let{children:t}=e;const{name:n}=(0,ke.useBlockEditContext)(),[a]=Bo(`tabCache-${n}`,"layout");return"layout"!==a?null:(0,o.createElement)(ke.InspectorControls,null,t)},ur=e=>{const{name:t}=(0,ke.useBlockEditContext)(),n=(0,_e.getBlockSupport)(t,"stkDefaultTab")||"style",[a,l]=Bo(`tabCache-${t}`,e.tabs.includes(n)?n:"style");return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(ke.InspectorControls,null,(0,o.createElement)(oa,{tabs:e.tabs,initialTab:a,onClick:l})),(0,o.createElement)(cr,null,e.hasLayoutPanel&&(0,o.createElement)(zo,{title:(0,d.__)("Layout",s.i18n),id:"layout",initialOpen:!0},(0,o.createElement)(ir,null))))};ur.defaultProps={tabs:["layout","style","advanced"],hasLayoutPanel:!0},(0,o.memo)(ur);const dr=e=>{let t=e.videoUrl,n=e.videoThumbnailUrl;const a=oe();return"Desktop"!==a&&e.videoUrlTablet&&(t=e.videoUrlTablet),"Mobile"===a&&e.videoUrlMobile&&(t=e.videoUrlMobile),"Desktop"!==a&&e.videoThumbnailUrlTablet&&(n=e.videoThumbnailUrlTablet),"Mobile"===a&&e.videoThumbnailUrlMobile&&(n=e.videoThumbnailUrlMobile),ss(t)?(0,o.createElement)("video",{className:"stk-video-background",autoPlay:!0,muted:!0,loop:!0,playsinline:!0,src:t,poster:n}):null};dr.defaultProps={videoUrl:"",videoUrlTablet:"",videoUrlMobile:"",videoThumbnailUrl:"",videoThumbnailUrlTablet:"",videoThumbnailUrlMobile:""},(dr.Content=e=>{const t=g()(["stk-video-background"],{"stk--hide-tablet":e.videoUrlTablet,"stk--hide-mobile":e.videoUrlTablet||e.videoUrlMobile}),n=g()(["stk-video-background"],{"stk--hide-desktop":!0,"stk--hide-mobile":e.videoUrlMobile}),a=g()(["stk-video-background"],{"stk--hide-desktop":!0,"stk--hide-tablet":!0});return(0,o.createElement)(o.Fragment,null,ss(e.videoUrl)&&(0,o.createElement)("video",{className:t,autoPlay:!0,muted:!0,loop:!0,playsinline:!0,src:e.videoUrl,poster:e.videoThumbnailUrl}),ss(e.videoUrlTablet)&&(0,o.createElement)("video",{className:n,autoPlay:!0,muted:!0,loop:!0,playsinline:!0,src:e.videoUrlTablet,poster:e.videoThumbnailUrlTablet}),ss(e.videoUrlMobile)&&(0,o.createElement)("video",{className:a,autoPlay:!0,muted:!0,loop:!0,playsinline:!0,src:e.videoUrlMobile,poster:e.videoThumbnailUrlMobile}))}).defaultProps={videoUrl:"",videoUrlTablet:"",videoUrlMobile:"",videoThumbnailUrl:"",videoThumbnailUrlTablet:"",videoThumbnailUrlMobile:""};const pr=dr,mr=e=>{const{blockTag:t,className:n,hasBackground:a,backgroundUrl:l,backgroundUrlTablet:r,backgroundUrlMobile:i,backgroundThumbnailUrl:s,backgroundThumbnailUrlTablet:u,backgroundThumbnailUrlMobile:d,backgroundColorType:p,...m}=e,h=g()([n],{"stk--has-background-overlay":a&&("gradient"===p||l||r||i)});return(0,o.createElement)(t,c({className:h},m),e.children,(0,o.createElement)(pr,{videoUrl:l,videoUrlTablet:r,videoUrlMobile:i,videoThumbnailUrl:s,videoThumbnailUrlTablet:u,videoThumbnailUrlMobile:d}))};mr.defaultProps={blockTag:"div",className:"",hasBackground:!0,backgroundUrl:"",backgroundUrlTablet:"",backgroundUrlMobile:"",backgroundColorType:""},(mr.Content=e=>{const{blockTag:t,className:n,hasBackground:a,backgroundUrl:l,backgroundUrlTablet:r,backgroundUrlMobile:i,backgroundThumbnailUrl:s,backgroundThumbnailUrlTablet:u,backgroundThumbnailUrlMobile:d,backgroundColorType:p,...m}=e,h=g()([n],{"stk--has-background-overlay":a&&("gradient"===p||l||r||i)});return(0,o.createElement)(t,c({className:h},m),e.children,(0,o.createElement)(pr.Content,{videoUrl:l,videoUrlTablet:r,videoUrlMobile:i,videoThumbnailUrl:s,videoThumbnailUrlTablet:u,videoThumbnailUrlMobile:d}))}).defaultProps={...mr.defaultProps};const hr=function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-10 210h1620V105.2H-10z"}))},gr={"wave-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-1_svg__st2",d:"M1341.4 48.9c-182.4 0-254.2 80.4-429.4 80.4-117.8 0-209.7-67.5-393.5-67.5-142.2 0-212.6 38.8-324.6 38.8S-10 64.7-10 64.7V210h1620V102c-110.6-40.2-181-53.1-268.6-53.1z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1341.4 151.4C1159 151.4 1087.2 71 912 71c-117.8 0-209.7 67.5-393.5 67.5-142.2 0-212.6-38.8-324.6-38.8S-10 135.6-10 135.6v75.9h1620V98.3c-110.6 40.2-181 53.1-268.6 53.1z"}))}}},"wave-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-2_svg__st2",d:"M1432.2 67.4c-88.8-16.7-156-5.3-204 8.5s-147.1 62.2-223.1 73.9c-75.4 11.6-164-7.5-275-27.9S571 88.1 456 98.1c-119.7 10.4-224.7 52-294.4 73-94.5 28.5-171.6-3-171.6-3V210h1620V91.6c-53 10-86.8-7.1-177.8-24.2z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1432.2 156.2c-88.8 16.7-156 5.3-204-8.5s-147.1-62.2-223.1-73.9c-75.4-11.6-164 7.5-275 27.9S571 135.5 456 125.5c-119.7-10.4-224.7-52-294.4-73C67.1 24-10 55.5-10 55.5v155.2h1620V132c-53-10-86.8 7.1-177.8 24.2z"}))}}},"wave-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"wave-3_svg__st2",d:"M1413.6 161.4c-157.9 0-338.2-37.7-495.1-67.4-215.6-40.8-328.1-44.6-418.2-41.1S317 73.4 188.4 102-10 136.2-10 136.2v74.2h1620v-68.5s-68.8 19.5-196.4 19.5z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1413.6 39.3c-157.9 0-338.2 37.7-495.1 67.4-215.6 40.8-328.1 44.6-418.2 41.1S317 127.3 188.4 98.7-10 64.5-10 64.5v150.2h1620V58.8s-68.8-19.5-196.4-19.5z"}))}}},"wave-4":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 209.7v-51.2s-81.4-33.3-241.4-23.1c-157.4 10-247.9 49.6-340.9 49.4-93.1-.1-121-14.6-174.2-33.6-75.7-27.1-166.9-27.2-185.5-25.1-18.7 2.1-67.8 5.5-106-19.3-38.2-24.7-94.8-55.4-209.2-43.1C238.1 76.3 80 111.8-10 55.7v154h1620z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 208.9V79.2s-81.4 33.3-241.4 23.1c-157.4-10-247.9-49.6-340.9-49.4-93.1.1-121 14.6-174.2 33.6-75.7 27.1-166.9 27.2-185.5 25.1-18.7-2.1-67.8-5.5-106 19.3-38.2 24.7-94.8 55.4-209.2 43.1C238.1 161.4 80 125.9-10 182v26.9h1620z"}))}}},"curve-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-1_svg__st2",d:"M1610 177.3C1423 122.9 1133.3 88 808 88c-334.7 0-631.8 37-818 94.1v28h1620v-32.8z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 78.3c-187 54.4-476.7 89.3-802 89.3-334.7 0-631.8-37-818-94.1v136.8h1620v-132z"}))}}},"curve-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-2_svg__st2",d:"M-10 207.6h1620S1430.8 23.8 1138.3 23.8C884 23.8 234.9 140.1-10 197.9v9.7z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-10 207.4h1620S1608.1.1 1604.2 3.7c-32.8 30.8-203.9 178.2-465.9 178.2C884 181.9 234.9 65.6-10 7.8v199.6z"}))}}},"curve-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"curve-3_svg__st1",d:"M-6.7 13.4S456 171.1 876.1 171.1 1606.4 16 1606.4 16v192.6H-6.7V13.4z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M-6.6 177.6S456.1 40.3 876.2 40.3s730.3 135.1 730.3 135.1v33.5H-6.6v-31.3z"}))}}},"slant-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"slant-1_svg__st2",d:"M-10 210h1620V.5L-10 183.9z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 210H-10V.5l1620 183.4z"}))}}},"slant-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"slant-2_svg__st1",d:"M1610 39.2V209H-10V39.2l810 118.9 810-118.9z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610 160v53.2H-10V160L800 41.1 1610 160z"}))}}},"straight-1":{default:{shape:hr},inverted:{shape:hr}},"rounded-1":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"rounded-1_svg__st1",d:"M1602.9 209l-9.3-117.7c-3.8-48.1-46.3-84.4-94.4-80.6L-8 131.2V209h1610.9z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1604.5-2.7l-10.9 104.5c-3.8 48.1-46.3 84.4-94.4 80.6L-8 61.8v148.5h1613.9l-1.4-213z"}))}}},"rounded-2":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{className:"rounded-2_svg__st2",d:"M1609.5 133.2h-9.5c0-10.9-8.9-19.8-19.8-19.8H19.8C8.9 113.3 0 122.2 0 133.2l-10-.2v77h1619.5v-76.8z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1609.5 128.5h-9.5c0 10.9-8.9 19.8-19.8 19.8H19.8C8.9 148.4 0 139.5 0 128.5l-10 .2v83.1h1619.5v-83.3z"}))}}},"rounded-3":{default:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610.5 189.1s-128.2-82.6-138.2-89.2c-17.1-11.3-37.2-15.5-87-18.1-49.7-2.6-753.6-40.7-783.9-42.3-30.3-1.5-81.1-1.3-121.1 8.2S-9.5 163.2-9.5 163.2v50h1620v-24.1z"}))}},inverted:{shape:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1600 200"},e),(0,o.createElement)("path",{d:"M1610.5 24.1s-128.2 82.6-138.2 89.2c-17.1 11.3-37.2 15.5-87 18.1-49.7 2.6-753.6 40.7-783.9 42.3-30.3 1.5-81.1 1.3-121.1-8.2S-9.5 50-9.5 50v158.9h1620V24.1z"}))}}}},vr=e=>{const{design:t,inverted:n,layer:a}=e,{shape:l}=gr[t||"wave-1"][n?"inverted":"default"];return(0,o.createElement)(l,{className:`stk-separator__layer-${a}`,preserveAspectRatio:"none","aria-hidden":!0})};vr.defaultProps={className:"",design:"wave-1",inverted:!1,layer:1},vr.Content=e=>(0,o.createElement)(vr,e);const br=(0,o.memo)((e=>{const{align:t,className:n="",blockHoverClass:a="",children:l,hoverRef:r,...i}=e,s=g()(a,{[`align${t}`]:t}),c=(0,ke.useBlockProps)({...i||{},ref:r,className:s,"data-align":t});return c.className=c.className.replace(n,"").trim(),(0,o.createElement)("div",c,l)}));br.displayName="BlockWrapper";const fr=br;var kr=n(2473),_r=n.n(kr),yr=Math.sqrt(50),wr=Math.sqrt(10),Er=Math.sqrt(2);function Sr(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function xr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Sr(Object(n),!0).forEach((function(t){Nr(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Sr(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Cr(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Tr(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function Mr(e,t,n){return t&&Tr(e.prototype,t),n&&Tr(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Nr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ir(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Br(e,t)}function Or(e){return Or=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Or(e)}function Br(e,t){return Br=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Br(e,t)}function Pr(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Dr(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Pr(e)}function Lr(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,o=Or(e);if(t){var a=Or(this).constructor;n=Reflect.construct(o,arguments,a)}else n=o.apply(this,arguments);return Dr(this,n)}}function Rr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,a,l=[],r=!0,i=!1;try{for(n=n.call(e);!(r=(o=n.next()).done)&&(l.push(o.value),!t||l.length!==t);r=!0);}catch(e){i=!0,a=e}finally{try{r||null==n.return||n.return()}finally{if(i)throw a}}return l}}(e,t)||Hr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ar(e){return function(e){if(Array.isArray(e))return Fr(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||Hr(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Hr(e,t){if(e){if("string"==typeof e)return Fr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Fr(e,t):void 0}}function Fr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var Vr="react-compound-slider:";function jr(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return function(t,n){return t.val>n.val?e?-1:1:n.val>t.val?e?1:-1:0}}function zr(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=e.findIndex((function(e){return e.key===t}));if(-1!==a){var l=e[a],r=l.key,i=l.val;return i===n?e:[].concat(Ar(e.slice(0,a)),[{key:r,val:n}],Ar(e.slice(a+1))).sort(jr(o))}return e}function $r(e,t){if(!e)return[0,0];var n=e.getBoundingClientRect();return[t?n.top:n.left,t?n.bottom:n.right]}function Ur(e){var t=e.type,n=void 0===t?"":t,o=e.touches;return!o||o.length>1||"touchend"===n.toLowerCase()&&o.length>0}function qr(e,t){return e?t.touches[0].clientY:t.touches[0].pageX}function Gr(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,a=0,l=e.map((function(e){var t=n.getValue(e);return e!==t&&(a+=1,_r()(!o,"".concat(Vr," Invalid value encountered. Changing ").concat(e," to ").concat(t,"."))),t})).map((function(e,t){return{key:"$$-".concat(t),val:e}})).sort(jr(t));return{handles:l,changes:a}}function Wr(e,t){return t}function Kr(e,t){for(var n=0;n<e.length;n++){if(e[n].key!==t[n].key)return e;if(t[n+1]&&t[n].val===t[n+1].val)return e}return t}function Jr(e,t,n,o,a){for(var l=-1,r=!0,i=0;i<e.length;i++){var s=e[i],c=t[i];if(!c||c.key!==s.key)return e;c.val!==s.val&&(l=i,r=c.val-s.val>0)}if(-1===l)return e;for(var u=r?n:-n,d=0;d<t.length;d++){var p=t[d],m=t[d+1];if(m&&p.val===m.val){if(d===l){var h=m.val+u;if(a(h)===h){var g=Jr(t,zr(t,m.key,m.val+u,o),n,o,a);return g===t?e:g}return e}var v=p.val+u;if(a(v)===v){var b=Jr(t,zr(t,p.key,p.val+u,o),n,o,a);return b===t?e:b}return e}}return t}function Yr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return t.forEach((function(t){return t&&t(e)}))}}var Xr=function(){return{value:0,percent:0}},Qr=function(e){Ir(n,e);var t=Lr(n);function n(){var e;Cr(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return(e=t.call.apply(t,[this].concat(a))).getRailProps=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.props,o=n.emitMouse,a=n.emitTouch;return xr(xr({},t),{},{onMouseDown:Yr(t&&t.onMouseDown,o),onTouchStart:Yr(t&&t.onTouchStart,a)})},e}return Mr(n,[{key:"render",value:function(){var e=this.getRailProps,t=this.props,n=t.getEventData,o=t.activeHandleID,a=void 0===o?"":o,l=(0,t.children)({getEventData:n||Xr,activeHandleID:a,getRailProps:e});return l&&u.Children.only(l)}}]),n}(u.Component),Zr=function(e){Ir(n,e);var t=Lr(n);function n(){var e;Cr(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return(e=t.call.apply(t,[this].concat(a))).autofocus=function(e){e.target instanceof HTMLElement&&e.target.focus()},e.getHandleProps=function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=e.props,a=o.emitKeyboard,l=o.emitMouse,r=o.emitTouch;return xr(xr({},n),{},{onKeyDown:Yr(n&&n.onKeyDown,(function(e){return a&&a(e,t)})),onMouseDown:Yr(n&&n.onMouseDown,e.autofocus,(function(e){return l&&l(e,t)})),onTouchStart:Yr(n&&n.onTouchStart,(function(e){return r&&r(e,t)}))})},e}return Mr(n,[{key:"render",value:function(){var e=this.getHandleProps,t=this.props,n=t.activeHandleID,o=void 0===n?"":n,a=t.children,l=t.handles,r=a({handles:void 0===l?[]:l,activeHandleID:o,getHandleProps:e});return r&&u.Children.only(r)}}]),n}(u.Component),ei=function(){function e(){Cr(this,e),this.interpolator=void 0,this.domain=[0,1],this.range=[0,1],this.domain=[0,1],this.range=[0,1],this.interpolator=null}return Mr(e,[{key:"createInterpolator",value:function(e,t){var n=this,o=e[0],a=e[1],l=t[0],r=t[1];return a<o?function(e){return n.interpolateValue(r,l)(n.deinterpolateValue(a,o)(e))}:function(e){return n.interpolateValue(l,r)(n.deinterpolateValue(o,a)(e))}}},{key:"interpolateValue",value:function(e,t){return t-=e=+e,function(n){return e+t*n}}},{key:"deinterpolateValue",value:function(e,t){return(t-=e=+e)?function(n){return(n-e)/t}:function(){return t}}},{key:"rescale",value:function(){return this.interpolator=null,this}},{key:"getValue",value:function(e){var t=this.domain,n=this.range;return(this.interpolator||(this.interpolator=this.createInterpolator(t,n)))(+e)}},{key:"setDomain",value:function(e){return this.domain=[e[0],e[1]],this.rescale(),this}},{key:"getDomain",value:function(){return this.domain}},{key:"setRange",value:function(e){return this.range=[e[0],e[1]],this}},{key:"getRange",value:function(){return this.range}},{key:"getTicks",value:function(e){var t=this.domain;return function(e,t,n){var o,a,l,r,i=-1;if(n=+n,(e=+e)==(t=+t)&&n>0)return[e];if((o=t<e)&&(a=e,e=t,t=a),0===(r=function(e,t,n){var o=(t-e)/Math.max(0,n),a=Math.floor(Math.log(o)/Math.LN10),l=o/Math.pow(10,a);return a>=0?(l>=yr?10:l>=wr?5:l>=Er?2:1)*Math.pow(10,a):-Math.pow(10,-a)/(l>=yr?10:l>=wr?5:l>=Er?2:1)}(e,t,n))||!isFinite(r))return[];if(r>0){let n=Math.round(e/r),o=Math.round(t/r);for(n*r<e&&++n,o*r>t&&--o,l=new Array(a=o-n+1);++i<a;)l[i]=(n+i)*r}else{r=-r;let n=Math.round(e*r),o=Math.round(t*r);for(n/r<e&&++n,o/r>t&&--o,l=new Array(a=o-n+1);++i<a;)l[i]=(n+i)/r}return o&&l.reverse(),l}(t[0],t[t.length-1],e||10)}}]),e}(),ti=function(){return{value:0,percent:0}},ni=function(e){Ir(n,e);var t=Lr(n);function n(){return Cr(this,n),t.apply(this,arguments)}return Mr(n,[{key:"render",value:function(){var e=this.props,t=e.children,n=e.values,o=e.scale,a=void 0===o?new ei:o,l=e.count,r=void 0===l?10:l,i=e.getEventData,s=void 0===i?ti:i,c=e.activeHandleID,d=t({getEventData:s,activeHandleID:void 0===c?"":c,ticks:(n||a.getTicks(r)).map((function(e){return{id:"$$-".concat(e),value:e,percent:a.getValue(e)}}))});return d&&u.Children.only(d)}}]),n}(u.Component),oi=function(){return{value:0,percent:0}},ai=function(e){Ir(n,e);var t=Lr(n);function n(){var e;Cr(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return(e=t.call.apply(t,[this].concat(a))).getTrackProps=function(t){var n=e.props,o=n.emitMouse,a=n.emitTouch;return xr(xr({},t||{}),{},{onMouseDown:Yr(t&&t.onMouseDown,o),onTouchStart:Yr(t&&t.onTouchStart,a)})},e}return Mr(n,[{key:"render",value:function(){for(var e=this.getTrackProps,t=this.props,n=t.children,o=t.left,a=void 0===o||o,l=t.right,r=void 0===l||l,i=t.scale,s=void 0===i?new ei:i,c=t.handles,d=void 0===c?[]:c,p=t.getEventData,m=void 0===p?oi:p,h=t.activeHandleID,g=void 0===h?"":h,v=s.getDomain(),b=[],f=0;f<d.length+1;f++){var k=d[f-1],_=d[f];0===f&&!0===a?k={id:"$",value:v[0],percent:0}:f===d.length&&!0===r&&(_={id:"$",value:v[1],percent:100}),k&&_&&b.push({id:"".concat(k.id,"-").concat(_.id),source:k,target:_})}var y=n({getEventData:m,activeHandleID:g,tracks:b,getTrackProps:e});return y&&u.Children.only(y)}}]),n}(u.Component);function li(e,t,n){return Math.min(Math.max(e,t),n)}var ri=Mr((function e(){var t=this;Cr(this,e),this.step=1,this.domain=[0,1],this.range=[0,1],this.setDomain=function(e){return t.domain=[e[0],e[1]],t},this.setRange=function(e){return t.range=[e[0],e[1]],t},this.setStep=function(e){return t.step=e,t},this.getValue=function(e){var n=Rr(t.domain,2),o=n[0],a=n[1],l=Rr(t.range,2),r=l[0],i=l[1],s=t.step,c=(li(e,o,a)-o)/(a-o);return li(s*Math.round(c*(i-r)/s)+r,r<i?r:i,i>r?i:r)}})),ii="undefined"!=typeof window&&"undefined"!=typeof document,si=function(){},ci=function(e,t,n,o){var a=o?e-t:e+t;return o?Math.max(n[0],a):Math.min(n[1],a)},ui=function(e,t,n,o){var a=o?e+t:e-t;return o?Math.min(n[1],a):Math.max(n[0],a)},di=[0,100],pi=function(e){Ir(n,e);var t=Lr(n);function n(){var e;Cr(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return(e=t.call.apply(t,[this].concat(a))).state={step:.1,values:[],domain:di,handles:[],reversed:!1,activeHandleID:"",valueToPerc:null,valueToStep:null,pixelToStep:null},e.slider=u.createRef(),e.onKeyDown=function(t,n){var o=["ArrowRight","ArrowUp"],a=["ArrowDown","ArrowLeft"],l=Pr(e),r=l.state.handles,i=l.props,s=i.step,c=void 0===s?.1:s,u=i.reversed,d=void 0!==u&&u,p=i.vertical,m=void 0!==p&&p,h=i.domain,g=void 0===h?[0,100]:h,v=t.key||"".concat(t.keyCode);if(o.concat(a).includes(v)){if(m){var b=[a,o];o=b[0],a=b[1]}t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault();var f=r.find((function(e){return e.key===n}));if(f){var k=f.val,_=k;o.includes(v)?_=ci(k,c,g,d):a.includes(v)&&(_=ui(k,c,g,d));var y=r.map((function(e){return e.key===n?{key:e.key,val:_}:e}));e.submitUpdate(y,!0)}}},e.onMouseDown=function(t,n){e.onStart(t,n,!1)},e.onTouchStart=function(t,n){Ur(t)||e.onStart(t,n,!0)},e.getEventData=function(t,n){var o,a=Pr(e),l=a.state,r=l.pixelToStep,i=l.valueToPerc,s=a.props.vertical;return r.setDomain($r(e.slider.current,s)),n&&t instanceof TouchEvent?o=r.getValue(qr(s,t)):t instanceof MouseEvent&&(o=r.getValue(s?t.clientY:t.pageX)),{value:o,percent:i.getValue(o)}},e.onMouseMove=function(t){var n=Pr(e),o=n.state,a=o.handles,l=o.pixelToStep,r=o.activeHandleID,i=void 0===r?"":r,s=n.props,c=s.vertical,u=s.reversed,d=void 0!==u&&u;l.setDomain($r(e.slider.current,c));var p=zr(a,i,l.getValue(c?t.clientY:t.pageX),d);e.submitUpdate(p)},e.onTouchMove=function(t){var n=Pr(e),o=n.state,a=o.handles,l=o.pixelToStep,r=o.activeHandleID,i=n.props,s=i.vertical,c=i.reversed;if(null!==l&&!Ur(t)){l.setDomain($r(e.slider.current,s));var u=zr(a,r,l.getValue(qr(s,t)),c);e.submitUpdate(u)}},e.onMouseUp=function(){var t=Pr(e),n=t.state,o=n.handles,a=void 0===o?[]:o,l=n.activeHandleID,r=t.props,i=r.onChange,s=void 0===i?si:i,c=r.onSlideEnd,u=void 0===c?si:c;s(a.map((function(e){return e.val}))),u(a.map((function(e){return e.val})),{activeHandleID:l}),e.setState({activeHandleID:""}),ii&&(document.removeEventListener("mousemove",e.onMouseMove),document.removeEventListener("mouseup",e.onMouseUp))},e.onTouchEnd=function(){var t=Pr(e),n=t.state,o=n.handles,a=n.activeHandleID,l=t.props,r=l.onChange,i=void 0===r?si:r,s=l.onSlideEnd,c=void 0===s?si:s;i(o.map((function(e){return e.val}))),c(o.map((function(e){return e.val})),{activeHandleID:a}),e.setState({activeHandleID:""}),ii&&(document.removeEventListener("touchmove",e.onTouchMove),document.removeEventListener("touchend",e.onTouchEnd))},e}return Mr(n,[{key:"componentDidMount",value:function(){var e=this.state.pixelToStep,t=this.props.vertical;e.setDomain($r(this.slider.current,t))}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"removeListeners",value:function(){ii&&(document.removeEventListener("mousemove",this.onMouseMove),document.removeEventListener("mouseup",this.onMouseUp),document.removeEventListener("touchmove",this.onTouchMove),document.removeEventListener("touchend",this.onTouchEnd))}},{key:"onStart",value:function(e,t,n){var o=this.state.handles,a=this.props.onSlideStart,l=void 0===a?si:a;n||e.preventDefault&&e.preventDefault(),e.stopPropagation&&e.stopPropagation(),o.find((function(e){return e.key===t}))?(this.setState({activeHandleID:t}),l(o.map((function(e){return e.val})),{activeHandleID:t}),n?this.addTouchEvents():this.addMouseEvents()):(this.setState({activeHandleID:""}),this.handleRailAndTrackClicks(e,n))}},{key:"handleRailAndTrackClicks",value:function(e,t){var n,o=this,a=this.state,l=a.handles,r=a.pixelToStep,i=this.props,s=i.vertical,c=i.reversed,u=void 0!==c&&c,d=this.slider;r.setDomain($r(d.current,s)),n=t?r.getValue(qr(s,e)):r.getValue(s?e.clientY:e.pageX);for(var p="",m=1/0,h=0;h<l.length;h++){var g=l[h],v=g.key,b=g.val,f=Math.abs(b-n);f<m&&(p=v,m=f)}var k=zr(l,p,n,u);this.setState({activeHandleID:p},(function(){o.submitUpdate(k,!0),t?o.addTouchEvents():o.addMouseEvents()}))}},{key:"addMouseEvents",value:function(){ii&&(document.addEventListener("mousemove",this.onMouseMove),document.addEventListener("mouseup",this.onMouseUp))}},{key:"addTouchEvents",value:function(){ii&&(document.addEventListener("touchmove",this.onTouchMove),document.addEventListener("touchend",this.onTouchEnd))}},{key:"submitUpdate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.props,o=n.mode,a=void 0===o?1:o,l=n.step,r=void 0===l?.1:l,i=n.onUpdate,s=void 0===i?si:i,c=n.onChange,u=void 0===c?si:c,d=n.reversed,p=void 0!==d&&d,m=this.state.valueToStep.getValue;this.setState((function(n){var o=n.handles,l=[];if("function"==typeof a)l=a(o,e,r,p,m),_r()(Array.isArray(l),"Custom mode function did not return an array.");else switch(a){case 1:l=Wr(0,e);break;case 2:l=Kr(o,e);break;case 3:l=Jr(o,e,r,p,m);break;default:l=e,_r()(!1,"".concat(Vr," Invalid mode value."))}return s(l.map((function(e){return e.val}))),t&&u(l.map((function(e){return e.val}))),{handles:l}}))}},{key:"render",value:function(){var e=this,t=this.state,n=t.handles,o=t.valueToPerc,a=t.activeHandleID,l=this.props,r=l.className,i=l.rootStyle,s=void 0===i?{}:i,c=l.rootProps,d=void 0===c?{}:c,p=l.component,m=void 0===p?"div":p,h=l.disabled,g=void 0!==h&&h,v=l.flatten,b=void 0!==v&&v,f=n.map((function(e){var t=e.key,n=e.val;return{id:t,value:n,percent:o.getValue(n)}})),k=u.Children.map(this.props.children,(function(t){return!0===function(e){if(!(0,u.isValidElement)(e))return!1;var t=e.type,n=t?t.name:"";return n===Zr.name||n===Qr.name||n===ni.name||n===ai.name}(t)?u.cloneElement(t,{scale:o,handles:f,activeHandleID:a,getEventData:e.getEventData,emitKeyboard:g?si:e.onKeyDown,emitMouse:g?si:e.onMouseDown,emitTouch:g?si:e.onTouchStart}):t}));return b?u.createElement(u.Fragment,null,u.createElement(m,xr(xr({},d),{},{style:s,className:r,ref:this.slider})),k):u.createElement(u.Fragment,null,u.createElement(m,xr(xr({},d),{},{style:s,className:r,ref:this.slider}),k))}}],[{key:"getDerivedStateFromProps",value:function(e,t){var n,o,a=e.step,l=void 0===a?.1:a,r=e.values,i=e.domain,s=void 0===i?di:i,c=e.reversed,u=void 0!==c&&c,d=e.onUpdate,p=void 0===d?si:d,m=e.onChange,h=void 0===m?si:m,g=e.warnOnChanges,v=void 0!==g&&g,b=t.valueToPerc,f=t.valueToStep,k=t.pixelToStep,_={};if(b&&f&&k||(b=new ei,f=new ri,k=new ri,_.valueToPerc=b,_.valueToStep=f,_.pixelToStep=k),t.domain===di||null===t.step||null===t.domain||null===t.reversed||l!==t.step||s[0]!==t.domain[0]||s[1]!==t.domain[1]||u!==t.reversed){var y=Rr(s,2),w=y[0],E=y[1];f.setStep(l).setRange([w,E]).setDomain([w,E]),!0===u?(b.setDomain([w,E]).setRange([100,0]),k.setStep(l).setRange([E,w])):(b.setDomain([w,E]).setRange([0,100]),k.setStep(l).setRange([w,E])),_r()(E>w,"".concat(Vr," Max must be greater than min (even if reversed). Max is ").concat(E,". Min is ").concat(w,"."));var S=Gr(r||t.values,u,f,v),x=S.handles;(S.changes||void 0===r||r===t.values)&&(p(x.map((function(e){return e.val}))),h(x.map((function(e){return e.val})))),_.step=l,_.values=r,_.domain=s===di?Ar(s):s,_.handles=x,_.reversed=u}else if(!((n=r)===(o=t.values)||n.length===o.length&&n.reduce(function(e){return function(t,n,o){return t&&e[o]===n}}(o),!0))){var C=Gr(r,u,f,v),T=C.handles;C.changes&&(p(T.map((function(e){return e.val}))),h(T.map((function(e){return e.val})))),_.values=r,_.handles=T}return Object.keys(_).length?_:null}}]),n}(u.PureComponent);const mi={position:"absolute",width:"100%",height:42,transform:"translate(0%, -50%)",borderRadius:7,cursor:"pointer"},hi={position:"absolute",width:"100%",height:4,backgroundColor:"rgb(221, 221, 221)",transform:"translate(0%, -50%)",borderRadius:7,pointerEvents:"none"},gi={backgroundColor:"var(--wp-admin-theme-color)",height:12,width:12},vi=e=>{let{getRailProps:t}=e;return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",c({style:mi},t())),(0,o.createElement)("div",{style:hi}))},bi=e=>{const{domain:[t,n],handle:{value:a,percent:l},disabled:r=!1,handleProps:i}=e;return(0,o.createElement)("button",c({role:"slider","aria-valuemin":t,"aria-valuemax":n,"aria-valuenow":a,disabled:r,style:{left:`${l}%`,...gi}},i))},fi=10,ki=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"right";if("left"===t)for(let t=e.length-1;t>=1;t--)if(e[t]<fi){const n=fi-e[t];e[t]=fi,e[t-1]-=n}for(let t=0;t<e.length-1;t++)if(e[t]<fi){const n=fi-e[t];e[t]=fi,e[t+1]-=n}if("right"===t)for(let t=e.length-1;t>=1;t--)if(e[t]<fi){const n=fi-e[t];e[t]=fi,e[t-1]-=n}return e},_i={position:"relative",width:"100%"},yi=[0,100],wi=e=>{let t=0;return[...e,100].map((e=>{const n=e-t;return t+=n,n}))},Ei=e=>{let t=0;return[...e].pop(),e.reduce(((n,o,a)=>{if(a===e.length-1)return n;const l=o+t;return t+=o,n.push(l),n}),[])},Si=(0,o.memo)((e=>{const t=(0,o.useRef)(null),n=(0,o.useRef)(0),a=(0,o.useRef)(null),l=(0,o.useRef)(!1);return(0,o.createElement)(pi,{className:"stk-column-widths-control__columns-slider",rootStyle:_i,onSlideStart:e=>t.current=e,mode:(e,o)=>{l.current=!0,t.current&&t.current.length===o.length||(t.current=e.map((e=>e.val)));let a=o.map((e=>e.val));if((0,v.isEqual)(t.current,a))return o;const r=((e,t)=>{let n;return t.some(((t,o)=>t!==e[o]&&(n=t>e[o]?"right":"left",!0))),n})(t.current,a),i=((e,t)=>{let n=-1;return t.some((t=>!e.includes(t)&&(n=t,!0))),-1===n&&t.some(((t,o)=>t!==e[o]&&(n=t,!0))),-1===n?0:n})(t.current,a);a=[...t.current],a[n.current]=i;const s=((e,t,n)=>{const o=wi(e),a=ki(o,n);return Ei(a)})(a,n.current,r);return t.current=s,s.forEach(((e,t)=>{o[t].val=e})),o},step:1,onChange:()=>{l.current=!1},onUpdate:t=>{l.current&&((0,v.isEqual)(a.current,t)||(a.current=t,e.onChange(wi(t))))},domain:yi,values:Ei(e.value)},(0,o.createElement)(Qr,null,(e=>{let{getRailProps:t}=e;return(0,o.createElement)(vi,{getRailProps:t})})),(0,o.createElement)(Zr,null,(e=>{let{handles:t,getHandleProps:a}=e;return(0,o.createElement)("div",{className:"slider-handles"},t.map(((e,t)=>{const l=a(e.id),r={...l,onMouseDown:e=>(n.current=t,l.onMouseDown(e)),onTouchStart:e=>(n.current=t,l.onTouchStart(e)),onKeyDown:e=>(n.current=t,l.onKeyDown(e))};return(0,o.createElement)(bi,{key:e.id,handle:e,domain:yi,handleProps:r})})))})))}),v.isEqual);Si.defaultProps={onChange:()=>{},value:[]};const xi=e=>e.every((e=>!e)),Ci=e=>{var t;const n={gridTemplateColumns:(a=e.value,a.map((e=>(e/100*a.length).toFixed(2)+"fr")).join(" ")),gridAutoFlow:xi(e.value)?"column":void 0,justifyContent:xi(e.value)?"space-around":void 0};var a;const l=g()("stk-column-widths-control__columns-input",{"stk-column-widths--many":(null===(t=e.value)||void 0===t?void 0:t.length)&&e.value.length>4});return(0,o.createElement)("div",{className:l,style:n},e.value.map(((t,n)=>(0,o.createElement)("input",{key:n,className:"components-column-widths-control__number",type:"number",step:"any",value:t||"",onChange:t=>{const o=t.target.value,a=o.includes(".")?parseFloat(parseFloat(o).toFixed(3)):parseInt(o,10),l=[...e.value];l[n]=isNaN(a)?"":a,e.onChange(l)},onBlur:()=>{const t=ki(e.value),o=100-t.reduce(((e,t)=>e+t),0);n<e.value.length-1?t[n+1]+=o:t[n-1]+=o,e.onChange(ki(t))}}))))};Ci.defaultProps={onChange:()=>{},value:[]};(0,d.__)("Column Widths",s.i18n);(0,d.__)("Column Widths",s.i18n);const Ti=(0,o.memo)((0,o.forwardRef)(((e,t)=>{const{attrNameTemplate:n,...a}=e,{fontFamily:l}=ve((e=>({fontFamily:e[Q(n)("fontFamily")]})));return(e=>{const{loadingThemeFont:t,themeFonts:n}=(0,ne.select)("stackable/theme-fonts").getThemeFonts();(0,o.useEffect)((()=>{n.includes(e)||(z(e),(0,m.doAction)("stackable.font-loader.load",e))}),[t,e])})(l),(0,o.createElement)(ke.RichText,c({ref:t},a))})));Ti.defaultProps={attrNameTemplate:"%s"},Ti.Content=e=>(0,o.createElement)(ke.RichText.Content,e);const Mi={placement:"left-start",offset:36,shift:!0},Ni=(Co((e=>(0,o.createElement)(Ii,e))),Eo((e=>{let{children:t}=e;return(0,o.createElement)("div",null,t)})),function(e){var t,n,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{withRef:!1};return n=t=function(t){function n(){var e,t;Cn(this,n);for(var o=arguments.length,a=new Array(o),l=0;l<o;l++)a[l]=arguments[l];return Sn(In(In(t=On(this,(e=Bn(n)).call.apply(e,[this].concat(a))))),"wrappedInstance",(0,u.createRef)()),t}return Dn(n,t),Mn(n,[{key:"componentDidMount",value:function(){(0,o.findDOMNode)(this).sortableHandle=!0}},{key:"getWrappedInstance",value:function(){return Rn()(a.withRef,"To access the wrapped instance, you need to pass in {withRef: true} as the second argument of the SortableHandle() call"),this.wrappedInstance.current}},{key:"render",value:function(){var t=a.withRef?this.wrappedInstance:null;return(0,u.createElement)(e,c({ref:t},this.props))}}]),n}(u.Component),Sn(t,"displayName",Xn("sortableHandle",e)),n}((()=>(0,o.createElement)(r.Dashicon,{icon:"menu",size:"16",tabIndex:"0"})))),Ii=e=>{const{item:t,onDelete:n,onChange:a,ItemPreview:l=null,ItemPicker:i=null,updateOnBlur:s=!1,sortable:c=!0,editableName:u=!0,showReset:d=!0}=e,[p,m]=(0,o.useState)(!1),[h,v]=(0,o.useState)(t.name);(0,o.useEffect)((()=>{t.name!==h&&v(t.name)}),[t.name]);const b=g()("block-editor-panel-color-gradient-settings__dropdown",e.className);return(0,o.createElement)(r.__experimentalHStack,{justify:"space-between",className:"stk-global-settings-color-picker__color-indicator-wrapper"},(0,o.createElement)(r.Dropdown,{popoverProps:Mi,focusOnMount:!p&&"firstElement",renderToggle:n=>{let{onToggle:i,isOpen:d}=n;return(0,o.createElement)(M,{className:b,onClick:()=>{e.onItemClick?e.onItemClick(t):p||i()},isPressed:d},(0,o.createElement)(r.__experimentalHStack,{justify:"flex-start"},(0,o.createElement)(l,{item:t}),u?(0,o.createElement)("input",{className:"components-input-control__input",value:p?h:t.name,onChange:e=>{v(e.target.value),s||a({...t,name:e.target.value})},onFocus:()=>m(!0),onBlur:e=>{var n;s&&a({...t,name:h}),setTimeout((()=>{m(!1)}),100),!d||null!==(n=e.relatedTarget)&&void 0!==n&&n.closest(".components-popover")||i()},onClick:()=>{d||i()},onKeyDown:e=>{13===e.keyCode&&e.target.blur()}}):(0,o.createElement)("p",{className:"stk-sortable-picker__item-name"},t.name),c&&(0,o.createElement)(Ni,null)))},renderContent:()=>(0,o.createElement)(i,{item:t,onChange:a})}),c&&(0,o.createElement)(M,{"aria-label":"Delete",className:"stk-global-settings-color-picker__delete-button",icon:"trash",isSmall:!0,isTertiary:!0,onClick:n}),!c&&(0,o.createElement)(ft,{showReset:d,onChange:n}))},Oi=e=>{const t=(0,o.useRef)(0),[n,a]=(0,o.useState)(e),l=(0,o.useCallback)((e=>{cancelAnimationFrame(t.current),t.current=requestAnimationFrame((()=>{a(e)}))}),[]);return(0,o.useEffect)((()=>()=>{cancelAnimationFrame(t.current)}),[]),[n,l]},Bi=(e,t)=>{const n=(0,o.useRef)(0),[a,l]=(0,o.useState)("");return(0,o.useEffect)((()=>(cancelAnimationFrame(n.current),n.current=requestAnimationFrame((()=>{l(e)})),()=>cancelAnimationFrame(n.current))),t),a},Pi=e=>Object.values(e).some((e=>Object.values(e).some((e=>""!==e)))),Di=()=>{const{getScheme:e,getColorGroups:t,allColorSchemes:n,COLOR_SCHEME_OPTIONS:o,baseColorScheme:a,backgroundModeColorScheme:l,containerModeColorScheme:r}=(0,ne.useSelect)((e=>{const{colorSchemes:t,hideColorSchemeColors:n,baseColorScheme:o,backgroundModeColorScheme:a,containerModeColorScheme:l}=e("stackable/global-color-schemes").getSettings(),r=(0,m.applyFilters)("stackable.global-settings.global-color-schemes.custom-color-schemes",t,!0),i=[{label:(0,d.__)("Scheme unavailable",s.i18n),value:"scheme-unavailable",hidden:!0,disabled:!0},...null==r?void 0:r.map((e=>({label:e.name,value:e.key,disabled:!Pi(e.colorScheme)})))],c=function(e){var t;let{mode:n="",returnFallback:o=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const a="background"===n?"scheme-default-2":"scheme-default-1";return(null===(t=i.find((t=>t.value===e)))||void 0===t?void 0:t.value)||(o?a:"scheme-unavailable")},u=e=>e.replace(/([a-z])([A-Z])/g,"$1 $2").replace(/^([a-z])/,(e=>e.toUpperCase())),p=(e,t)=>"--stk-"+t+"-"+(0,v.kebabCase)(e);return{getScheme:c,getColorGroups:()=>{if(n)return{colorSchemeColors:[],colorSchemeGradients:[]};const e=[],t=[];return r.forEach((n=>{if(n.hideInPicker)return;const o=[],a=[];Object.entries(n.colorScheme).forEach((e=>{let[t,l]=e;if(null==l||!l.desktop)return o;null!=l&&l.desktop.startsWith("linear-")||null!=l&&l.desktop.startsWith("radial-")?a.push({gradient:null==l?void 0:l.desktop,name:u(t),slug:p(t,n.key)}):o.push({color:null==l?void 0:l.desktop,name:u(t),slug:p(t,n.key)})})),0!==o.length&&e.push({name:n.name,id:n.key,colors:o}),0!==a.length&&t.push({name:n.name,id:n.key,gradients:a})})),{colorSchemeColors:e,colorSchemeGradients:t}},allColorSchemes:r,COLOR_SCHEME_OPTIONS:i,baseColorScheme:c(o),backgroundModeColorScheme:c(a,"background"),containerModeColorScheme:c(l)}}),[]);return{getScheme:e,getColorGroups:t,allColorSchemes:n,COLOR_SCHEME_OPTIONS:o,baseColorScheme:a,backgroundModeColorScheme:l,containerModeColorScheme:r}},{clearTimeout:Li,setTimeout:Ri}=window,Ai=250;function Hi(e){let{ref:t,isFocused:n,debounceTimeout:a=Ai,onChange:l=v.noop}=e;const[r,i]=(0,o.useState)(!1),s=(0,o.useRef)(),c=e=>{i(e),l(e)},u=()=>{const e=s.current;e&&Li&&Li(e)};return(0,o.useEffect)((()=>()=>u()),[]),{showMovers:r,debouncedShowMovers:e=>{u(),r||c(!0)},debouncedHideMovers:e=>{u(),s.current=Ri((()=>{(()=>{const e=(null==t?void 0:t.current)&&t.current.matches(":hover");return!n&&!e})()&&c(!1)}),a)}}}(0,Ye.createHigherOrderComponent)((e=>t=>{const n=(0,o.useRef)(),{showMovers:a,gestures:l}=function(e){let{ref:t,debounceTimeout:n=Ai,onChange:a=v.noop}=e;const[l,r]=(0,o.useState)(!1),{showMovers:i,debouncedShowMovers:s,debouncedHideMovers:c}=Hi({ref:t,debounceTimeout:n,isFocused:l,onChange:a}),u=(0,o.useRef)(!1),d=()=>(null==t?void 0:t.current)&&t.current.contains(document.activeElement);return(0,o.useEffect)((()=>{const e=t.current,n=()=>{d()&&(r(!0),s())},o=()=>{d()||(r(!1),c())};return e&&!u.current&&(e.addEventListener("focus",n,!0),e.addEventListener("blur",o,!0),u.current=!0),()=>{e&&(e.removeEventListener("focus",n),e.removeEventListener("blur",o))}}),[t,u,r,s,c]),{showMovers:i,gestures:{onMouseMove:s,onMouseLeave:c}}}({ref:n});return(0,o.createElement)("div",c({},l,{ref:n}),(0,o.createElement)(e,c({},t,{isHovered:a})))}),"withIsHovered"),(0,Ye.createHigherOrderComponent)((e=>t=>(0,o.createElement)(fe,t,(0,o.createElement)(e,t))),"withBlockAttributeContext");const Fi={};(0,Ye.createHigherOrderComponent)((e=>t=>{const{wrapperProps:n=Fi}=t,a=zi(t),[l,r]=Ee();return(0,o.createElement)(fr,c({align:t.attributes.align,className:t.attributes.className,blockHoverClass:r},n),a&&(0,o.createElement)(e,c({},t,{blockState:l,blockHoverClass:r})))}),"withBlockWrapper");let Vi=!0,ji=null;const zi=e=>{const{clientId:t,isSelected:n}=e,{rootBlockClientId:a}=(0,ne.useSelect)((e=>{const{getBlockRootClientId:n}=e("core/block-editor");return{rootBlockClientId:n(t)}}),[t]);n&&(ji=a);const l=a===t;Vi&&setTimeout((()=>{Vi=!1}),1e3);const r=!l||ji===t||Vi,[i,s]=(0,o.useState)(r);return(0,o.useEffect)((()=>{if(!i){const e=setTimeout((()=>{s(!0)}),300);return()=>clearTimeout(e)}}),[i]),i},{clearTimeout:$i,setTimeout:Ui}=window;const qi={};(0,Ye.createHigherOrderComponent)((e=>t=>{const{wrapperProps:n=qi}=t,a=(0,o.useRef)(),{showMovers:l,gestures:r}=function(e){let{ref:t}=e;const[n,a]=(0,o.useState)(!1),{showMovers:l,debouncedShowMovers:r,debouncedHideMovers:i}=function(e){let{ref:t,isFocused:n}=e;const[a,l]=(0,o.useState)(!1),r=(0,o.useRef)(),i=(0,o.useCallback)((()=>{r.current&&$i&&$i(r.current),l(!0)}),[]),s=(0,o.useCallback)((()=>{r.current&&$i&&$i(r.current),r.current=Ui((()=>{const e=(null==t?void 0:t.current)&&t.current.matches(":hover");n||e||l(!1)}),100)}),[n]);return(0,o.useEffect)((()=>()=>{l(!1),r.current&&$i&&$i(r.current)}),[]),{showMovers:a,debouncedShowMovers:i,debouncedHideMovers:s}}({ref:t,isFocused:n}),s=(0,o.useRef)(!1),c=()=>(null==t?void 0:t.current)&&t.current.contains(document.activeElement);return(0,o.useEffect)((()=>{const e=t.current,n=()=>{c()&&(a(!0),r())},o=()=>{c()||(a(!1),i())};return e&&!s.current&&(e.addEventListener("focus",n,!0),e.addEventListener("blur",o,!0),s.current=!0),()=>{e&&(e.removeEventListener("focus",n),e.removeEventListener("blur",o))}}),[t,s,a,r,i]),{showMovers:l,gestures:{onMouseMove:r,onMouseLeave:i}}}({ref:a}),[i,s]=Ee(),u=zi(t);return(0,o.createElement)(fr,c({align:t.attributes.align,className:t.attributes.className,blockHoverClass:s,hoverRef:a},u?r:{},n),u&&(0,o.createElement)(e,c({},t,{isHovered:l,blockState:i,blockHoverClass:s})))}),"withBlockWrapperIsHovered");const Gi={},Wi=e=>{const t=(0,o.useContext)(Ct),n=(0,ne.useSelect)((e=>{var t;return null===(t=e("core/editor"))||void 0===t?void 0:t.getCurrentPostId()})),[a,l]=(0,o.useState)(0);return(0,o.useEffect)((()=>{if(((e,t)=>(null==e?void 0:e.postId)&&t&&(null==e?void 0:e.postId)!==t)(t,n)&&e){const n=Gi[e]||[];n.includes(null==t?void 0:t.postId)||n.push(null==t?void 0:t.postId),Gi[e]=n,l(n.findIndex((e=>e===(null==t?void 0:t.postId)))+1)}}),[null==t?void 0:t.id,n,e]),a},Ki=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Object.keys(e).reduce(((n,o)=>({...n,[o]:Ji(e[o],t)})),{})},Ji=function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return""!==e&&void 0!==e&&t&&!String(e).match(/!important/i)?`${e} !important`:e},Yi=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return function(o){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n;const r=null!==t?t:e=>(0,v.lowerFirst)(e),i=void 0===e[r(o)]?"":e[r(o)];return""!==i?a?(0,d.sprintf)(a.replace(/%([sd])%/,"%$1%%"),i):i:l}},Xi=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{min:n=Number.NEGATIVE_INFINITY,max:o=Number.POSITIVE_INFINITY}=t;if(""!==e){const t=(0,v.clamp)(e,parseFloat(n),parseFloat(o));if(!isNaN(t))return parseFloat(t)!==parseFloat(e)?t:void 0}},Qi=e=>{const t=document.createElement("div");if(t.classList.add("editor-styles-wrapper"),t.classList.add("ugb-default-font-size"),t.innerHTML=`<div class="wp-block">${e.map((e=>e.startsWith(".")?`<p class="${e.substring(1)}"></p>`:`<${e}></${e}>`)).join()}</div>`,!document||!document.body)return{};document.body.appendChild(t);const n={};return e.forEach((e=>{const o=window.getComputedStyle(t.querySelector(e)).getPropertyValue("font-size");n[e]=Math.round(parseFloat(o))})),document.body.removeChild(t),n};let Zi={};const es=()=>{Zi={...Qi(["h1","h2","h3","h4","h5","h6","p",".stk-subtitle",".stk-button__inner-text"])}};(0,m.addFilter)("stackable.global-settings.typography.editor-styles","stackable/default-font-sizes",((e,t,n,o)=>(e[`.ugb-default-font-size .wp-block ${t}`]=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"%s",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"desktop",n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=Yi(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},(t=>(0,v.camelCase)((0,d.sprintf)(e,t))),""),{importantSize:a=!1,important:l=!0,inherit:r=!0,inheritMax:i=50,inheritMin:s}=n;let c={};const u=o("FontSize"),p=o("TabletFontSize"),m=o("MobileFontSize");if("tablet"!==t&&"mobile"!==t)c={fontFamily:""!==o("FontFamily")?q(o("FontFamily")):void 0,fontSize:""!==u?Ji(`${u}${o("FontSizeUnit")||"px"}`,a):void 0,fontWeight:""!==o("FontWeight")?o("FontWeight"):void 0,textTransform:""!==o("TextTransform")?o("TextTransform"):void 0,letterSpacing:""!==o("LetterSpacing")?`${o("LetterSpacing")}px`:void 0,lineHeight:""!==o("LineHeight")?`${o("LineHeight")}${o("LineHeightUnit")||"em"}`:void 0};else if("tablet"===t){if(c={lineHeight:""!==o("TabletLineHeight")?`${o("TabletLineHeight")}${o("TabletLineHeightUnit")||"em"}`:void 0,letterSpacing:""!==o("TabletLetterSpacing")?`${o("TabletLetterSpacing")}px`:void 0},r){const e=Xi(u,{min:s,max:i});e&&(c.fontSize=`${e}${o("FontSizeUnit")||"px"}`)}p&&(c.fontSize=o("TabletFontSize",`%s${o("TabletFontSizeUnit")||"px"}`))}else{if(c={lineHeight:""!==o("MobileLineHeight")?`${o("MobileLineHeight")}${o("MobileLineHeightUnit")||"em"}`:void 0,letterSpacing:""!==o("MobileLetterSpacing")?`${o("MobileLetterSpacing")}px`:void 0},r){const e=Xi(u,{min:s,max:i});e&&(c.fontSize=`${e}${o("FontSizeUnit")||"px"}`);const t=Xi(p,{min:s,max:i});t?c.fontSize=`${t}${o("TabletFontSizeUnit")||"px"}`:(e||p)&&(c.fontSize=void 0)}m&&(c.fontSize=o("MobileFontSize",`%s${o("MobileFontSizeUnit")||"px"}`))}return l?Ki(c):c}("%s","desktop",o,{important:!0}),Zi={},e)));l((()=>{es()})),window._stackableCachedImageData={},(0,d.__)("Facebook",s.i18n),(0,d.__)("Twitter",s.i18n),(0,d.__)("Instagram",s.i18n),(0,d.__)("Pinterest",s.i18n),(0,d.__)("LinkedIn",s.i18n),(0,d.__)("YouTube",s.i18n),(0,d.__)("Email",s.i18n);const ts=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const[n,a]=(0,o.useState)((()=>{try{const n=window.localStorage.getItem(e);return n?JSON.parse(n):t}catch(e){return t}})),l=t=>{try{const o=t instanceof Function?t(n):t;a(o),window.localStorage.setItem(e,JSON.stringify(o))}catch(e){}};return[n,l]},ns=e=>{const t=document.createElement("div");return t.innerHTML=e,t.firstElementChild},os=(s.iconsFaKit?s.iconsFaProKitVersion:s.iconsFaFreeKitVersion||"6.5.1")||"5.15.4",as={fas:"solid",far:"regular",fal:"light",fat:"thin",fad:"duotone",fab:"brands",fass:"sharp-solid",fasr:"sharp-regular",fasl:"sharp-light"},ls=(e,t)=>window.StkFontAwesome&&window.StkFontAwesome[`${e}-${t}`]?window.StkFontAwesome[`${e}-${t}`]:"",rs=(e,t)=>{const n=s.iconsFaKit?s.iconsFaKit:"5.15.4"===s.iconsFaFreeKitVersion?"d2a8ea0b89":"8f4ebede24",o=(e=>as[e]||"solid")(e);let a;return a=s.iconsFaKit?`https://ka-p.fontawesome.com/releases/v${os}/svgs/${o}/${t}.svg?token=${n}`:`https://ka-f.fontawesome.com/releases/v${os}/svgs/${o}/${t}.svg?token=${n}`,new Promise((async(n,o)=>{const l=await fetch(a).then((e=>e.text())).catch((()=>o(!1)));window.StkFontAwesome||(window.StkFontAwesome={}),window.StkFontAwesome[`${e}-${t}`]=l,n(!0)}))};n(3726);const is=e=>e?`stk-${e}`:"",ss=e=>(null==e?void 0:e.endsWith("mp4"))||(null==e?void 0:e.endsWith("webm"))||(null==e?void 0:e.endsWith("ogg")),cs=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return e;const n=e.replace(/\/\*.*?\*\//g,"").replace(/\n\s*\n/g,"").replace(/[\n\r \t]/g," ").replace(/ +/g," ").replace(/:is/g," :is").replace(/ ?([,:;{}]) ?/g,"$1").replace(/[^\}\{]+\{\}/g,"").replace(/[^\}\{]+\{\}/g,"").replace(/;}/g,"}").trim();return t?n.replace(/\s?\!important/g,"").replace(/([;\}])/g," !important$1").replace(/\} !important\}/g,"}}").trim():n},us={},ds=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"";const a=`${e}-${t}-${n}-${o}`;if(us[a])return us[a];const l=e.trim().replace(/[\n\s\t]+/g," ").replace(/:(is|where|matches)\([^\)]*\)/g,(e=>e.replace(/,/g,"|||"))).split(",").map((e=>{let a="";return a=e.includes("[data-block=")||"html"===e||"body"===e?e:e.includes("%s")?e.replaceAll("%s",n):n&&t?e.includes(n)?e:n&&!t?`.${n} ${e.trim()}`:`.${n} ${e.trim()}`.replace(new RegExp(`(.${n}) (.${t}(#|:|\\[|\\.|\\s|$))`,"g"),"$1$2").replace(/\s:(?!(is|where))/,":"):e,o?`${o} ${a}`:a})).join(", ").replace(/\|\|\|/g,", ");return us[a]=l,l},ps=e=>`${e.substring(0,7)}`,ms=Object.freeze({ENABLED:1,HIDDEN:2,DISABLED:3}),hs=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const{getBlockName:n,getSelectedBlockClientId:o,getBlockAttributes:a,hasMultiSelection:l,getMultiSelectedBlockClientIds:r}=(0,ne.select)("core/block-editor"),{updateBlockAttributes:i}=(0,ne.dispatch)("core/block-editor"),{getBlockType:s}=(0,ne.select)("core/blocks");if(!t&&l())return void r().forEach((t=>{hs(e,t)}));const c=t||o();if(!c)return;const u=a(c),d=n(c).replace(/^\w+\//g,""),p=s(n(c)).attributes,h=Object.keys(p).reduce(((e,t)=>({...e,[t]:p[t]?p[t].default:""})),{}),g=(0,m.applyFilters)(`stackable.${d}.design.filtered-block-attributes`,{...h,...e},u),b=(0,m.applyFilters)(`stackable.${d}.design.no-text-attributes`,g,u);G(b),i(c,(0,v.omit)(b,["uniqueClass"]))};class gs extends o.Component{constructor(){super(...arguments),this.toggleBlock=this.toggleBlock.bind(this),this.enableAllBlocks=this.enableAllBlocks.bind(this),this.disableAllBlocks=this.disableAllBlocks.bind(this),this.ajaxTimeout=null,this.state={disabledBlocks:this.props.disabledBlocks||[],isSaving:!1}}componentDidUpdate(e,t){this.state.disabledBlocks!==t.disabledBlocks&&(clearTimeout(this.ajaxTimeout),this.ajaxTimeout=setTimeout((()=>{(0,a.send)("stackable_update_disable_blocks_v2",{success:()=>{this.setState({isSaving:!1})},error:e=>{this.setState({isSaving:!1}),alert(e)},data:{nonce:s.v2nonce,disabledBlocks:this.state.disabledBlocks}}),this.setState({isSaving:!0})}),600))}toggleBlock(e){this.state.disabledBlocks.includes(e)?this.setState({disabledBlocks:this.state.disabledBlocks.filter((t=>t!==e))}):this.setState({disabledBlocks:[...this.state.disabledBlocks,e]})}enableAllBlocks(){this.setState({disabledBlocks:[]})}disableAllBlocks(){this.setState({disabledBlocks:Object.keys(this.props.blocks)})}render(){const{blocks:e,searchedSettings:t}=this.props;return(0,o.createElement)("div",null,(0,o.createElement)("div",{className:"s-settings-header"},this.state.isSaving&&(0,o.createElement)(r.Spinner,null),(0,o.createElement)("button",{onClick:this.enableAllBlocks,className:"button button-large button-link"},(0,d.__)("Enable All",s.i18n)),(0,o.createElement)("button",{onClick:this.disableAllBlocks,className:"button button-large button-link"},(0,d.__)("Disable All",s.i18n))),(0,o.createElement)("div",{className:"s-settings-grid",style:{rowGap:0}},Object.keys(e).map(((n,a)=>{const l=e[n];if(l.sDeprecated)return null;const r=this.state.disabledBlocks.includes(n);return(0,o.createElement)(x,{key:a,label:(0,d.__)(l.title,s.i18n),searchedSettings:t,value:!r,onChange:()=>this.toggleBlock(n),size:"small",disabled:(0,d.__)("Disabled",s.i18n),enabled:(0,d.__)("Enabled",s.i18n)})}))))}}const vs=e=>{let{searchSettings:t}=e;const[n,a]=(0,o.useState)(!1);return(0,o.useEffect)((()=>{f().then((e=>{a(!!e.stackable_optimize_script_load)}))}),[]),(0,o.createElement)(o.Fragment,null,(0,o.createElement)(x,{label:(0,d.__)("Frontend JS & CSS Files",s.i18n),searchSettings:t,value:n,onChange:e=>{new p.models.Settings({stackable_optimize_script_load:e}).save(),a(e)},disabled:(0,d.__)("Load across entire site",s.i18n),enabled:(0,d.__)("Load only in posts with Stackable blocks",s.i18n)}))},bs={"ugb/accordion":{title:(0,d._x)("Accordion","block title",s.i18n)},"ugb/text":{title:(0,d._x)("Advanced Text","block title",s.i18n)},"ugb/heading":{title:(0,d._x)("Advanced Heading","block title",s.i18n)},"ugb/blockquote":{title:(0,d._x)("Blockquote","block title",s.i18n)},"ugb/blog-posts":{title:(0,d._x)("Blog Posts","block title",s.i18n)},"ugb/button":{title:(0,d._x)("Button","block title",s.i18n)},"ugb/cta":{title:(0,d._x)("Call to Action","block title",s.i18n)},"ugb/card":{title:(0,d._x)("Card","block title",s.i18n)},"ugb/columns":{title:(0,d._x)("Columns","block title",s.i18n)},"ugb/container":{title:(0,d._x)("Container","block title",s.i18n)},"ugb/count-up":{title:(0,d._x)("Count Up","block title",s.i18n)},"ugb/divider":{title:(0,d._x)("Divider","block title",s.i18n)},"ugb/expand":{title:(0,d._x)("Expand / Show More","block title",s.i18n)},"ugb/feature-grid":{title:(0,d._x)("Feature Grid","block title",s.i18n)},"ugb/feature":{title:(0,d._x)("Feature","block title",s.i18n)},"ugb/header":{title:(0,d._x)("Header","block title",s.i18n)},"ugb/icon":{title:(0,d._x)("Icon","block title",s.i18n)},"ugb/icon-list":{title:(0,d._x)("Icon List","block title",s.i18n)},"ugb/image-box":{title:(0,d._x)("Image Box","block title",s.i18n)},"ugb/notification":{title:(0,d._x)("Notification","block title",s.i18n)},"ugb/number-box":{title:(0,d._x)("Number Box","block title",s.i18n)},"ugb/pricing-box":{title:(0,d._x)("Pricing Box","block title",s.i18n)},"ugb/separator":{title:(0,d._x)("Separator","block title",s.i18n)},"ugb/spacer":{title:(0,d._x)("Spacer","block title",s.i18n)},"ugb/team-member":{title:(0,d._x)("Team Member","block title",s.i18n)},"ugb/testimonial":{title:(0,d._x)("Testimonial","block title",s.i18n)},"ugb/video-popup":{title:(0,d._x)("Video Popup","block title",s.i18n)}},fs=(e=>{const t={};return e.keys().forEach((n=>{const o=e(n),a=o["stk-type"];a&&(t[a]||(t[a]=[]),t[a].push(o)),(o["stk-variants"]||[]).forEach((e=>{const n=e["stk-type"];n&&(t[n]||(t[n]=[]),t[n].push({...e,name:`${o.name}|${e.name}`}))}))})),Object.keys(t).forEach((e=>{t[e]=(0,v.sortBy)(t[e],"name")})),t})(n(9737)),ks=()=>(0,m.applyFilters)("stackable.settings.blocks",fs),_s=[{id:"essential",label:(0,d.__)("Essential Blocks",s.i18n),Icon:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",stroke:"#47a0dd",strokeWidth:2.5,strokeLinejoin:"round"},e),(0,o.createElement)("path",{d:"M25.333 4H6.667A2.667 2.667 0 004 6.667v18.667A2.667 2.667 0 006.667 28h18.667A2.667 2.667 0 0028 25.333V6.667A2.667 2.667 0 0025.333 4z"}),(0,o.createElement)("path",{d:"M11.333 13.333a2 2 0 100-4 2 2 0 100 4z"}),(0,o.createElement)("path",{d:"M28 20l-6.667-6.667L6.667 28",strokeLinecap:"round"}))},description:(0,d.__)("All the necessary building blocks you need to design anything.",s.i18n)},{id:"special",label:(0,d.__)("Special Blocks",s.i18n),Icon:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",stroke:"#e2735d",strokeWidth:2.5,strokeLinejoin:"round"},e),(0,o.createElement)("path",{d:"M30.667 9.333L21.333 16l9.333 6.667V9.333z"}),(0,o.createElement)("path",{d:"M18.667 6.667H4a2.667 2.667 0 00-2.667 2.667v13.333A2.667 2.667 0 004 25.334h14.667a2.667 2.667 0 002.667-2.667V9.333a2.667 2.667 0 00-2.667-2.667z"}))},description:(0,d.__)("Blocks with special functionality that will allow you to create distinctive designs.",s.i18n)},{id:"section",label:(0,d.__)("Section Blocks",s.i18n),Icon:function(e){return(0,o.createElement)("svg",c({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32",fill:"none",stroke:"#eabd59",strokeWidth:2.5,strokeLinejoin:"round"},e),(0,o.createElement)("path",{d:"M25.333 4H6.667A2.667 2.667 0 004 6.667v18.667A2.667 2.667 0 006.667 28h18.667A2.667 2.667 0 0028 25.333V6.667A2.667 2.667 0 0025.333 4z"}),(0,o.createElement)("path",{d:"M4 12h24M12 28V12",strokeLinecap:"round"}))},description:(0,d.__)("Use these blocks act as templates to help you build sections effortlessly.",s.i18n)}],ys=[{id:"editor-settings",label:(0,d.__)("Editor Settings",s.i18n),groups:[{id:"blocks",children:[(0,d.__)("Nested Block Width",s.i18n),(0,d.__)("Nested Wide Block Width",s.i18n),(0,d.__)("Stackable Text as Default Block",s.i18n)]},{id:"editor",children:[(0,d.__)("Design Library",s.i18n),(0,d.__)("Stackable Settings",s.i18n),(0,d.__)("Block Linking (Beta)",s.i18n)]},{id:"toolbar",children:[(0,d.__)("Toolbar Text Highlight",s.i18n),(0,d.__)("Toolbar Dynamic Content",s.i18n),(0,d.__)("Copy & Paste Styles",s.i18n),(0,d.__)("Reset Layout",s.i18n),(0,d.__)("Save as Default Block",s.i18n)]},{id:"inspector",children:[(0,d.__)("Don't show help video tooltips",s.i18n),(0,d.__)("Auto-Collapse Panels",s.i18n)]}]},{id:"responsiveness",label:(0,d.__)("Responsiveness",s.i18n),groups:[{id:"dynamic-breakpoints",children:[(0,d.__)("Tablet Breakpoint",s.i18n),(0,d.__)("Mobile Breakpoint",s.i18n)]}]},{id:"blocks",label:(0,d.__)("Blocks",s.i18n),groups:_s.map((e=>{let{id:t}=e;return{id:t,children:ks()[t].map((e=>e.title))}}))},{id:"optimizations",label:(0,d.__)("Optimization",s.i18n),groups:[{id:"optimizations",children:[(0,d.__)("Optimize Inline CSS",s.i18n),(0,d.__)("Lazy Load Images within Carousels",s.i18n)]}]},{id:"global-settings",label:(0,d.__)("Global Settings",s.i18n),groups:[{id:"global-settings",children:[(0,d.__)("Force Typography Styles",s.i18n)]}]},{id:"role-manager",label:(0,d.__)("Role Manager",s.i18n),groups:[{id:"role-manager",children:[(0,d.__)("Role Manager",s.i18n),(0,d.__)("Administrator",s.i18n),(0,d.__)("Editor",s.i18n),(0,d.__)("Author",s.i18n),(0,d.__)("Contributor",s.i18n),(0,d.__)("Subscriber",s.i18n)]}]},{id:"custom-fields-settings",label:(0,d.__)("Custom Fields",s.i18n),groups:[{id:"custom-fields-settings",children:[(0,d.__)("Custom Fields",s.i18n),(0,d.__)("Administrator",s.i18n),(0,d.__)("Editor",s.i18n),(0,d.__)("Author",s.i18n),(0,d.__)("Contributor",s.i18n),(0,d.__)("Subscriber",s.i18n)]}]},{id:"integrations",label:(0,d.__)("Integration",s.i18n),groups:[{id:"integrations",children:[(0,d.__)("Google Maps API Key",s.i18n),(0,d.__)("FontAwesome Pro Kit",s.i18n),(0,d.__)("FontAwesome Icon Library Version",s.i18n)]}]},{id:"other-settings",label:(0,d.__)("Miscellaneous ",s.i18n),groups:[{id:"miscellaneous",children:[(0,d.__)("Show Go premium notices",s.i18n),(0,d.__)("Generate Global Colors for native blocks",s.i18n)]},{id:"migration-settings",children:[(0,d.__)("Load version 2 blocks in the editor",s.i18n),(0,d.__)("Load version 2 blocks in the editor only when the page was using version 2 blocks",s.i18n),(0,d.__)("Load version 2 frontend block stylesheet and scripts for backward compatibility",s.i18n)]}]},{id:"v2-settings",label:(0,d.__)("V2 Settings",s.i18n),groups:[{id:"optimizations",children:[(0,d.__)("Frontend JS & CSS Files")]},{id:"blocks",children:Object.values(bs).map((e=>e.title))}]}],ws=ks(),Es=_s.reduce(((e,t)=>{let{id:n}=t;return ws[n].forEach((t=>{var n;e[t.name]=null!==(n=t["stk-required-blocks"])&&void 0!==n?n:[]})),e}),{}),Ss=()=>{const e=ks();return(0,o.createElement)(o.Fragment,null,_s.map((t=>{let{id:n,label:a}=t;return(0,o.createElement)("div",{className:"s-getting-started-blocks-wrapper",key:n},(0,o.createElement)("h3",null,a),(0,o.createElement)("div",{className:"s-getting-started-blocks"},e[n].map(((e,t)=>(0,o.createElement)("div",{key:t,className:"s-box"},(0,o.createElement)("h4",null,(0,d.__)(e.title,s.i18n)),(0,o.createElement)("p",null,(0,d.__)(e.description,s.i18n)),e["stk-demo"]&&(0,o.createElement)("a",{href:e["stk-demo"],target:"_example"},(0,d.__)("See example",s.i18n)))))))})))},xs=()=>{const[e,t]=(0,o.useState)(null);return(0,o.useEffect)((()=>{f().catch((e=>{t(e)}))}),[]),e?(0,o.createElement)("div",{className:"notice notice-error"},(0,o.createElement)("p",null,(0,d.__)("Error getting Stackable settings. We got the following error. Please contact your administrator.",s.i18n)),e.responseJSON&&(0,o.createElement)("p",null,(0,o.createElement)("strong",null,e.responseJSON.data.status," (",e.responseJSON.code,").")," ",e.responseJSON.message," ")):null},Cs=e=>{let{blockName:t,blockList:n,isDisabled:a,onConfirm:l,onCancel:i}=e;const c=ks(),u=e=>{for(const t in c)for(const n of c[t])if(n.name===e)return n.title;return e},p=u(t);return(0,o.createElement)(r.Modal,{className:"s-confirm-modal",size:"medium",title:a?(0,d.sprintf)((0,d.__)("Disable %s block?",s.i18n),p):(0,d.sprintf)((0,d.__)("Enable %s block?",s.i18n),p),onRequestClose:i},a?(0,o.createElement)("p",null,(0,d.__)("Disabling this block will also disable these blocks that require this block to function:",s.i18n)):(0,o.createElement)("p",null,(0,d.__)("Enabling this block will also enable these blocks that are needed for this block to function:",s.i18n)),(0,o.createElement)("ul",null,n.map(((e,t)=>(0,o.createElement)("li",{key:t},u(e))))),(0,o.createElement)(r.Flex,{justify:"flex-end",expanded:!1},(0,o.createElement)(r.FlexItem,null,(0,o.createElement)(r.Button,{variant:"secondary",onClick:i},(0,d.__)("Cancel",s.i18n))),(0,o.createElement)(r.FlexItem,null,(0,o.createElement)(r.Button,{variant:"primary",onClick:l},a?(0,d.__)("Disable",s.i18n):(0,d.__)("Enable",s.i18n)))))},Ts=e=>{let{currentTab:t,handleTabChange:n,hasUnsavedChanges:a,handleSettingsSave:l,currentSearch:i,filteredSearchTree:c,isSaving:u,isRecentlySaved:p,hasV2Tab:m}=e;const h=g()(["s-save-changes",{"s-button-has-unsaved-changes":a&&!p}]);return(0,o.createElement)(o.Fragment,null,(0,o.createElement)("nav",{className:"s-sidenav"},(0,o.createElement)("div",null,c.map((e=>{let{id:a,label:l,groups:r}=e;const s=i&&r.some((e=>e.children.length>0)),c=g()(["s-sidenav-item",{"s-sidenav-item-highlight":s},{"s-active":t===a}]);return"v2-settings"!==a||m?(0,o.createElement)("button",{key:a,className:c,onClick:()=>n(a),onKeyDown:()=>n(a),role:"tab",tabIndex:0},l):null})),(0,o.createElement)("div",{className:"s-save-changes-wrapper"},"v2-settings"!==t&&(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"s-save-changes-inner-wrapper"},a&&(0,o.createElement)("span",{className:"s-save-changes-note"},(0,d.__)("There are unsaved changes",s.i18n)),(0,o.createElement)("button",{className:h,onClick:l,disabled:p},u?(0,o.createElement)(r.Spinner,null):p?(0,d.__)("Saved Succesfully!",s.i18n):(0,d.__)("Save Changes",s.i18n))))))))},Ms=e=>{let{currentSearch:t,handleSearchChange:n}=e;return(0,o.createElement)("div",{className:"s-search-setting"},(0,o.createElement)("input",{className:"s-search-setting__input",type:"search",placeholder:(0,d.__)("Search settings",s.i18n),value:t,onChange:e=>{n(e.target.value.toLowerCase())}}))},Ns=()=>{const[e,t]=(0,o.useState)({}),[n,a]=(0,o.useState)({}),[l,r]=(0,o.useState)("editor-settings"),[i,s]=(0,o.useState)(""),[c,u]=(0,o.useState)(!1),[d,m]=(0,o.useState)(!1),[h,g]=(0,o.useState)(!1),v=e=>"1"===e.stackable_v2_frontend_compatibility||"1"===e.stackable_v2_editor_compatibility||"1"===e.stackable_v2_editor_compatibility_usage,b=(0,o.useCallback)((e=>{t((t=>({...t,...e}))),a((t=>({...t,...e})))}),[]),k=(0,o.useCallback)((()=>{0!==Object.keys(n).length&&(u(!0),m(!0),new p.models.Settings(n).save().then((()=>{setTimeout((()=>{u(!1)}),500),setTimeout((()=>{m(!1)}),1500)})),a({}))}),[n,e]);(0,o.useEffect)((()=>{f().then((e=>{t(e),g(v(e))}))}),[]),(0,o.useEffect)((()=>{v(e)||g(!1)}),[e]);const _=(0,o.useMemo)((()=>Object.keys(n).length>0),[n]);(0,o.useEffect)((()=>{const e=e=>{_&&(e.preventDefault(),e.returnValue=!0)};return window.addEventListener("beforeunload",e),()=>{window.removeEventListener("beforeunload",e)}}),[_]);const y=(0,o.useMemo)((()=>{if(!i)return ys;const e=i.toLowerCase();return ys.map((t=>{const n=t.groups.map((t=>{const n=t.children.filter((t=>t.toLowerCase().includes(e.toLowerCase())));return{...t,children:n}}));return{...t,groups:n}}))}),[i]),w={settings:e,handleSettingsChange:b,filteredSearchTree:y,currentTab:l};return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(Ts,{currentTab:l,handleTabChange:r,hasUnsavedChanges:_,handleSettingsSave:k,currentSearch:i,filteredSearchTree:y,isSaving:c,isRecentlySaved:d,hasV2Tab:h}),(0,o.createElement)("article",{className:"s-box",id:l},(0,o.createElement)(Ms,{currentSearch:i,handleSearchChange:s}),"editor-settings"===l&&(0,o.createElement)(Is,w),"responsiveness"===l&&(0,o.createElement)(Os,w),"blocks"===l&&(0,o.createElement)(Bs,w),"optimizations"===l&&(0,o.createElement)(Ps,w),"global-settings"===l&&(0,o.createElement)(Ds,w),"role-manager"===l&&(0,o.createElement)(Ls,w),"custom-fields-settings"===l&&(0,o.createElement)(Rs,w),"integrations"===l&&(0,o.createElement)(As,w),"other-settings"===l&&(0,o.createElement)(Hs,w),(0,o.createElement)(Fs,w)))},Is=e=>{const{settings:t,handleSettingsChange:n,filteredSearchTree:a}=e,l=a.find((e=>"editor-settings"===e.id)).groups,r=l.find((e=>"blocks"===e.id)),i=l.find((e=>"editor"===e.id)),c=l.find((e=>"toolbar"===e.id)),u=l.find((e=>"inspector"===e.id)),p=l.reduce(((e,t)=>e+t.children.length),0);return(0,o.createElement)("div",{className:"s-editor-settings"},p<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,r.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Block Widths",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Adjust the width of Stackable blocks here.",s.i18n)),(0,o.createElement)(T,{label:(0,d.__)("Nested Block Width",s.i18n),searchedSettings:r.children,value:t.stackable_block_default_width,type:"text",onChange:e=>{n({stackable_block_default_width:e})},help:(0,d.__)("The width used when a Columns block has its Content Width set to center. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula.",s.i18n)}),(0,o.createElement)(T,{label:(0,d.__)("Nested Wide Block Width",s.i18n),searchedSettings:r.children,value:t.stackable_block_wide_width,type:"text",onChange:e=>{n({stackable_block_wide_width:e})},help:(0,d.__)("The width used when a Columns block has its Content Width set to wide. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula.",s.i18n)})),i.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Editor",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("You can customize some of the features and behavior of Stackable in the editor here.")," "),(0,o.createElement)(x,{label:(0,d.__)("Stackable Text as Default Block",s.i18n),searchedSettings:r.children,value:t.stackable_enable_text_default_block,onChange:e=>{n({stackable_enable_text_default_block:e})},help:(0,d.__)("If enabled, Stackable Text blocks will be added by default instead of the native Paragraph Block.",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Design Library",s.i18n),searchedSettings:i.children,value:t.stackable_enable_design_library,onChange:e=>{n({stackable_enable_design_library:e})},help:(0,d.__)("Adds a button on the top of the editor which gives access to a collection of pre-made block designs. Note: You can still access the Design Library by adding the Design Library block.",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Stackable Settings",s.i18n),searchedSettings:i.children,value:t.stackable_enable_global_settings,onChange:e=>{n({stackable_enable_global_settings:e})},help:(0,d.__)("Adds a button on the top of the editor which gives access to Stackable settings. Note: You won't be able to access Stackable settings when this is disabled.",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Block Linking (Beta)",s.i18n),searchedSettings:i.children,value:t.stackable_enable_block_linking,onChange:e=>{n({stackable_enable_block_linking:e})},help:(0,o.createElement)(o.Fragment,null,(0,d.__)("Gives you the ability to link columns. Any changes you make on one column will automatically get applied on the other columns.",s.i18n)," ",(0,o.createElement)("a",{target:"_docs",href:"https://docs.wpstackable.com/article/452-how-to-use-block-linking"},(0,d.__)("Learn more",s.i18n)))})),c.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Toolbar",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("You can disable some toolbar features here.",s.i18n)," "),(0,o.createElement)(x,{label:(0,d.__)("Toolbar Text Highlight",s.i18n),searchedSettings:c.children,value:t.stackable_enable_text_highlight,onChange:e=>{n({stackable_enable_text_highlight:e})},help:(0,d.__)("Adds a toolbar button for highlighting text",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Toolbar Dynamic Content",s.i18n),searchedSettings:c.children,value:t.stackable_enable_dynamic_content,onChange:e=>{n({stackable_enable_dynamic_content:e})},help:(0,d.__)("Adds a toolbar button for inserting and modifying dynamic content",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Copy & Paste Styles",s.i18n),searchedSettings:c.children,value:t.stackable_enable_copy_paste_styles,onChange:e=>{n({stackable_enable_copy_paste_styles:e})},help:(0,d.__)("Adds a toolbar button for advanced copying and pasting block styles",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Reset Layout",s.i18n),searchedSettings:c.children,value:t.stackable_enable_reset_layout,onChange:e=>{n({stackable_enable_reset_layout:e})},help:(0,d.__)("Adds a toolbar button for resetting the layout of a stackble block back to the original",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Save as Default Block",s.i18n),searchedSettings:c.children,value:t.stackable_enable_save_as_default_block,onChange:e=>{n({stackable_enable_save_as_default_block:e})},help:(0,d.__)("Adds a toolbar button for saving a block as the default block",s.i18n)})),u.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Inspector",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("You can customize some of the features and behavior of Stackable in the inspector here.")),(0,o.createElement)(x,{label:(0,d.__)("Don't show help video tooltips",s.i18n),searchedSettings:u.children,value:"1"===t.stackable_help_tooltip_disabled,onChange:e=>{n({stackable_help_tooltip_disabled:e?"1":""})},help:(0,d.__)("Disables the help video tooltips that appear in the inspector.",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Auto-Collapse Panels",s.i18n),searchedSettings:u.children,value:t.stackable_auto_collapse_panels,onChange:e=>{n({stackable_auto_collapse_panels:e})},help:(0,d.__)("Collapse other inspector panels when opening another, keeping only one open at a time.",s.i18n)}))))},Os=e=>{var t,n;const{settings:a,handleSettingsChange:l,filteredSearchTree:r}=e,i=r.find((e=>"responsiveness"===e.id)).groups,c=i.find((e=>"dynamic-breakpoints"===e.id)),u=i.reduce(((e,t)=>e+t.children.length),0);return(0,o.createElement)("div",{className:"s-responsiveness"},u<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,c.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Dynamic Breakpoints",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Blocks can be styles differently for tablet and mobile screens, and some styles adjust to make them fit better in smaller screens. You can change the widths when tablet and mobile views are triggered. ",s.i18n),(0,o.createElement)("a",{href:"https://docs.wpstackable.com/article/464-how-to-use-dynamic-breakpoints?utm_source=wp-settings-global-settings&utm_campaign=learnmore&utm_medium=wp-dashboard",target:"_docs"},(0,d.__)("Learn more",s.i18n))),(0,o.createElement)(T,{label:(0,d.__)("Tablet Breakpoint",s.i18n),searchedSettings:c.children,type:"number",value:(null===(t=a.stackable_dynamic_breakpoints)||void 0===t?void 0:t.tablet)||"",onChange:e=>{var t;l({stackable_dynamic_breakpoints:{tablet:e,mobile:(null===(t=a.stackable_dynamic_breakpoints)||void 0===t?void 0:t.mobile)||""}})},placeholder:s.defaultBreakpoints.tablet||"1024"}," px"),(0,o.createElement)(T,{label:(0,d.__)("Mobile Breakpoint",s.i18n),searchedSettings:c.children,type:"number",value:(null===(n=a.stackable_dynamic_breakpoints)||void 0===n?void 0:n.mobile)||"",onChange:e=>{var t;l({stackable_dynamic_breakpoints:{tablet:(null===(t=a.stackable_dynamic_breakpoints)||void 0===t?void 0:t.tablet)||"",mobile:e}})},placeholder:s.defaultBreakpoints.mobile||"768"}," px"))))},Bs=e=>{var t;const{settings:n,handleSettingsChange:a,filteredSearchTree:l}=e,i=Object.freeze({enabled:ms.ENABLED,hidden:ms.HIDDEN,disabled:ms.DISABLED}),c=ks(),u=l.find((e=>"blocks"===e.id)).groups,p=u.reduce(((e,t)=>e+t.children.length),0),m=null!==(t=n.stackable_block_states)&&void 0!==t?t:{},[h,v]=(0,o.useState)(!1),[b,f]=(0,o.useState)(!1),[k,_]=(0,o.useState)(""),[y,w]=(0,o.useState)([]),E=e=>null==e?void 0:e.map((e=>i[e.toLowerCase()]));return(0,o.createElement)(o.Fragment,null,h&&y&&(0,o.createElement)(Cs,{blockName:k,blockList:y,isDisabled:!0,onConfirm:()=>{v(!1);const e={...m,[k]:ms.DISABLED};y.forEach((t=>{e[t]=ms.DISABLED})),a({stackable_block_states:e})},onCancel:()=>{v(!1)}}),b&&y&&(0,o.createElement)(Cs,{blockName:k,blockList:y,isDisabled:!1,onConfirm:()=>{f(!1);const e={...m};delete e[k],y.forEach((t=>{delete e[t]})),a({stackable_block_states:e})},onCancel:()=>{f(!1)}}),(0,o.createElement)("div",{className:"s-blocks"},p<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Blocks",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Here you can enable, hide and disable Stackable blocks. Hiding blocks will hide the block from the list of available blocks. Disabling blocks will prevent them from being registered at all. When using block variations or design library patterns, disabled blocks will be substituted with the relevant core blocks.",s.i18n)),(0,o.createElement)("div",{className:"s-settings-header"},(0,o.createElement)(r.Button,{variation:"secondary",onClick:()=>{const e={};_s.forEach((t=>{let{id:n}=t;c[n].forEach((t=>{const n=E(t["stk-available-states"]);n&&!n.includes(ms.ENABLED)&&m[t.name]&&(e[t.name]=m[t.name])}))})),a({stackable_block_states:e})}},(0,d.__)("Enable All",s.i18n)),(0,o.createElement)(r.Button,{variation:"secondary",onClick:()=>{const e={};_s.forEach((t=>{let{id:n}=t;c[n].forEach((t=>{const n=E(t["stk-available-states"]);!n||n.includes(ms.HIDDEN)?e[t.name]=ms.HIDDEN:m[t.name]&&(e[t.name]=m[t.name])}))})),a({stackable_block_states:e})}},(0,d.__)("Hide All",s.i18n)),(0,o.createElement)(r.Button,{variation:"secondary",onClick:()=>{const e={};_s.forEach((t=>{let{id:n}=t;c[n].forEach((t=>{const n=E(t["stk-available-states"]);!n||n.includes(ms.DISABLED)?e[t.name]=ms.DISABLED:n.includes(ms.HIDDEN)?e[t.name]=ms.HIDDEN:m[t.name]&&(e[t.name]=m[t.name])}))})),a({stackable_block_states:e})}},(0,d.__)("Disable All",s.i18n))),_s.map((e=>{let{id:t,label:n,Icon:l}=e;const r=g()(["s-box-block__title",`s-box-block__title--${t}`]),i=u.find((e=>e.id===t));return i.children.length>0&&(0,o.createElement)("div",{className:"s-box s-box-block",key:t},(0,o.createElement)("h3",{className:r},l&&(0,o.createElement)(l,{height:"20",width:"20"}),(0,o.createElement)("span",null,n)),(0,o.createElement)("div",{className:"s-settings-grid"},c[t].map(((e,t)=>{var n;const l=null!==(n=m[e.name])&&void 0!==n?n:ms.ENABLED,r=E(e["stk-available-states"]);return r&&r.length<=1?null:(0,o.createElement)(I,{key:t,className:"s-block-setting",label:(0,d.__)(e.title,s.i18n),demoLink:e["stk-demo"],searchedSettings:i.children,value:l,default:ms.ENABLED,controls:[{value:ms.ENABLED,title:(0,d.__)("Enabled",s.i18n),selectedColor:"#009733"},{value:ms.HIDDEN,title:(0,d.__)("Hidden",s.i18n)},{value:ms.DISABLED,title:(0,d.__)("Disabled",s.i18n),selectedColor:"#de0000"}],availableStates:r,onChange:t=>{((e,t)=>{const n=Number(t);let o={...m};if(_(e),n===ms.ENABLED){const t=(l=e,Es[l]||[]).filter((e=>e in m));t.length>0?(w(t),f(!0)):delete o[e]}else if(n===ms.DISABLED){const t=(e=>{const t=[];for(const n in Es)Es[n].includes(e)&&t.push(n);return t})(e).filter((e=>!(e in m)));t.length>0?(w(t),v(!0)):o={...m,[e]:n}}else o={...m,[e]:n};var l;a({stackable_block_states:o})})(e.name,t)},isSmall:!0})}))))})))))},Ps=e=>{const{settings:t,handleSettingsChange:n,filteredSearchTree:a}=e,l=a.find((e=>"optimizations"===e.id)).groups,r=l.find((e=>"optimizations"===e.id)),i=l.reduce(((e,t)=>e+t.children.length),0);return(0,o.createElement)("div",{className:"s-optimizations"},i<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,r.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Optimizations",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Here you can adjust some optimization settings that are performed by Stackable.",s.i18n)),(0,o.createElement)(x,{label:(0,d.__)("Optimize Inline CSS",s.i18n),searchedSettings:r.children,value:t.stackable_optimize_inline_css,onChange:e=>{n({stackable_optimize_inline_css:e})},help:(0,d.__)("Optimize inlined CSS styles. If this is enabled, similar selectors will be combined together, helpful if you changed Block Defaults.",s.i18n)}),(0,o.createElement)(x,{label:(0,d.__)("Lazy Load Images within Carousels",s.i18n),searchedSettings:r.children,value:t.stackable_enable_carousel_lazy_loading,onChange:e=>{n({stackable_enable_carousel_lazy_loading:e})},help:(0,d.__)("Disable this if you encounter layout or spacing issues when using images inside carousel-type blocks because of image lazy loading.",s.i18n)}))))},Ds=e=>{const t=e.filteredSearchTree.find((e=>"global-settings"===e.id)).groups,n=t.find((e=>"global-settings"===e.id)),a=t.reduce(((e,t)=>e+t.children.length),0);return(0,o.createElement)("div",{className:"s-global-settings"},a<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,n.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Global Settings",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Here you can tweak Global Settings that affect the styles across your entire site.",s.i18n)),(0,o.createElement)(x,{label:(0,d.__)("Force Typography Styles",s.i18n),searchedSettings:n.children,value:e.settings.stackable_global_force_typography,onChange:t=>{e.handleSettingsChange({stackable_global_force_typography:t})},disabled:(0,d.__)("Not forced",s.i18n),enabled:(0,d.__)("Force styles",s.i18n)}))))},Ls=e=>{const t=e.filteredSearchTree.find((e=>"role-manager"===e.id)).groups;e.roleManager=t.find((e=>"role-manager"===e.id));const n=t.reduce(((e,t)=>e+t.children.length),0),a=(0,m.applyFilters)("stackable.admin.settings.editorModeSettings",o.Fragment);return(0,o.createElement)("div",{className:"s-role-manager"},n<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,e.roleManager.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Role Manager",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Lock the Block Editor's inspector for different user roles, and give clients edit access to only images and content. Content Editing Mode affects all blocks. ",s.i18n),(0,o.createElement)("a",{target:"_docs",href:s.isPro?"https://docs.wpstackable.com/article/360-role-manager-and-content-editing-mode?utm_source=wp-settings-role-manager&utm_campaign=learnmore&utm_medium=wp-dashboard":"https://wpstackable.com/blog/introducing-role-manager-for-gutenberg/?utm_source=wp-settings-role-manager&utm_campaign=learnmore&utm_medium=wp-dashboard"},(0,d.__)("Learn more",s.i18n))),s.isPro?(0,o.createElement)(o.Suspense,{fallback:(0,o.createElement)(r.Spinner,null)},(0,o.createElement)("div",{className:"s-editing-mode-settings"},(0,o.createElement)(a,e))):(0,o.createElement)("p",{className:"s-settings-pro"},(0,d.__)("This is only available in Stackable Premium. ",s.i18n),(0,o.createElement)("a",{href:"https://wpstackable.com/premium/?utm_source=wp-settings-role-manager&utm_campaign=gopremium&utm_medium=wp-dashboard",target:"_premium"},(0,d.__)("Go Premium",s.i18n))))))},Rs=e=>{const t=e.filteredSearchTree.find((e=>"custom-fields-settings"===e.id)).groups;e.customFields=t.find((e=>"custom-fields-settings"===e.id));const n=t.reduce(((e,t)=>e+t.children.length),0),a=(0,m.applyFilters)("stackable.admin.settings.customFieldsEnableSettings",o.Fragment),l=(0,m.applyFilters)("stackable.admin.settings.customFieldsManagerSettings",o.Fragment);return(0,o.createElement)("div",{className:"s-custom-fields"},n<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,e.customFields.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("div",{className:"s-custom-fields-settings-header"},(0,o.createElement)("h2",null,(0,d.__)("Custom Fields",s.i18n)),s.isPro&&(0,o.createElement)(o.Suspense,{fallback:(0,o.createElement)(r.Spinner,null)},(0,o.createElement)("div",{className:"s-custom-fields-enable"},(0,o.createElement)(a,e)))),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Create Custom Fields that you can reference across your entire site. You can assign which roles can manage your Custom Fields. ",s.i18n),(0,o.createElement)("a",{href:"https://docs.wpstackable.com/article/463-how-to-use-stackable-custom-fields/?utm_source=wp-settings-custom-fields&utm_campaign=learnmore&utm_medium=wp-dashboard",target:"_docs"},(0,d.__)("Learn more",s.i18n))),s.isPro?(0,o.createElement)(o.Suspense,{fallback:(0,o.createElement)(r.Spinner,null)},(0,o.createElement)("div",{className:"s-custom-fields-manager"},(0,o.createElement)(l,e))):(0,o.createElement)("p",{className:"s-settings-pro"},(0,d.__)("This is only available in Stackable Premium. ",s.i18n),(0,o.createElement)("a",{href:"https://wpstackable.com/premium/?utm_source=wp-settings-custom-fields&utm_campaign=gopremium&utm_medium=wp-dashboard",target:"_premium"},(0,d.__)("Go Premium",s.i18n))))))},As=e=>{const{settings:t,handleSettingsChange:n,filteredSearchTree:a}=e,l=a.find((e=>"integrations"===e.id)).groups;e.integrations=l.find((e=>"integrations"===e.id));const i=l.reduce(((e,t)=>e+t.children.length),0),c=(0,m.applyFilters)("stackable.admin.settings.iconSettings",o.Fragment);return(0,o.createElement)("div",{className:"s-integrations"},i<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,e.integrations.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Integrations",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Here are settings for the different integrations available in Stackable.",s.i18n)),(0,o.createElement)(T,{label:(0,d.__)("Google Maps API Key",s.i18n),searchedSettings:e.integrations.children,value:t.stackable_google_maps_api_key,type:"text",onChange:e=>{n({stackable_google_maps_api_key:e})},help:(0,o.createElement)(o.Fragment,null,(0,d.__)("Adding a Google API Key enables additional features of the Stackable Map Block.",s.i18n)," ",(0,o.createElement)("a",{href:"https://docs.wpstackable.com/article/483-how-to-use-stackable-map-block#api-key",target:"_blank",rel:"noreferrer"},(0,d.__)("Learn more",s.i18n)))}),s.isPro?(0,o.createElement)(o.Suspense,{fallback:(0,o.createElement)(r.Spinner,null)},(0,o.createElement)("div",{className:"ugb-admin-setting"},(0,o.createElement)(c,e))):(0,o.createElement)(o.Fragment,null,(0,o.createElement)("div",{className:"s-settings-field ugb-admin-setting"},(0,o.createElement)("label",{className:"s-text-field",htmlFor:"s-icon-kit-field"},(0,o.createElement)("span",{className:"s-settings-field__title ugb-admin-setting__label"},(0,d.__)("FontAwesome Pro Kit",s.i18n)),(0,o.createElement)("p",{className:"s-settings-pro"},(0,d.__)("This is only available in Stackable Premium. ",s.i18n),(0,o.createElement)("a",{href:"https://wpstackable.com/premium/?utm_source=wp-settings-integrations&utm_campaign=gopremium&utm_medium=wp-dashboard",target:"_premium"},(0,d.__)("Go Premium",s.i18n)))))),(0,o.createElement)("div",{className:"s-icon-settings-fa-version"},(0,o.createElement)("div",{className:"s-icon-settings-fa-pro-version"},(0,o.createElement)("label",{className:"ugb-admin-setting__label-wrapper",htmlFor:"s-icon-settings-fa-pro-version"},(0,o.createElement)("span",{className:"ugb-admin-setting__label"}," ",(0,d.__)("FontAwesome Icon Library Version",s.i18n)),(0,o.createElement)("div",{className:"ugb-admin-setting__field"},(0,o.createElement)("p",null,(0,d.__)("You are using the version set in your Font Awesome Pro Kit.",s.i18n))))),(0,o.createElement)("div",{className:"s-icon-settings-fa-free-version"},(0,o.createElement)(E,{label:(0,d.__)("FontAwesome Icon Library Version",s.i18n),searchedSettings:e.integrations.children,value:t.stackable_icons_fa_free_version,options:[{name:"6.5.1",value:"6.5.1"},{name:"5.15.4",value:"5.15.4"}],onChange:e=>{n({stackable_icons_fa_free_version:e})}}))))))},Hs=e=>{const{settings:t,handleSettingsChange:n,filteredSearchTree:a}=e,l=a.find((e=>"other-settings"===e.id)).groups,i=l.find((e=>"miscellaneous"===e.id)),c=l.find((e=>"migration-settings"===e.id)),u=l.reduce(((e,t)=>e+t.children.length),0),p=(e,t)=>t.children.includes(e)?"":"ugb-admin-setting--not-highlight";return(0,o.createElement)("div",{className:"s-other-options-wrapper"},u<=0?(0,o.createElement)("h3",null,(0,d.__)("No matching settings",s.i18n)):(0,o.createElement)(o.Fragment,null,i.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Miscellaneous",s.i18n)),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Below are other minor settings. Some may be useful when upgrading from older versions of Stackable.",s.i18n)),s.showProNoticesOption&&(0,o.createElement)(r.CheckboxControl,{label:(0,d.__)('Show "Go premium" notices',s.i18n),className:p((0,d.__)("Show Go premium notices",s.i18n),i),checked:"1"===t.stackable_show_pro_notices,onChange:e=>{n({stackable_show_pro_notices:e?"1":""})}}),(0,o.createElement)(r.CheckboxControl,{label:(0,d.__)("Generate Global Colors for native blocks",s.i18n),className:p((0,d.__)("Generate Global Colors for native blocks",s.i18n),i),help:(0,d.__)("When enabled, extra frontend CSS is generated to support Stackable global colors used in native blocks. If you don't use Stackable global colors in native blocks, simply toggle this OFF. Please note that Stackable global colors are no longer available for native blocks. To ensure your styles always look perfect, our auto-detect feature will activate this option whenever needed.",s.i18n),checked:!!t.stackable_global_colors_native_compatibility,onChange:e=>{n({stackable_global_colors_native_compatibility:e})}})),c.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Migration Settings",s.i18n)),(0,o.createElement)("p",null,(0,d.__)("After enabling the version 2 blocks, please refresh the page to re-fetch the blocks from the server.",s.i18n)),(0,o.createElement)("p",null,(0,d.__)("Migrating from version 2 to version 3?",s.i18n)," ",(0,o.createElement)("a",{target:"_docs",href:"https://docs.wpstackable.com/article/462-migrating-from-version-2-to-version-3?utm_source=wp-settings-migrating&utm_campaign=learnmore&utm_medium=wp-dashboard"},(0,d.__)("Learn more about migration and the settings below",s.i18n))),(0,o.createElement)(r.CheckboxControl,{label:(0,d.__)("Load version 2 blocks in the editor",s.i18n),className:p((0,d.__)("Load version 2 blocks in the editor",s.i18n),c),checked:"1"===t.stackable_v2_editor_compatibility,onChange:e=>{e&&n({stackable_v2_editor_compatibility_usage:""}),n({stackable_v2_editor_compatibility:e?"1":""})}}),(0,o.createElement)(r.CheckboxControl,{label:(0,d.__)("Load version 2 blocks in the editor only when the page was using version 2 blocks",s.i18n),className:p((0,d.__)("Load version 2 blocks in the editor only when the page was using version 2 blocks",s.i18n),c),checked:"1"===t.stackable_v2_editor_compatibility_usage,onChange:e=>{e&&n({stackable_v2_editor_compatibility:""}),n({stackable_v2_editor_compatibility_usage:e?"1":""})}}),(0,o.createElement)(r.CheckboxControl,{disabled:"1"===t.stackable_v2_editor_compatibility||"1"===t.stackable_v2_editor_compatibility_usage,label:(0,d.__)("Load version 2 frontend block stylesheet and scripts for backward compatibility",s.i18n),className:p((0,d.__)("Load version 2 frontend block stylesheet and scripts for backward compatibility",s.i18n),c),checked:"1"===t.stackable_v2_editor_compatibility||"1"===t.stackable_v2_editor_compatibility_usage||"1"===t.stackable_v2_frontend_compatibility,onChange:e=>{n({stackable_v2_frontend_compatibility:e?"1":""})}}))))},Fs=e=>{const t=e.filteredSearchTree.find((e=>"v2-settings"===e.id)).groups,n=t.find((e=>"optimizations"===e.id)),a=t.find((e=>"blocks"===e.id)),l=g()(["s-v2-settings",{"s-settings-hide":"v2-settings"!==e.currentTab}]);return(0,o.createElement)("div",{className:l},n.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("🏃‍♂️ Optimization Settings",s.i18n)," (V2)"),(0,o.createElement)("p",{className:"s-settings-subtitle"},(0,d.__)("Here are some settings that you can tweak to optimize Stackable.",s.i18n),(0,o.createElement)("a",{href:"https://docs.wpstackable.com/article/460-how-to-use-optimization-settings?utm_source=wp-settings-global-settings&utm_campaign=learnmore&utm_medium=wp-dashboard",target:"_docs"},(0,d.__)("Learn more.",s.i18n)," "),(0,o.createElement)("br",null),(0,o.createElement)("strong",null,(0,d.__)("This only works for version 2 blocks.",s.i18n))),(0,o.createElement)(vs,{searchedSettings:n.children})),a.children.length>0&&(0,o.createElement)("div",{className:"s-setting-group"},(0,o.createElement)("h2",null,(0,d.__)("Enable & Disable Blocks",s.i18n)," (V2)"),(0,o.createElement)("strong",null,(0,d.__)("This only works for version 2 blocks.",s.i18n)),(0,o.createElement)(gs,{blocks:bs,disabledBlocks:s.v2disabledBlocks,searchedSettings:a.children})))};l((()=>{document.querySelector(".s-getting-started__body")&&i(document.querySelector(".s-getting-started__body")).render((0,o.createElement)(R,null)),document.querySelector(".s-getting-started__block-list")&&i(document.querySelector(".s-getting-started__block-list")).render((0,o.createElement)(Ss,null)),document.querySelector(".s-sidenav")&&i(document.querySelector(".s-sidenav")).render((0,o.createElement)(Ts,null)),document.querySelector(".s-rest-settings-notice")&&i(document.querySelector(".s-rest-settings-notice")).render((0,o.createElement)(xs,null)),document.querySelector(".s-content")&&i(document.querySelector(".s-content")).render((0,o.createElement)(Ns,null))}))},4184:(e,t)=>{var n;!function(){"use strict";var o={}.hasOwnProperty;function a(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var l=typeof n;if("string"===l||"number"===l)e.push(n);else if(Array.isArray(n)){if(n.length){var r=a.apply(null,n);r&&e.push(r)}}else if("object"===l)if(n.toString===Object.prototype.toString)for(var i in n)o.call(n,i)&&n[i]&&e.push(i);else e.push(n.toString())}}return e.join(" ")}e.exports?(a.default=a,e.exports=a):void 0===(n=function(){return a}.apply(t,[]))||(e.exports=n)}()},8874:e=>{"use strict";e.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},5985:(e,t,n)=>{"use strict";var o=n(2409);e.exports={name:"hsl",min:[0,0,0],max:[360,100,100],channel:["hue","saturation","lightness"],alias:["HSL"],rgb:function(e){var t,n,o,a,l,r=e[0]/360,i=e[1]/100,s=e[2]/100;if(0===i)return[l=255*s,l,l];t=2*s-(n=s<.5?s*(1+i):s+i-s*i),a=[0,0,0];for(var c=0;c<3;c++)(o=r+1/3*-(c-1))<0?o++:o>1&&o--,l=6*o<1?t+6*(n-t)*o:2*o<1?n:3*o<2?t+(n-t)*(2/3-o)*6:t,a[c]=255*l;return a}},o.hsl=function(e){var t,n,o=e[0]/255,a=e[1]/255,l=e[2]/255,r=Math.min(o,a,l),i=Math.max(o,a,l),s=i-r;return i===r?t=0:o===i?t=(a-l)/s:a===i?t=2+(l-o)/s:l===i&&(t=4+(o-a)/s),(t=Math.min(60*t,360))<0&&(t+=360),n=(r+i)/2,[t,100*(i===r?0:n<=.5?s/(i+r):s/(2-i-r)),100*n]}},2409:e=>{"use strict";e.exports={name:"rgb",min:[0,0,0],max:[255,255,255],channel:["red","green","blue"],alias:["RGB"]}},4247:function(e,t){var n,o;void 0===(o="function"==typeof(n=function(){var e=/^v?(?:\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+)(\.(?:[x*]|\d+))?(?:-[\da-z\-]+(?:\.[\da-z\-]+)*)?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i;function t(e){var t,n,o=e.replace(/^v/,"").replace(/\+.*$/,""),a=(n="-",-1===(t=o).indexOf(n)?t.length:t.indexOf(n)),l=o.substring(0,a).split(".");return l.push(o.substring(a+1)),l}function n(e){return isNaN(Number(e))?e:Number(e)}function o(t){if("string"!=typeof t)throw new TypeError("Invalid argument expected string");if(!e.test(t))throw new Error("Invalid argument not valid semver ('"+t+"' received)")}function a(e,a){[e,a].forEach(o);for(var l=t(e),r=t(a),i=0;i<Math.max(l.length-1,r.length-1);i++){var s=parseInt(l[i]||0,10),c=parseInt(r[i]||0,10);if(s>c)return 1;if(c>s)return-1}var u=l[l.length-1],d=r[r.length-1];if(u&&d){var p=u.split(".").map(n),m=d.split(".").map(n);for(i=0;i<Math.max(p.length,m.length);i++){if(void 0===p[i]||"string"==typeof m[i]&&"number"==typeof p[i])return-1;if(void 0===m[i]||"string"==typeof p[i]&&"number"==typeof m[i])return 1;if(p[i]>m[i])return 1;if(m[i]>p[i])return-1}}else if(u||d)return u?-1:1;return 0}var l=[">",">=","=","<","<="],r={">":[1],">=":[0,1],"=":[0],"<=":[-1,0],"<":[-1]};return a.validate=function(t){return"string"==typeof t&&e.test(t)},a.compare=function(e,t,n){!function(e){if("string"!=typeof e)throw new TypeError("Invalid operator type, expected string but got "+typeof e);if(-1===l.indexOf(e))throw new TypeError("Invalid operator, expected one of "+l.join("|"))}(n);var o=a(e,t);return r[n].indexOf(o)>-1},a})?n.apply(t,[]):n)||(e.exports=o)},4863:function(e){e.exports=function(){"use strict";var e=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var n=Object.prototype.toString.call(e);return"[object RegExp]"===n||"[object Date]"===n||function(e){return e.$$typeof===t}(e)}(e)};var t="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?r((n=e,Array.isArray(n)?[]:{}),e,t):e;var n}function o(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function a(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return e.propertyIsEnumerable(t)})):[]}(e))}function l(e,t,o){var l={};return o.isMergeableObject(e)&&a(e).forEach((function(t){l[t]=n(e[t],o)})),a(t).forEach((function(a){o.isMergeableObject(t[a])&&e[a]?l[a]=function(e,t){if(!t.customMerge)return r;var n=t.customMerge(e);return"function"==typeof n?n:r}(a,o)(e[a],t[a],o):l[a]=n(t[a],o)})),l}function r(t,a,r){(r=r||{}).arrayMerge=r.arrayMerge||o,r.isMergeableObject=r.isMergeableObject||e;var i=Array.isArray(a);return i===Array.isArray(t)?i?r.arrayMerge(t,a,r):l(t,a,r):n(a,r)}return r.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,n){return r(e,n,t)}),{})},r}()},4063:e=>{"use strict";e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var o,a,l;if(Array.isArray(t)){if((o=t.length)!=n.length)return!1;for(a=o;0!=a--;)if(!e(t[a],n[a]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((o=(l=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(a=o;0!=a--;)if(!Object.prototype.hasOwnProperty.call(n,l[a]))return!1;for(a=o;0!=a--;){var r=l[a];if(!e(t[r],n[r]))return!1}return!0}return t!=t&&n!=n}},1143:e=>{"use strict";e.exports=function(e,t,n,o,a,l,r,i){if(!e){var s;if(void 0===t)s=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var c=[n,o,a,l,r,i],u=0;(s=new Error(t.replace(/%s/g,(function(){return c[u++]})))).name="Invariant Violation"}throw s.framesToPop=1,s}}},4017:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.hexToRgb=function(e){var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null}},6537:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=n(7193);t.default=o.isDarkColor,e.exports=t.default},7193:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isDarkColor=void 0;var o=n(4017);t.isDarkColor=function(e,t){if(t&&t.override){var n=Object.keys(t.override).find((function(t){return t.toLowerCase()===e.toLowerCase()}));if(void 0!==n)return t.override[n]}var a=(0,o.hexToRgb)(e),l=[a.r/255,a.g/255,a.b/255].map((function(e){return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)}));return.2126*l[0]+.7152*l[1]+.0722*l[2]<=.179}},7418:e=>{"use strict";var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function a(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach((function(e){o[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},o)).join("")}catch(e){return!1}}()?Object.assign:function(e,l){for(var r,i,s=a(e),c=1;c<arguments.length;c++){for(var u in r=Object(arguments[c]))n.call(r,u)&&(s[u]=r[u]);if(t){i=t(r);for(var d=0;d<i.length;d++)o.call(r,i[d])&&(s[i[d]]=r[i[d]])}}return s}},2703:(e,t,n)=>{"use strict";var o=n(414);function a(){}function l(){}l.resetWarningCache=a,e.exports=function(){function e(e,t,n,a,l,r){if(r!==o){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:l,resetWarningCache:a};return n.PropTypes=n,n}},5697:(e,t,n)=>{e.exports=n(2703)()},414:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},8394:(e,t,n)=>{"use strict";var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),l=n(7294),r=d(l),i=d(n(5697)),s=d(n(1444)),c=d(n(8497)),u=n(7581);function d(e){return e&&e.__esModule?e:{default:e}}var p=function(){return!0},m=function(e){function t(e){var n=e.alwaysRenderSuggestions;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var o=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));return h.call(o),o.state={isFocused:!1,isCollapsed:!n,highlightedSectionIndex:null,highlightedSuggestionIndex:null,highlightedSuggestion:null,valueBeforeUpDown:null},o.justPressedUpDown=!1,o.justMouseEntered=!1,o.pressedSuggestion=null,o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"componentDidMount",value:function(){document.addEventListener("mousedown",this.onDocumentMouseDown),document.addEventListener("mouseup",this.onDocumentMouseUp),this.input=this.autowhatever.input,this.suggestionsContainer=this.autowhatever.itemsContainer}},{key:"componentWillReceiveProps",value:function(e){(0,s.default)(e.suggestions,this.props.suggestions)?e.highlightFirstSuggestion&&e.suggestions.length>0&&!1===this.justPressedUpDown&&!1===this.justMouseEntered&&this.highlightFirstSuggestion():this.willRenderSuggestions(e)?this.state.isCollapsed&&!this.justSelectedSuggestion&&this.revealSuggestions():this.resetHighlightedSuggestion()}},{key:"componentDidUpdate",value:function(e,t){var n=this.props,o=n.suggestions,a=n.onSuggestionHighlighted,l=n.highlightFirstSuggestion;if(!(0,s.default)(o,e.suggestions)&&o.length>0&&l)this.highlightFirstSuggestion();else if(a){var r=this.getHighlightedSuggestion();r!=t.highlightedSuggestion&&a({suggestion:r})}}},{key:"componentWillUnmount",value:function(){document.removeEventListener("mousedown",this.onDocumentMouseDown),document.removeEventListener("mouseup",this.onDocumentMouseUp)}},{key:"updateHighlightedSuggestion",value:function(e,t,n){var o=this;this.setState((function(a){var l=a.valueBeforeUpDown;return null===t?l=null:null===l&&void 0!==n&&(l=n),{highlightedSectionIndex:e,highlightedSuggestionIndex:t,highlightedSuggestion:null===t?null:o.getSuggestion(e,t),valueBeforeUpDown:l}}))}},{key:"resetHighlightedSuggestion",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.setState((function(t){var n=t.valueBeforeUpDown;return{highlightedSectionIndex:null,highlightedSuggestionIndex:null,highlightedSuggestion:null,valueBeforeUpDown:e?null:n}}))}},{key:"revealSuggestions",value:function(){this.setState({isCollapsed:!1})}},{key:"closeSuggestions",value:function(){this.setState({highlightedSectionIndex:null,highlightedSuggestionIndex:null,highlightedSuggestion:null,valueBeforeUpDown:null,isCollapsed:!0})}},{key:"getSuggestion",value:function(e,t){var n=this.props,o=n.suggestions,a=n.multiSection,l=n.getSectionSuggestions;return a?l(o[e])[t]:o[t]}},{key:"getHighlightedSuggestion",value:function(){var e=this.state,t=e.highlightedSectionIndex,n=e.highlightedSuggestionIndex;return null===n?null:this.getSuggestion(t,n)}},{key:"getSuggestionValueByIndex",value:function(e,t){return(0,this.props.getSuggestionValue)(this.getSuggestion(e,t))}},{key:"getSuggestionIndices",value:function(e){var t=e.getAttribute("data-section-index"),n=e.getAttribute("data-suggestion-index");return{sectionIndex:"string"==typeof t?parseInt(t,10):null,suggestionIndex:parseInt(n,10)}}},{key:"findSuggestionElement",value:function(e){var t=e;do{if(null!==t.getAttribute("data-suggestion-index"))return t;t=t.parentNode}while(null!==t);throw console.error("Clicked element:",e),new Error("Couldn't find suggestion element")}},{key:"maybeCallOnChange",value:function(e,t,n){var o=this.props.inputProps,a=o.value,l=o.onChange;t!==a&&l(e,{newValue:t,method:n})}},{key:"willRenderSuggestions",value:function(e){var t=e.suggestions,n=e.inputProps,o=e.shouldRenderSuggestions,a=n.value;return t.length>0&&o(a)}},{key:"getQuery",value:function(){var e=this.props.inputProps.value,t=this.state.valueBeforeUpDown;return(null===t?e:t).trim()}},{key:"render",value:function(){var e=this,t=this.props,n=t.suggestions,a=t.renderInputComponent,l=t.onSuggestionsFetchRequested,i=t.renderSuggestion,s=t.inputProps,d=t.multiSection,m=t.renderSectionTitle,h=t.id,g=t.getSectionSuggestions,v=t.theme,b=t.getSuggestionValue,f=t.alwaysRenderSuggestions,k=t.highlightFirstSuggestion,_=this.state,y=_.isFocused,w=_.isCollapsed,E=_.highlightedSectionIndex,S=_.highlightedSuggestionIndex,x=_.valueBeforeUpDown,C=f?p:this.props.shouldRenderSuggestions,T=s.value,M=s.onFocus,N=s.onKeyDown,I=this.willRenderSuggestions(this.props),O=f||y&&!w&&I,B=O?n:[],P=o({},s,{onFocus:function(t){if(!e.justSelectedSuggestion&&!e.justClickedOnSuggestionsContainer){var n=C(T);e.setState({isFocused:!0,isCollapsed:!n}),M&&M(t),n&&l({value:T,reason:"input-focused"})}},onBlur:function(t){e.justClickedOnSuggestionsContainer?e.input.focus():(e.blurEvent=t,e.justSelectedSuggestion||(e.onBlur(),e.onSuggestionsClearRequested()))},onChange:function(t){var n=t.target.value,a=C(n);e.maybeCallOnChange(t,n,"type"),e.suggestionsContainer&&(e.suggestionsContainer.scrollTop=0),e.setState(o({},k?{}:{highlightedSectionIndex:null,highlightedSuggestionIndex:null,highlightedSuggestion:null},{valueBeforeUpDown:null,isCollapsed:!a})),a?l({value:n,reason:"input-changed"}):e.onSuggestionsClearRequested()},onKeyDown:function(t,o){var a=t.keyCode;switch(a){case 40:case 38:if(w)C(T)&&(l({value:T,reason:"suggestions-revealed"}),e.revealSuggestions());else if(n.length>0){var r,i=o.newHighlightedSectionIndex,s=o.newHighlightedItemIndex;r=null===s?null===x?T:x:e.getSuggestionValueByIndex(i,s),e.updateHighlightedSuggestion(i,s,T),e.maybeCallOnChange(t,r,40===a?"down":"up")}t.preventDefault(),e.justPressedUpDown=!0,setTimeout((function(){e.justPressedUpDown=!1}));break;case 13:if(229===t.keyCode)break;var c=e.getHighlightedSuggestion();if(O&&!f&&e.closeSuggestions(),null!=c){var u=b(c);e.maybeCallOnChange(t,u,"enter"),e.onSuggestionSelected(t,{suggestion:c,suggestionValue:u,suggestionIndex:S,sectionIndex:E,method:"enter"}),e.justSelectedSuggestion=!0,setTimeout((function(){e.justSelectedSuggestion=!1}))}break;case 27:O&&t.preventDefault();var d=O&&!f;null===x?d||(e.maybeCallOnChange(t,"","escape"),C("")?l({value:"",reason:"escape-pressed"}):e.onSuggestionsClearRequested()):e.maybeCallOnChange(t,x,"escape"),d?(e.onSuggestionsClearRequested(),e.closeSuggestions()):e.resetHighlightedSuggestion()}N&&N(t)}}),D={query:this.getQuery()};return r.default.createElement(c.default,{multiSection:d,items:B,renderInputComponent:a,renderItemsContainer:this.renderSuggestionsContainer,renderItem:i,renderItemData:D,renderSectionTitle:m,getSectionItems:g,highlightedSectionIndex:E,highlightedItemIndex:S,inputProps:P,itemProps:this.itemProps,theme:(0,u.mapToAutowhateverTheme)(v),id:h,ref:this.storeAutowhateverRef})}}]),t}(l.Component);m.propTypes={suggestions:i.default.array.isRequired,onSuggestionsFetchRequested:function(e,t){if("function"!=typeof e[t])throw new Error("'onSuggestionsFetchRequested' must be implemented. See: https://github.com/moroshko/react-autosuggest#onSuggestionsFetchRequestedProp")},onSuggestionsClearRequested:function(e,t){var n=e[t];if(!1===e.alwaysRenderSuggestions&&"function"!=typeof n)throw new Error("'onSuggestionsClearRequested' must be implemented. See: https://github.com/moroshko/react-autosuggest#onSuggestionsClearRequestedProp")},onSuggestionSelected:i.default.func,onSuggestionHighlighted:i.default.func,renderInputComponent:i.default.func,renderSuggestionsContainer:i.default.func,getSuggestionValue:i.default.func.isRequired,renderSuggestion:i.default.func.isRequired,inputProps:function(e,t){var n=e[t];if(!n.hasOwnProperty("value"))throw new Error("'inputProps' must have 'value'.");if(!n.hasOwnProperty("onChange"))throw new Error("'inputProps' must have 'onChange'.")},shouldRenderSuggestions:i.default.func,alwaysRenderSuggestions:i.default.bool,multiSection:i.default.bool,renderSectionTitle:function(e,t){var n=e[t];if(!0===e.multiSection&&"function"!=typeof n)throw new Error("'renderSectionTitle' must be implemented. See: https://github.com/moroshko/react-autosuggest#renderSectionTitleProp")},getSectionSuggestions:function(e,t){var n=e[t];if(!0===e.multiSection&&"function"!=typeof n)throw new Error("'getSectionSuggestions' must be implemented. See: https://github.com/moroshko/react-autosuggest#getSectionSuggestionsProp")},focusInputOnSuggestionClick:i.default.bool,highlightFirstSuggestion:i.default.bool,theme:i.default.object,id:i.default.string},m.defaultProps={renderSuggestionsContainer:function(e){var t=e.containerProps,n=e.children;return r.default.createElement("div",t,n)},shouldRenderSuggestions:function(e){return e.trim().length>0},alwaysRenderSuggestions:!1,multiSection:!1,focusInputOnSuggestionClick:!0,highlightFirstSuggestion:!1,theme:u.defaultTheme,id:"1"};var h=function(){var e=this;this.onDocumentMouseDown=function(t){e.justClickedOnSuggestionsContainer=!1;for(var n=t.detail&&t.detail.target||t.target;null!==n&&n!==document;){if(null!==n.getAttribute("data-suggestion-index"))return;if(n===e.suggestionsContainer)return void(e.justClickedOnSuggestionsContainer=!0);n=n.parentNode}},this.storeAutowhateverRef=function(t){null!==t&&(e.autowhatever=t)},this.onSuggestionMouseEnter=function(t,n){var o=n.sectionIndex,a=n.itemIndex;e.updateHighlightedSuggestion(o,a),t.target===e.pressedSuggestion&&(e.justSelectedSuggestion=!0),e.justMouseEntered=!0,setTimeout((function(){e.justMouseEntered=!1}))},this.highlightFirstSuggestion=function(){e.updateHighlightedSuggestion(e.props.multiSection?0:null,0)},this.onDocumentMouseUp=function(){e.pressedSuggestion&&!e.justSelectedSuggestion&&e.input.focus(),e.pressedSuggestion=null},this.onSuggestionMouseDown=function(t){e.justSelectedSuggestion||(e.justSelectedSuggestion=!0,e.pressedSuggestion=t.target)},this.onSuggestionsClearRequested=function(){var t=e.props.onSuggestionsClearRequested;t&&t()},this.onSuggestionSelected=function(t,n){var o=e.props,a=o.alwaysRenderSuggestions,l=o.onSuggestionSelected,r=o.onSuggestionsFetchRequested;l&&l(t,n),a?r({value:n.suggestionValue,reason:"suggestion-selected"}):e.onSuggestionsClearRequested(),e.resetHighlightedSuggestion()},this.onSuggestionClick=function(t){var n=e.props,o=n.alwaysRenderSuggestions,a=n.focusInputOnSuggestionClick,l=e.getSuggestionIndices(e.findSuggestionElement(t.target)),r=l.sectionIndex,i=l.suggestionIndex,s=e.getSuggestion(r,i),c=e.props.getSuggestionValue(s);e.maybeCallOnChange(t,c,"click"),e.onSuggestionSelected(t,{suggestion:s,suggestionValue:c,suggestionIndex:i,sectionIndex:r,method:"click"}),o||e.closeSuggestions(),!0===a?e.input.focus():e.onBlur(),setTimeout((function(){e.justSelectedSuggestion=!1}))},this.onBlur=function(){var t=e.props,n=t.inputProps,o=t.shouldRenderSuggestions,a=n.value,l=n.onBlur,r=e.getHighlightedSuggestion(),i=o(a);e.setState({isFocused:!1,highlightedSectionIndex:null,highlightedSuggestionIndex:null,highlightedSuggestion:null,valueBeforeUpDown:null,isCollapsed:!i}),l&&l(e.blurEvent,{highlightedSuggestion:r})},this.onSuggestionMouseLeave=function(t){e.resetHighlightedSuggestion(!1),e.justSelectedSuggestion&&t.target===e.pressedSuggestion&&(e.justSelectedSuggestion=!1)},this.onSuggestionTouchStart=function(){e.justSelectedSuggestion=!0},this.onSuggestionTouchMove=function(){e.justSelectedSuggestion=!1,e.pressedSuggestion=null,e.input.focus()},this.itemProps=function(t){return{"data-section-index":t.sectionIndex,"data-suggestion-index":t.itemIndex,onMouseEnter:e.onSuggestionMouseEnter,onMouseLeave:e.onSuggestionMouseLeave,onMouseDown:e.onSuggestionMouseDown,onTouchStart:e.onSuggestionTouchStart,onTouchMove:e.onSuggestionTouchMove,onClick:e.onSuggestionClick}},this.renderSuggestionsContainer=function(t){var n=t.containerProps,o=t.children;return(0,e.props.renderSuggestionsContainer)({containerProps:n,children:o,query:e.getQuery()})}};t.default=m},8808:(e,t,n)=>{"use strict";e.exports=n(8394).default},7581:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTheme={container:"react-autosuggest__container",containerOpen:"react-autosuggest__container--open",input:"react-autosuggest__input",inputOpen:"react-autosuggest__input--open",inputFocused:"react-autosuggest__input--focused",suggestionsContainer:"react-autosuggest__suggestions-container",suggestionsContainerOpen:"react-autosuggest__suggestions-container--open",suggestionsList:"react-autosuggest__suggestions-list",suggestion:"react-autosuggest__suggestion",suggestionFirst:"react-autosuggest__suggestion--first",suggestionHighlighted:"react-autosuggest__suggestion--highlighted",sectionContainer:"react-autosuggest__section-container",sectionContainerFirst:"react-autosuggest__section-container--first",sectionTitle:"react-autosuggest__section-title"},t.mapToAutowhateverTheme=function(e){var t={};for(var n in e)switch(n){case"suggestionsContainer":t.itemsContainer=e[n];break;case"suggestionsContainerOpen":t.itemsContainerOpen=e[n];break;case"suggestion":t.item=e[n];break;case"suggestionFirst":t.itemFirst=e[n];break;case"suggestionHighlighted":t.itemHighlighted=e[n];break;case"suggestionsList":t.itemsList=e[n];break;default:t[n]=e[n]}return t}},6514:(e,t,n)=>{"use strict";var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),l=n(7294),r=p(l),i=p(n(5697)),s=p(n(2383)),c=p(n(8379)),u=p(n(1560)),d=p(n(7489));function p(e){return e&&e.__esModule?e:{default:e}}var m={},h=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.storeInputReference=function(e){null!==e&&(n.input=e)},n.storeItemsContainerReference=function(e){null!==e&&(n.itemsContainer=e)},n.onHighlightedItemChange=function(e){n.highlightedItem=e},n.getItemId=function(e,t){return null===t?null:"react-autowhatever-"+n.props.id+"-"+(null===e?"":"section-"+e)+"-item-"+t},n.onFocus=function(e){var t=n.props.inputProps;n.setState({isInputFocused:!0}),t.onFocus&&t.onFocus(e)},n.onBlur=function(e){var t=n.props.inputProps;n.setState({isInputFocused:!1}),t.onBlur&&t.onBlur(e)},n.onKeyDown=function(e){var t=n.props,o=t.inputProps,a=t.highlightedSectionIndex,l=t.highlightedItemIndex;switch(e.key){case"ArrowDown":case"ArrowUp":var r="ArrowDown"===e.key?"next":"prev",i=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],o=!0,a=!1,l=void 0;try{for(var r,i=e[Symbol.iterator]();!(o=(r=i.next()).done)&&(n.push(r.value),!t||n.length!==t);o=!0);}catch(e){a=!0,l=e}finally{try{!o&&i.return&&i.return()}finally{if(a)throw l}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}(n.sectionIterator[r]([a,l]),2),s=i[0],c=i[1];o.onKeyDown(e,{newHighlightedSectionIndex:s,newHighlightedItemIndex:c});break;default:o.onKeyDown(e,{highlightedSectionIndex:a,highlightedItemIndex:l})}},n.highlightedItem=null,n.state={isInputFocused:!1},n.setSectionsItems(e),n.setSectionIterator(e),n.setTheme(e),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"componentDidMount",value:function(){this.ensureHighlightedItemIsVisible()}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){e.items!==this.props.items&&this.setSectionsItems(e),e.items===this.props.items&&e.multiSection===this.props.multiSection||this.setSectionIterator(e),e.theme!==this.props.theme&&this.setTheme(e)}},{key:"componentDidUpdate",value:function(){this.ensureHighlightedItemIsVisible()}},{key:"setSectionsItems",value:function(e){e.multiSection&&(this.sectionsItems=e.items.map((function(t){return e.getSectionItems(t)})),this.sectionsLengths=this.sectionsItems.map((function(e){return e.length})),this.allSectionsAreEmpty=this.sectionsLengths.every((function(e){return 0===e})))}},{key:"setSectionIterator",value:function(e){this.sectionIterator=(0,s.default)({multiSection:e.multiSection,data:e.multiSection?this.sectionsLengths:e.items.length})}},{key:"setTheme",value:function(e){this.theme=(0,c.default)(e.theme)}},{key:"renderSections",value:function(){var e=this;if(this.allSectionsAreEmpty)return null;var t=this.theme,n=this.props,o=n.id,a=n.items,l=n.renderItem,i=n.renderItemData,s=n.renderSectionTitle,c=n.highlightedSectionIndex,p=n.highlightedItemIndex,m=n.itemProps;return a.map((function(n,a){var h="react-autowhatever-"+o+"-",g=h+"section-"+a+"-",v=0===a;return r.default.createElement("div",t(g+"container","sectionContainer",v&&"sectionContainerFirst"),r.default.createElement(u.default,{section:n,renderSectionTitle:s,theme:t,sectionKeyPrefix:g}),r.default.createElement(d.default,{items:e.sectionsItems[a],itemProps:m,renderItem:l,renderItemData:i,sectionIndex:a,highlightedItemIndex:c===a?p:null,onHighlightedItemChange:e.onHighlightedItemChange,getItemId:e.getItemId,theme:t,keyPrefix:h,ref:e.storeItemsListReference}))}))}},{key:"renderItems",value:function(){var e=this.props.items;if(0===e.length)return null;var t=this.theme,n=this.props,o=n.id,a=n.renderItem,l=n.renderItemData,i=n.highlightedSectionIndex,s=n.highlightedItemIndex,c=n.itemProps;return r.default.createElement(d.default,{items:e,itemProps:c,renderItem:a,renderItemData:l,highlightedItemIndex:null===i?s:null,onHighlightedItemChange:this.onHighlightedItemChange,getItemId:this.getItemId,theme:t,keyPrefix:"react-autowhatever-"+o+"-"})}},{key:"ensureHighlightedItemIsVisible",value:function(){var e=this.highlightedItem;if(e){var t=this.itemsContainer,n=e.offsetParent===t?e.offsetTop:e.offsetTop-t.offsetTop,o=t.scrollTop;n<o?o=n:n+e.offsetHeight>o+t.offsetHeight&&(o=n+e.offsetHeight-t.offsetHeight),o!==t.scrollTop&&(t.scrollTop=o)}}},{key:"render",value:function(){var e=this.theme,t=this.props,n=t.id,a=t.multiSection,l=t.renderInputComponent,i=t.renderItemsContainer,s=t.highlightedSectionIndex,c=t.highlightedItemIndex,u=this.state.isInputFocused,d=a?this.renderSections():this.renderItems(),p=null!==d,m=this.getItemId(s,c),h="react-autowhatever-"+n,g=o({role:"combobox","aria-haspopup":"listbox","aria-owns":h,"aria-expanded":p},e("react-autowhatever-"+n+"-container","container",p&&"containerOpen"),this.props.containerProps),v=l(o({type:"text",value:"",autoComplete:"off","aria-autocomplete":"list","aria-controls":h,"aria-activedescendant":m},e("react-autowhatever-"+n+"-input","input",p&&"inputOpen",u&&"inputFocused"),this.props.inputProps,{onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.props.inputProps.onKeyDown&&this.onKeyDown,ref:this.storeInputReference})),b=i({containerProps:o({id:h,role:"listbox"},e("react-autowhatever-"+n+"-items-container","itemsContainer",p&&"itemsContainerOpen"),{ref:this.storeItemsContainerReference}),children:d});return r.default.createElement("div",g,v,b)}}]),t}(l.Component);h.propTypes={id:i.default.string,multiSection:i.default.bool,renderInputComponent:i.default.func,renderItemsContainer:i.default.func,items:i.default.array.isRequired,renderItem:i.default.func,renderItemData:i.default.object,renderSectionTitle:i.default.func,getSectionItems:i.default.func,containerProps:i.default.object,inputProps:i.default.object,itemProps:i.default.oneOfType([i.default.object,i.default.func]),highlightedSectionIndex:i.default.number,highlightedItemIndex:i.default.number,theme:i.default.oneOfType([i.default.object,i.default.array])},h.defaultProps={id:"1",multiSection:!1,renderInputComponent:function(e){return r.default.createElement("input",e)},renderItemsContainer:function(e){var t=e.containerProps,n=e.children;return r.default.createElement("div",t,n)},renderItem:function(){throw new Error("`renderItem` must be provided")},renderItemData:m,renderSectionTitle:function(){throw new Error("`renderSectionTitle` must be provided")},getSectionItems:function(){throw new Error("`getSectionItems` must be provided")},containerProps:m,inputProps:m,itemProps:m,highlightedSectionIndex:null,highlightedItemIndex:null,theme:{container:"react-autowhatever__container",containerOpen:"react-autowhatever__container--open",input:"react-autowhatever__input",inputOpen:"react-autowhatever__input--open",inputFocused:"react-autowhatever__input--focused",itemsContainer:"react-autowhatever__items-container",itemsContainerOpen:"react-autowhatever__items-container--open",itemsList:"react-autowhatever__items-list",item:"react-autowhatever__item",itemFirst:"react-autowhatever__item--first",itemHighlighted:"react-autowhatever__item--highlighted",sectionContainer:"react-autowhatever__section-container",sectionContainerFirst:"react-autowhatever__section-container--first",sectionTitle:"react-autowhatever__section-title"}},t.default=h},6844:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),l=n(7294),r=c(l),i=c(n(5697)),s=c(n(3130));function c(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var p=function(e){function t(){var e,n,o;u(this,t);for(var a=arguments.length,l=Array(a),r=0;r<a;r++)l[r]=arguments[r];return n=o=d(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),o.storeItemReference=function(e){null!==e&&(o.item=e)},o.onMouseEnter=function(e){var t=o.props,n=t.sectionIndex,a=t.itemIndex;o.props.onMouseEnter(e,{sectionIndex:n,itemIndex:a})},o.onMouseLeave=function(e){var t=o.props,n=t.sectionIndex,a=t.itemIndex;o.props.onMouseLeave(e,{sectionIndex:n,itemIndex:a})},o.onMouseDown=function(e){var t=o.props,n=t.sectionIndex,a=t.itemIndex;o.props.onMouseDown(e,{sectionIndex:n,itemIndex:a})},o.onClick=function(e){var t=o.props,n=t.sectionIndex,a=t.itemIndex;o.props.onClick(e,{sectionIndex:n,itemIndex:a})},d(o,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"shouldComponentUpdate",value:function(e){return(0,s.default)(e,this.props,["renderItemData"])}},{key:"render",value:function(){var e=this.props,t=e.isHighlighted,n=e.item,a=e.renderItem,l=e.renderItemData,i=function(e,t){var n={};for(var o in e)t.indexOf(o)>=0||Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}(e,["isHighlighted","item","renderItem","renderItemData"]);return delete i.sectionIndex,delete i.itemIndex,"function"==typeof i.onMouseEnter&&(i.onMouseEnter=this.onMouseEnter),"function"==typeof i.onMouseLeave&&(i.onMouseLeave=this.onMouseLeave),"function"==typeof i.onMouseDown&&(i.onMouseDown=this.onMouseDown),"function"==typeof i.onClick&&(i.onClick=this.onClick),r.default.createElement("li",o({role:"option"},i,{ref:this.storeItemReference}),a(n,o({isHighlighted:t},l)))}}]),t}(l.Component);p.propTypes={sectionIndex:i.default.number,isHighlighted:i.default.bool.isRequired,itemIndex:i.default.number.isRequired,item:i.default.any.isRequired,renderItem:i.default.func.isRequired,renderItemData:i.default.object.isRequired,onMouseEnter:i.default.func,onMouseLeave:i.default.func,onMouseDown:i.default.func,onClick:i.default.func},t.default=p},7489:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},a=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),l=n(7294),r=u(l),i=u(n(5697)),s=u(n(6844)),c=u(n(3130));function u(e){return e&&e.__esModule?e:{default:e}}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var m=function(e){function t(){var e,n,o;d(this,t);for(var a=arguments.length,l=Array(a),r=0;r<a;r++)l[r]=arguments[r];return n=o=p(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(l))),o.storeHighlightedItemReference=function(e){o.props.onHighlightedItemChange(null===e?null:e.item)},p(o,n)}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),a(t,[{key:"shouldComponentUpdate",value:function(e){return(0,c.default)(e,this.props,["itemProps"])}},{key:"render",value:function(){var e=this,t=this.props,n=t.items,a=t.itemProps,l=t.renderItem,i=t.renderItemData,c=t.sectionIndex,u=t.highlightedItemIndex,d=t.getItemId,p=t.theme,m=t.keyPrefix,h=null===c?m:m+"section-"+c+"-",g="function"==typeof a;return r.default.createElement("ul",o({role:"listbox"},p(h+"items-list","itemsList")),n.map((function(t,n){var m=0===n,v=n===u,b=h+"item-"+n,f=g?a({sectionIndex:c,itemIndex:n}):a,k=o({id:d(c,n),"aria-selected":v},p(b,"item",m&&"itemFirst",v&&"itemHighlighted"),f);return v&&(k.ref=e.storeHighlightedItemReference),r.default.createElement(s.default,o({},k,{sectionIndex:c,isHighlighted:v,itemIndex:n,item:t,renderItem:l,renderItemData:i}))})))}}]),t}(l.Component);m.propTypes={items:i.default.array.isRequired,itemProps:i.default.oneOfType([i.default.object,i.default.func]),renderItem:i.default.func.isRequired,renderItemData:i.default.object.isRequired,sectionIndex:i.default.number,highlightedItemIndex:i.default.number,onHighlightedItemChange:i.default.func.isRequired,getItemId:i.default.func.isRequired,theme:i.default.func.isRequired,keyPrefix:i.default.string.isRequired},m.defaultProps={sectionIndex:null},t.default=m},1560:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),a=n(7294),l=s(a),r=s(n(5697)),i=s(n(3130));function s(e){return e&&e.__esModule?e:{default:e}}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}var d=function(e){function t(){return c(this,t),u(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"shouldComponentUpdate",value:function(e){return(0,i.default)(e,this.props)}},{key:"render",value:function(){var e=this.props,t=e.section,n=e.renderSectionTitle,o=e.theme,a=e.sectionKeyPrefix,r=n(t);return r?l.default.createElement("div",o(a+"title","sectionTitle"),r):null}}]),t}(a.Component);d.propTypes={section:r.default.any.isRequired,renderSectionTitle:r.default.func.isRequired,theme:r.default.func.isRequired,sectionKeyPrefix:r.default.string.isRequired},t.default=d},3130:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=function(e,t){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];if(e===t)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!0;var r={},i=void 0,s=void 0;for(i=0,s=o.length;i<s;i++)r[o[i]]=!0;for(i=0,s=a.length;i<s;i++){var c=a[i],u=e[c],d=t[c];if(u!==d){if(!r[c]||null===u||null===d||"object"!==(void 0===u?"undefined":n(u))||"object"!==(void 0===d?"undefined":n(d)))return!0;var p=Object.keys(u),m=Object.keys(d);if(p.length!==m.length)return!0;for(var h=0,g=p.length;h<g;h++){var v=p[h];if(u[v]!==d[v])return!0}}}return!1}},8497:(e,t,n)=>{"use strict";e.exports=n(6514).default},5112:function(e,t,n){"use strict";var o,a=this&&this.__extends||(o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},o(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),l=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var r=l(n(5697)),i=l(n(7294)),s=function(e){function t(n){var o=e.call(this,n)||this;return o.resetDragging=function(){o.frameDragCounter=0,o.setState({draggingOverFrame:!1,draggingOverTarget:!1})},o.handleWindowDragOverOrDrop=function(e){e.preventDefault()},o.handleFrameDrag=function(e){if(t.eventHasFiles(e))return o.frameDragCounter+="dragenter"===e.type?1:-1,1===o.frameDragCounter?(o.setState({draggingOverFrame:!0}),void(o.props.onFrameDragEnter&&o.props.onFrameDragEnter(e))):0===o.frameDragCounter?(o.setState({draggingOverFrame:!1}),void(o.props.onFrameDragLeave&&o.props.onFrameDragLeave(e))):void 0},o.handleFrameDrop=function(e){o.state.draggingOverTarget||(o.resetDragging(),o.props.onFrameDrop&&o.props.onFrameDrop(e))},o.handleDragOver=function(e){t.eventHasFiles(e)&&(o.setState({draggingOverTarget:!0}),!t.isIE()&&o.props.dropEffect&&(e.dataTransfer.dropEffect=o.props.dropEffect),o.props.onDragOver&&o.props.onDragOver(e))},o.handleDragLeave=function(e){o.setState({draggingOverTarget:!1}),o.props.onDragLeave&&o.props.onDragLeave(e)},o.handleDrop=function(e){if(o.props.onDrop&&t.eventHasFiles(e)){var n=e.dataTransfer?e.dataTransfer.files:null;o.props.onDrop(n,e)}o.resetDragging()},o.handleTargetClick=function(e){o.props.onTargetClick&&o.props.onTargetClick(e),o.resetDragging()},o.stopFrameListeners=function(e){e&&(e.removeEventListener("dragenter",o.handleFrameDrag),e.removeEventListener("dragleave",o.handleFrameDrag),e.removeEventListener("drop",o.handleFrameDrop))},o.startFrameListeners=function(e){e&&(e.addEventListener("dragenter",o.handleFrameDrag),e.addEventListener("dragleave",o.handleFrameDrag),e.addEventListener("drop",o.handleFrameDrop))},o.frameDragCounter=0,o.state={draggingOverFrame:!1,draggingOverTarget:!1},o}return a(t,e),t.prototype.componentDidMount=function(){this.startFrameListeners(this.props.frame),this.resetDragging(),window.addEventListener("dragover",this.handleWindowDragOverOrDrop),window.addEventListener("drop",this.handleWindowDragOverOrDrop)},t.prototype.componentDidUpdate=function(e){e.frame!==this.props.frame&&(this.resetDragging(),this.stopFrameListeners(e.frame),this.startFrameListeners(this.props.frame))},t.prototype.componentWillUnmount=function(){this.stopFrameListeners(this.props.frame),window.removeEventListener("dragover",this.handleWindowDragOverOrDrop),window.removeEventListener("drop",this.handleWindowDragOverOrDrop)},t.prototype.render=function(){var e=this.props,t=e.children,n=e.className,o=e.targetClassName,a=e.draggingOverFrameClassName,l=e.draggingOverTargetClassName,r=this.state,s=r.draggingOverTarget,c=o;return r.draggingOverFrame&&(c+=" "+a),s&&(c+=" "+l),i.default.createElement("div",{className:n,onDragOver:this.handleDragOver,onDragLeave:this.handleDragLeave,onDrop:this.handleDrop},i.default.createElement("div",{className:c,onClick:this.handleTargetClick},t))},t.isIE=function(){return"undefined"!=typeof window&&(-1!==window.navigator.userAgent.indexOf("MSIE")||window.navigator.appVersion.indexOf("Trident/")>0)},t.eventHasFiles=function(e){var t=!1;if(e.dataTransfer){var n=e.dataTransfer.types;for(var o in n)if("Files"===n[o]){t=!0;break}}return t},t.propTypes={className:r.default.string,targetClassName:r.default.string,draggingOverFrameClassName:r.default.string,draggingOverTargetClassName:r.default.string,onDragOver:r.default.func,onDragLeave:r.default.func,onDrop:r.default.func,onTargetClick:r.default.func,dropEffect:r.default.oneOf(["copy","move","link","none"]),frame:function(e,t,n){var o=e[t];return null==o?new Error("Warning: Required prop `"+t+"` was not specified in `"+n+"`"):o===document||o instanceof HTMLElement?void 0:new Error("Warning: Prop `"+t+"` must be one of the following: document, HTMLElement!")},onFrameDragEnter:r.default.func,onFrameDragLeave:r.default.func,onFrameDrop:r.default.func},t.defaultProps={dropEffect:"copy",frame:"undefined"==typeof window?void 0:window.document,className:"file-drop",targetClassName:"file-drop-target",draggingOverFrameClassName:"file-drop-dragging-over-frame",draggingOverTargetClassName:"file-drop-dragging-over-target"},t}(i.default.PureComponent);t.FileDrop=s},8379:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});function o(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}var a,l=(a=n(1894))&&a.__esModule?a:{default:a},r=function(e){return e};t.default=function(e){var t=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],o=!0,a=!1,l=void 0;try{for(var r,i=e[Symbol.iterator]();!(o=(r=i.next()).done)&&(n.push(r.value),!t||n.length!==t);o=!0);}catch(e){a=!0,l=e}finally{try{!o&&i.return&&i.return()}finally{if(a)throw l}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}(Array.isArray(e)&&2===e.length?e:[e,null],2),n=t[0],a=t[1];return function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];var c=i.map((function(e){return n[e]})).filter(r);return"string"==typeof c[0]||"function"==typeof a?{key:e,className:a?a.apply(void 0,o(c)):c.join(" ")}:{key:e,style:l.default.apply(void 0,[{}].concat(o(c)))}}},e.exports=t.default},1894:e=>{"use strict";var t=Object.prototype.propertyIsEnumerable;function n(e){if(null==e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}function o(e){var n=Object.getOwnPropertyNames(e);return Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(e))),n.filter((function(n){return t.call(e,n)}))}e.exports=Object.assign||function(e,t){for(var a,l,r=n(e),i=1;i<arguments.length;i++){a=arguments[i],l=o(Object(a));for(var s=0;s<l.length;s++)r[l[s]]=a[l[s]]}return r}},2408:(e,t,n)=>{"use strict";var o=n(7418),a=60103,l=60106;t.Fragment=60107,t.StrictMode=60108,t.Profiler=60114;var r=60109,i=60110,s=60112;t.Suspense=60113;var c=60115,u=60116;if("function"==typeof Symbol&&Symbol.for){var d=Symbol.for;a=d("react.element"),l=d("react.portal"),t.Fragment=d("react.fragment"),t.StrictMode=d("react.strict_mode"),t.Profiler=d("react.profiler"),r=d("react.provider"),i=d("react.context"),s=d("react.forward_ref"),t.Suspense=d("react.suspense"),c=d("react.memo"),u=d("react.lazy")}var p="function"==typeof Symbol&&Symbol.iterator;function m(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function b(){}function f(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error(m(85));this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var k=f.prototype=new b;k.constructor=f,o(k,v.prototype),k.isPureReactComponent=!0;var _={current:null},y=Object.prototype.hasOwnProperty,w={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,n){var o,l={},r=null,i=null;if(null!=t)for(o in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(r=""+t.key),t)y.call(t,o)&&!w.hasOwnProperty(o)&&(l[o]=t[o]);var s=arguments.length-2;if(1===s)l.children=n;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(o in s=e.defaultProps)void 0===l[o]&&(l[o]=s[o]);return{$$typeof:a,type:e,key:r,ref:i,props:l,_owner:_.current}}function S(e){return"object"==typeof e&&null!==e&&e.$$typeof===a}var x=/\/+/g;function C(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function T(e,t,n,o,r){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case a:case l:s=!0}}if(s)return r=r(s=e),e=""===o?"."+C(s,0):o,Array.isArray(r)?(n="",null!=e&&(n=e.replace(x,"$&/")+"/"),T(r,t,n,"",(function(e){return e}))):null!=r&&(S(r)&&(r=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(r,n+(!r.key||s&&s.key===r.key?"":(""+r.key).replace(x,"$&/")+"/")+e)),t.push(r)),1;if(s=0,o=""===o?".":o+":",Array.isArray(e))for(var c=0;c<e.length;c++){var u=o+C(i=e[c],c);s+=T(i,t,n,u,r)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(i=e.next()).done;)s+=T(i=i.value,t,n,u=o+C(i,c++),r);else if("object"===i)throw t=""+e,Error(m(31,"[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t));return s}function M(e,t,n){if(null==e)return e;var o=[],a=0;return T(e,o,"","",(function(e){return t.call(n,e,a++)})),o}function N(e){if(-1===e._status){var t=e._result;t=t(),e._status=0,e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}if(1===e._status)return e._result;throw e._result}var I={current:null};function O(){var e=I.current;if(null===e)throw Error(m(321));return e}var B={ReactCurrentDispatcher:I,ReactCurrentBatchConfig:{transition:0},ReactCurrentOwner:_,IsSomeRendererActing:{current:!1},assign:o};t.Children={map:M,forEach:function(e,t,n){M(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return M(e,(function(){t++})),t},toArray:function(e){return M(e,(function(e){return e}))||[]},only:function(e){if(!S(e))throw Error(m(143));return e}},t.Component=v,t.PureComponent=f,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B,t.cloneElement=function(e,t,n){if(null==e)throw Error(m(267,e));var l=o({},e.props),r=e.key,i=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,s=_.current),void 0!==t.key&&(r=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(u in t)y.call(t,u)&&!w.hasOwnProperty(u)&&(l[u]=void 0===t[u]&&void 0!==c?c[u]:t[u])}var u=arguments.length-2;if(1===u)l.children=n;else if(1<u){c=Array(u);for(var d=0;d<u;d++)c[d]=arguments[d+2];l.children=c}return{$$typeof:a,type:e.type,key:r,ref:i,props:l,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:i,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:r,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=S,t.lazy=function(e){return{$$typeof:u,_payload:{_status:-1,_result:e},_init:N}},t.memo=function(e,t){return{$$typeof:c,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return O().useCallback(e,t)},t.useContext=function(e,t){return O().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return O().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return O().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return O().useLayoutEffect(e,t)},t.useMemo=function(e,t){return O().useMemo(e,t)},t.useReducer=function(e,t,n){return O().useReducer(e,t,n)},t.useRef=function(e){return O().useRef(e)},t.useState=function(e){return O().useState(e)},t.version="17.0.2"},7294:(e,t,n)=>{"use strict";e.exports=n(2408)},2383:e=>{"use strict";var t=function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],o=!0,a=!1,l=void 0;try{for(var r,i=e[Symbol.iterator]();!(o=(r=i.next()).done)&&(n.push(r.value),!t||n.length!==t);o=!0);}catch(e){a=!0,l=e}finally{try{!o&&i.return&&i.return()}finally{if(a)throw l}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")};e.exports=function(e){var n=e.data,o=e.multiSection;function a(e){var a=t(e,2),l=a[0],r=a[1];return o?null===r||r===n[l]-1?null===(l=function(e){for(null===e?e=0:e++;e<n.length&&0===n[e];)e++;return e===n.length?null:e}(l))?[null,null]:[l,0]:[l,r+1]:0===n||r===n-1?[null,null]:null===r?[null,0]:[null,r+1]}return{next:a,prev:function(e){var a=t(e,2),l=a[0],r=a[1];return o?null===r||0===r?null===(l=function(e){for(null===e?e=n.length-1:e--;e>=0&&0===n[e];)e--;return-1===e?null:e}(l))?[null,null]:[l,n[l]-1]:[l,r-1]:0===n||0===r?[null,null]:null===r?[null,n-1]:[null,r-1]},isLast:function(e){return null===a(e)[1]}}}},1444:e=>{"use strict";e.exports=function(e,t){if(e===t)return!0;if(!e||!t)return!1;var n=e.length;if(t.length!==n)return!1;for(var o=0;o<n;o++)if(e[o]!==t[o])return!1;return!0}},3726:function(e,t,n){"use strict";var o;!function(a){if("function"!=typeof l){var l=function(e){return e};l.nonNative=!0}const r=l("plaintext"),i=l("html"),s=l("comment"),c=/<(\w*)>/g,u=/<\/?([^\s\/>]+)/;function d(e,t,n){return m(e=e||"",p(t=t||[],n=n||""))}function p(e,t){return{allowable_tags:e=function(e){let t=new Set;if("string"==typeof e){let n;for(;n=c.exec(e);)t.add(n[1])}else l.nonNative||"function"!=typeof e[l.iterator]?"function"==typeof e.forEach&&e.forEach(t.add,t):t=new Set(e);return t}(e),tag_replacement:t,state:r,tag_buffer:"",depth:0,in_quote_char:""}}function m(e,t){if("string"!=typeof e)throw new TypeError("'html' parameter must be a string");let n=t.allowable_tags,o=t.tag_replacement,a=t.state,l=t.tag_buffer,c=t.depth,u=t.in_quote_char,d="";for(let t=0,p=e.length;t<p;t++){let p=e[t];if(a===r)"<"===p?(a=i,l+=p):d+=p;else if(a===i)switch(p){case"<":if(u)break;c++;break;case">":if(u)break;if(c){c--;break}u="",a=r,l+=">",n.has(h(l))?d+=l:d+=o,l="";break;case'"':case"'":u=p===u?"":u||p,l+=p;break;case"-":"<!-"===l&&(a=s),l+=p;break;case" ":case"\n":if("<"===l){a=r,d+="< ",l="";break}l+=p;break;default:l+=p}else a===s&&(">"===p?("--"==l.slice(-2)&&(a=r),l=""):l+=p)}return t.state=a,t.tag_buffer=l,t.depth=c,t.in_quote_char=u,d}function h(e){let t=u.exec(e);return t?t[1].toLowerCase():null}d.init_streaming_mode=function(e,t){let n=p(e=e||[],t=t||"");return function(e){return m(e||"",n)}},void 0===(o=function(){return d}.call(t,n,t,e))||(e.exports=o)}()},2473:e=>{"use strict";e.exports=function(){}},9737:(e,t,n)=>{var o={"./accordion/block.json":2103,"./blockquote/block.json":8317,"./button-group/block.json":2405,"./button/block.json":4379,"./call-to-action/block.json":1578,"./card/block.json":8598,"./carousel/block.json":8744,"./column/block.json":8325,"./columns/block.json":7317,"./count-up/block.json":9730,"./countdown/block.json":8501,"./design-library/block.json":6914,"./divider/block.json":3695,"./expand/block.json":6705,"./feature-grid/block.json":3705,"./feature/block.json":4859,"./heading/block.json":1642,"./hero/block.json":7125,"./horizontal-scroller/block.json":1433,"./icon-box/block.json":285,"./icon-button/block.json":1897,"./icon-label/block.json":5983,"./icon-list-item/block.json":2646,"./icon-list/block.json":2413,"./icon/block.json":8293,"./image-box/block.json":782,"./image/block.json":4639,"./map/block.json":6324,"./notification/block.json":1705,"./number-box/block.json":9222,"./posts/block.json":7327,"./price/block.json":6386,"./pricing-box/block.json":3345,"./progress-bar/block.json":9890,"./progress-circle/block.json":4259,"./separator/block.json":7388,"./spacer/block.json":9206,"./subtitle/block.json":8381,"./tab-content/block.json":2283,"./tab-labels/block.json":7990,"./table-of-contents/block.json":3590,"./tabs/block.json":3573,"./team-member/block.json":8511,"./testimonial/block.json":8227,"./text/block.json":7921,"./timeline/block.json":6836,"./video-popup/block.json":7562};function a(e){var t=l(e);return n(t)}function l(e){if(!n.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}a.keys=function(){return Object.keys(o)},a.resolve=l,e.exports=a,a.id=9737},2103:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/accordion","title":"Accordion","description":"A title that your visitors can toggle to view more text. Use as FAQs or multiple ones for an Accordion.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Toggle","Faq"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/accordion-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon-label","stackable/heading","stackable/icon"],"stk-substitution-blocks":["stackable/text"]}')},8317:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/blockquote","title":"Blockquote","description":"Display a quote in style","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/blockquote-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon"],"stk-substitution-blocks":["stackable/text"]}')},2405:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/button-group","title":"Button Group","description":"Add a customizable button.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Link"],"stk-variants":[{"name":"icon-button","title":"Icon Button","description":"Add a customizable button.","category":"stackable","stk-type":"essential","stk-demo":"https://wpstackable.com/icon-button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"},{"name":"button","title":"Button","description":"Add a customizable button.","category":"stackable","stk-type":"essential","stk-demo":"https://wpstackable.com/button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"},{"name":"social-buttons","title":"Social Buttons","description":"Add social buttons.","category":"stackable","stk-type":"special","stk-demo":"https://wpstackable.com/social-buttons-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/button-group|icon-button"]}],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden"}')},4379:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/button","title":"Button","description":"Add a customizable button.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"parent":["stackable/button-group"],"keywords":["Link"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden","stk-demo":"https://wpstackable.com/button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-block-dependency":"stackable/button-group|button"}')},1578:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/call-to-action","title":"Call to Action","description":"A small section you can use to call the attention of your visitors. Great for calling attention to your products or deals.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"keywords":["CTA"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/call-to-action-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-substitution-blocks":["stackable/heading","stackable/text","stackable/button-group","stackable/button"]}')},8598:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/card","title":"Card","description":"Describe a single subject in a small card. You can use this to describe your product, service or a person.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/card-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-substitution-blocks":["stackable/heading","stackable/text","stackable/subtitle","stackable/button-group","stackable/button"]}')},8744:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/carousel","title":"Carousel","description":"A carousel slider.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Slider"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/carousel-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},8325:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/column","title":"Inner Column","description":"A single column with advanced layout options.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation","stackable/columnWrapDesktop"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"keywords":["Section rows"],"parent":["stackable/columns","stackable/carousel","stackable/feature","stackable/feature-grid","stackable/horizontal-scroller","stackable/tab-content"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden","stk-available-states":["enabled","hidden"]}')},7317:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/columns","title":"Columns","description":"Multiple columns with advanced layout options.","category":"stackable","usesContext":["postId","postType","queryId"],"keywords":["Section rows","Container"],"providesContext":{"stackable/innerBlockOrientation":"columnJustify","stackable/columnWrapDesktop":"columnWrapDesktop"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/columns-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-available-states":["enabled","hidden"]}')},9730:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/count-up","title":"Count Up","description":"Showcase your stats. Display how many customers you have or the number of downloads of your app.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Number"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/count-up-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},8501:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/countdown","title":"Countdown","description":"Display a countdown timer on your website.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Timer"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/countdown-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},6914:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/design-library","title":"Design Library","description":"Choose a layout or block from the Stackable Design Library.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Template"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/designs/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-available-states":["enabled","hidden"]}')},3695:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/divider","title":"Divider","description":"Add a pause between your content.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Horizontal Rule","HR"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special"}')},6705:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/expand","title":"Expand / Show More","description":"Display a small snippet of text. Your readers can toggle it to show more information.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Hide","Less"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/expand-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/text","stackable/button-group|button"]}')},3705:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/feature-grid","title":"Feature Grid","description":"Display multiple product features or services. You can use Feature Grids one after another.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/columnWrapDesktop":"columnWrapDesktop"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/feature-grid-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-substitution-blocks":["stackable/image","stackable/heading","stackable/text","stackable/button-group","stackable/button"]}')},4859:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/feature","title":"Feature","description":"Display a product feature or a service in a large area.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/columnWrapDesktop":"columnWrapDesktop"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/feature-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/image"],"stk-substitution-blocks":["stackable/heading","stackable/text","stackable/button-group","stackable/button"]}')},1642:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/heading","title":"Heading","description":"Introduce new sections of your content in style.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Title"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/advanced-heading-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},7125:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/hero","title":"Hero","description":"A large hero area. Typically used at the very top of a page.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"keywords":["Header"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/hero-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-substitution-blocks":["stackable/heading","stackable/text","stackable/button-group","stackable/button"]}')},1433:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/horizontal-scroller","title":"Horizontal Scroller","description":"A slider that scrolls horizontally.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Slider","Carousel"],"providesContext":{"stackable/columnFit":"columnFit"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/horizontal-scroller-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},285:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon-box","title":"Icon Box","description":"A small text area with an icon that can be used to summarize features or services","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/icon-box-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon-label","stackable/icon","stackable/heading"]}')},1897:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon-button","title":"Icon Button","description":"Add a customizable button.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"parent":["stackable/button-group"],"keywords":["Link"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden","stk-demo":"https://wpstackable.com/icon-button-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-block-dependency":"stackable/button-group|icon-button"}')},5983:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon-label","title":"Icon Label","description":"An Icon and Heading paired together.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["SVG"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/icon-label-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon","stackable/heading"]}')},2646:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon-list-item","title":"Icon List Item","description":"A single list entry in the Icon List block","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation","stackable/ordered","stackable/uniqueId"],"keywords":[],"parent":["stackable/icon-list"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden","stk-demo":"https://wpstackable.com/separator-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},2413:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon-list","title":"Icon List","description":"An unordered list with icons. You can use this as a list of features or benefits.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Checklist","Bullets","Number list"],"providesContext":{"stackable/ordered":"ordered","stackable/uniqueId":"uniqueId"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/icon-list-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},8293:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/icon","title":"Icon","description":"Pick an icon or upload your own SVG icon to decorate your content.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["SVG"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/icon-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},782:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/image-box","title":"Image Box","description":"Display an image that shows more information when hovered on. Can be used as a fancy link to other pages.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/image-box-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/image","stackable/subtitle","stackable/icon"],"stk-substitution-blocks":["stackable/heading","stackable/text"]}')},4639:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/image","title":"Image","description":"An image with advanced controls to make a visual statement.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/advanced-image-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},6324:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/map","title":"Map","description":"Embedded Google Map with advanced controls.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","keywords":["location","address"],"stk-type":"special","stk-demo":"https://wpstackable.com/map-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},1705:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/notification","title":"Notification","description":"Show a notice to your readers. People can dismiss the notice to permanently hide it.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"keywords":["Notice","Alert"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/notification-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon"],"stk-substitution-blocks":["stackable/heading","stackable/text","stackable/button-group","stackable/button"]}')},9222:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/number-box","title":"Number Box","description":"Display steps or methods that your users will do in your service.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Steps"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/number-box-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},7327:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/posts","title":"Posts","description":"Your latest blog posts. Use this to showcase a few of your posts in your landing pages.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Blog Posts","Lastest Posts","Query Loop"],"providesContext":{"type":"type","orderBy":"orderBy","order":"order","taxonomyType":"taxonomyType","taxonomy":"taxonomy","taxonomyFilterType":"taxonomyFilterType","postOffset":"postOffset","postExclude":"postExclude","postInclude":"postInclude","numberOfItems":"numberOfItems","stkQueryId":"stkQueryId"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/blog-posts-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},6386:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/price","title":"Price","description":"Show a price of a product or service with currency and a suffix styled with different weights","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Currency","Pricing","Number"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/price-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/text"]}')},3345:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/pricing-box","title":"Pricing Box","description":"Display the different pricing tiers of your business.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"keywords":["Currency","Price","Pricing Table"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/pricing-table-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/price","stackable/text","stackable/icon-list"],"stk-substitution-blocks":["stackable/heading","stackable/subtitle","stackable/button-group","stackable/button"]}')},9890:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/progress-bar","title":"Progress Bar","description":"Visualize a progress value or percentage in a bar.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["percentage status"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/progress-bar-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},4259:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/progress-circle","title":"Progress Circle","description":"Visualize a progress value or percentage in a circle.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["percentage status"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/progress-circle-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},7388:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/separator","title":"Separator","description":"A fancy separator to be placed between content.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Svg Divider"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/separator-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},9206:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/spacer","title":"Spacer","description":"Sometimes you just need some space.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special"}')},8381:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/subtitle","title":"Subtitle","description":"Subtitle text that you can add custom styling to from the global settings.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/subtitle-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},2283:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/tab-content","title":"Tab Content","description":"A wrapper for tab panels.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation","stackable/tabPanelEffect","stackable/equalTabHeight"],"keywords":[],"parent":["stackable/tabs"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden"}')},7990:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/tab-labels","title":"Tab Labels","description":"Create interactive navigation within tabs.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation","stackable/initialTabOpen","stackable/tabOrientation"],"keywords":[],"parent":["stackable/tabs"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"hidden"}')},3590:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/table-of-contents","title":"Table of Contents","description":"Automatically generated table of contents based on Heading blocks.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["ToC","Index","Outline"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/table-of-contents-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},3573:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/tabs","title":"Tabs","description":"Organize and display content in multiple tabs.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["toggle"],"providesContext":{"stackable/initialTabOpen":"initialTabOpen","stackable/tabOrientation":"tabOrientation","stackable/tabPanelEffect":"tabPanelEffect","stackable/equalTabHeight":"equalTabHeight"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/tabs-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},8511:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/team-member","title":"Team Member","description":"Display members of your team or your office. Use multiple Team Member blocks if you have a large team.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/team-member-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-substitution-blocks":["stackable/image","stackable/heading","stackable/subtitle","stackable/text","stackable/button-group","stackable/button"]}')},8227:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/testimonial","title":"Testimonial","description":"Showcase what your users say about your product or service.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"providesContext":{"stackable/innerBlockOrientation":"innerBlockOrientation"},"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"section","stk-demo":"https://wpstackable.com/testimonial-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/image-box"],"stk-substitution-blocks":["stackable/image","stackable/heading","stackable/subtitle","stackable/text"]}')},7921:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/text","title":"Text","description":"Start with the building block of all page layouts.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["Paragraph"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"essential","stk-demo":"https://wpstackable.com/advanced-text-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},6836:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/timeline","title":"Timeline","description":"Show events in chronological order","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["history","milestone"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/timeline-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink"}')},7562:e=>{"use strict";e.exports=JSON.parse('{"apiVersion":3,"name":"stackable/video-popup","title":"Video Popup","description":"Display a large thumbnail that your users can click to play a video full-screen. Great for introductory or tutorial videos.","category":"stackable","usesContext":["postId","postType","queryId","stackable/innerBlockOrientation"],"keywords":["YouTube","Vimeo","Embed Mp4"],"textdomain":"stackable-ultimate-gutenberg-blocks","stk-type":"special","stk-demo":"https://wpstackable.com/video-popup-block/?utm_source=welcome&utm_medium=settings&utm_campaign=view_demo&utm_content=demolink","stk-required-blocks":["stackable/icon","stackable/image"]}')}},l={};function r(e){var t=l[e];if(void 0!==t)return t.exports;var n=l[e]={exports:{}};return a[e].call(n.exports,n,n.exports,r),n.exports}r.m=a,r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,r.t=function(n,o){if(1&o&&(n=this(n)),8&o)return n;if("object"==typeof n&&n){if(4&o&&n.__esModule)return n;if(16&o&&"function"==typeof n.then)return n}var a=Object.create(null);r.r(a);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&o&&n;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>l[e]=()=>n[e]));return l.default=()=>n,r.d(a,l),a},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce(((t,n)=>(r.f[n](e,t),t)),[])),r.u=e=>"data/google-fonts.994007071c9a32044e7e.json",r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n={},o="stackable:",r.l=(e,t,a,l)=>{if(n[e])n[e].push(t);else{var i,s;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==e||d.getAttribute("data-webpack")==o+a){i=d;break}}i||(s=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,r.nc&&i.setAttribute("nonce",r.nc),i.setAttribute("data-webpack",o+a),i.src=e),n[e]=[t];var p=(t,o)=>{i.onerror=i.onload=null,clearTimeout(m);var a=n[e];if(delete n[e],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(o))),t)return t(o)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),s&&document.head.appendChild(i)}},r.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.g.importScripts&&(e=r.g.location+"");var t=r.g.document;if(!e&&t&&(t.currentScript&&(e=t.currentScript.src),!e)){var n=t.getElementsByTagName("script");n.length&&(e=n[n.length-1].src)}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),r.p=e})(),(()=>{var e={788:0};r.f.j=(t,n)=>{var o=r.o(e,t)?e[t]:void 0;if(0!==o)if(o)n.push(o[2]);else{var a=new Promise(((n,a)=>o=e[t]=[n,a]));n.push(o[2]=a);var l=r.p+r.u(t),i=new Error;r.l(l,(n=>{if(r.o(e,t)&&(0!==(o=e[t])&&(e[t]=void 0),o)){var a=n&&("load"===n.type?"missing":n.type),l=n&&n.target&&n.target.src;i.message="Loading chunk "+t+" failed.\n("+a+": "+l+")",i.name="ChunkLoadError",i.type=a,i.request=l,o[1](i)}}),"chunk-"+t,t)}};var t=(t,n)=>{var o,a,[l,i,s]=n,c=0;if(l.some((t=>0!==e[t]))){for(o in i)r.o(i,o)&&(r.m[o]=i[o]);s&&s(r)}for(t&&t(n);c<l.length;c++)a=l[c],r.o(e,a)&&e[a]&&e[a][0](),e[l[c]]=0},n=globalThis.webpackChunkstackable=globalThis.webpackChunkstackable||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),r(6446)})();