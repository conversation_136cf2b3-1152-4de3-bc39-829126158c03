var frontend_block_notification;(()=>{"use strict";var t,e={};(t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})})(e),window.stackableNotification=new class{init=()=>{document.querySelectorAll(".stk-block-notification.stk--is-dismissible").forEach((t=>{if(t._StackableHasInitAccordion)return;const e=`stckbl-notif-${t.getAttribute("data-block-id")}`;localStorage.getItem(e)&&!window.location.search.match(/preview=\w+/)&&(t.style.display="none"),t.querySelector(".stk-block-notification__close-button").addEventListener("click",(()=>{localStorage.setItem(e,1),t.style.display="none"})),t._StackableHasInitAccordion=!0}))}},t=window.stackableNotification.init,"undefined"!=typeof document&&("complete"!==document.readyState&&"interactive"!==document.readyState?document.addEventListener("DOMContentLoaded",t):t()),frontend_block_notification=e})();