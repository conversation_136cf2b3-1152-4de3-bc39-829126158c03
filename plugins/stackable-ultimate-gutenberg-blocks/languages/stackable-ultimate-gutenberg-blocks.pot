# Copyright (C) 2025 Gambit Technologies, Inc
# This file is distributed under the same license as the Stackable - Gutenberg Blocks plugin.
msgid ""
msgstr ""
"Project-Id-Version: Stackable - Gutenberg Blocks 3.15.3\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/stackable-ultimate-gutenberg-blocks\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2025-04-16T10:33:33+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.5.0\n"

#. Plugin Name of the plugin
msgid "Stackable - Gutenberg Blocks"
msgstr ""

#. Plugin URI of the plugin
msgid "https://wpstackable.com"
msgstr ""

#. Description of the plugin
msgid "An Amazing Block Library That Lets You Reimagine the Way You Use the WordPress Block Editor (Gutenberg)."
msgstr ""

#. Author of the plugin
msgid "Gambit Technologies, Inc"
msgstr ""

#. Author URI of the plugin
msgid "http://gambit.ph"
msgstr ""

#: dist/translation-strings.php:9
#: dist/admin_welcome.js:2
msgid "Scheme unavailable"
msgstr ""

#: dist/translation-strings.php:10
#: src/welcome/index.php:29
#: src/welcome/index.php:30
#: src/welcome/index.php:161
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Stackable"
msgstr ""

#: dist/translation-strings.php:11
#: dist/admin_welcome.js:2
msgid "Sans-Serif"
msgstr ""

#: dist/translation-strings.php:12
#: dist/admin_welcome.js:2
msgid "Serif"
msgstr ""

#: dist/translation-strings.php:13
#: dist/admin_welcome.js:2
msgid "Serif Alternative"
msgstr ""

#: dist/translation-strings.php:14
#: dist/admin_welcome.js:2
msgid "Monospace"
msgstr ""

#: dist/translation-strings.php:15
#: dist/admin_welcome.js:2
msgid "System UI"
msgstr ""

#: dist/translation-strings.php:16
#: dist/admin_welcome.js:2
msgid "Transitional"
msgstr ""

#: dist/translation-strings.php:17
#: dist/admin_welcome.js:2
msgid "Old Style"
msgstr ""

#: dist/translation-strings.php:18
#: dist/admin_welcome.js:2
msgid "Humanist"
msgstr ""

#: dist/translation-strings.php:19
#: dist/admin_welcome.js:2
msgid "Geometric Humanist"
msgstr ""

#: dist/translation-strings.php:20
#: dist/admin_welcome.js:2
msgid "Classical Humanist"
msgstr ""

#: dist/translation-strings.php:21
#: dist/admin_welcome.js:2
msgid "Neo Grotesque"
msgstr ""

#: dist/translation-strings.php:22
#: dist/admin_welcome.js:2
msgid "Monospace Slab Serif"
msgstr ""

#: dist/translation-strings.php:23
#: dist/admin_welcome.js:2
msgid "Monospace Code"
msgstr ""

#: dist/translation-strings.php:24
#: dist/admin_welcome.js:2
msgid "Industrial"
msgstr ""

#: dist/translation-strings.php:25
#: dist/admin_welcome.js:2
msgid "Rounded Sans"
msgstr ""

#: dist/translation-strings.php:26
#: dist/admin_welcome.js:2
msgid "Slab Serif"
msgstr ""

#: dist/translation-strings.php:27
#: dist/admin_welcome.js:2
msgid "Antique"
msgstr ""

#: dist/translation-strings.php:28
#: dist/admin_welcome.js:2
msgid "Didone"
msgstr ""

#: dist/translation-strings.php:29
#: dist/admin_welcome.js:2
msgid "Handwritten"
msgstr ""

#: dist/translation-strings.php:30
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:247
msgid "Default"
msgstr ""

#: dist/translation-strings.php:31
msgid "Description for this block. You can use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:32
msgid "Description for this block. Use this space for describing your block. Any text will do."
msgstr ""

#: dist/translation-strings.php:33
#: dist/admin_welcome.js:2
msgid "Essential Blocks"
msgstr ""

#: dist/translation-strings.php:34
#: dist/admin_welcome.js:2
msgid "All the necessary building blocks you need to design anything."
msgstr ""

#: dist/translation-strings.php:35
#: dist/admin_welcome.js:2
msgid "Special Blocks"
msgstr ""

#: dist/translation-strings.php:36
#: dist/admin_welcome.js:2
msgid "Blocks with special functionality that will allow you to create distinctive designs."
msgstr ""

#: dist/translation-strings.php:37
#: dist/admin_welcome.js:2
msgid "Section Blocks"
msgstr ""

#: dist/translation-strings.php:38
#: dist/admin_welcome.js:2
msgid "Use these blocks act as templates to help you build sections effortlessly."
msgstr ""

#: dist/translation-strings.php:39
#: dist/admin_welcome.js:2
msgid "Editor Settings"
msgstr ""

#: dist/translation-strings.php:40
#: dist/admin_welcome.js:2
msgid "Nested Block Width"
msgstr ""

#: dist/translation-strings.php:41
#: dist/admin_welcome.js:2
msgid "Nested Wide Block Width"
msgstr ""

#: dist/translation-strings.php:42
#: dist/admin_welcome.js:2
msgid "Stackable Text as Default Block"
msgstr ""

#: dist/translation-strings.php:43
#: src/stk-block-types.php:307
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:504
msgid "Design Library"
msgstr ""

#: dist/translation-strings.php:44
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Stackable Settings"
msgstr ""

#: dist/translation-strings.php:45
#: dist/admin_welcome.js:2
msgid "Block Linking (Beta)"
msgstr ""

#: dist/translation-strings.php:46
#: dist/admin_welcome.js:2
msgid "Toolbar Text Highlight"
msgstr ""

#: dist/translation-strings.php:47
#: dist/admin_welcome.js:2
msgid "Toolbar Dynamic Content"
msgstr ""

#: dist/translation-strings.php:48
#: dist/admin_welcome.js:2
msgid "Copy & Paste Styles"
msgstr ""

#: dist/translation-strings.php:49
#: dist/admin_welcome.js:2
msgid "Reset Layout"
msgstr ""

#: dist/translation-strings.php:50
#: dist/admin_welcome.js:2
msgid "Save as Default Block"
msgstr ""

#: dist/translation-strings.php:51
#: dist/admin_welcome.js:2
msgid "Don't show help video tooltips"
msgstr ""

#: dist/translation-strings.php:52
#: dist/admin_welcome.js:2
msgid "Auto-Collapse Panels"
msgstr ""

#: dist/translation-strings.php:53
#: dist/admin_welcome.js:2
msgid "Responsiveness"
msgstr ""

#: dist/translation-strings.php:54
#: dist/admin_welcome.js:2
msgid "Tablet Breakpoint"
msgstr ""

#: dist/translation-strings.php:55
#: dist/admin_welcome.js:2
msgid "Mobile Breakpoint"
msgstr ""

#: dist/translation-strings.php:56
#: dist/admin_welcome.js:2
msgid "Blocks"
msgstr ""

#: dist/translation-strings.php:57
#: dist/admin_welcome.js:2
msgid "Optimization"
msgstr ""

#: dist/translation-strings.php:58
#: dist/admin_welcome.js:2
msgid "Optimize Inline CSS"
msgstr ""

#: dist/translation-strings.php:59
#: dist/admin_welcome.js:2
msgid "Lazy Load Images within Carousels"
msgstr ""

#: dist/translation-strings.php:60
#: dist/admin_welcome.js:2
msgid "Global Settings"
msgstr ""

#: dist/translation-strings.php:61
#: dist/admin_welcome.js:2
msgid "Force Typography Styles"
msgstr ""

#: dist/translation-strings.php:62
#: dist/admin_welcome.js:2
msgid "Role Manager"
msgstr ""

#: dist/translation-strings.php:63
#: dist/admin_welcome.js:2
msgid "Administrator"
msgstr ""

#: dist/translation-strings.php:64
#: dist/admin_welcome.js:2
msgid "Editor"
msgstr ""

#: dist/translation-strings.php:65
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:269
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:273
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:277
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:282
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:287
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:291
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:295
#: dist/admin_welcome.js:2
msgid "Author"
msgstr ""

#: dist/translation-strings.php:66
#: dist/admin_welcome.js:2
msgid "Contributor"
msgstr ""

#: dist/translation-strings.php:67
#: dist/admin_welcome.js:2
msgid "Subscriber"
msgstr ""

#: dist/translation-strings.php:68
#: src/welcome/index.php:150
#: dist/admin_welcome.js:2
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:119
msgid "Custom Fields"
msgstr ""

#: dist/translation-strings.php:69
#: dist/admin_welcome.js:2
msgid "Integration"
msgstr ""

#: dist/translation-strings.php:70
#: dist/admin_welcome.js:2
msgid "Google Maps API Key"
msgstr ""

#: dist/translation-strings.php:71
#: dist/admin_welcome.js:2
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:126
msgid "FontAwesome Pro Kit"
msgstr ""

#: dist/translation-strings.php:72
#: dist/admin_welcome.js:2
msgid "FontAwesome Icon Library Version"
msgstr ""

#: dist/translation-strings.php:73
#: dist/admin_welcome.js:2
msgid "Miscellaneous "
msgstr ""

#: dist/translation-strings.php:74
#: dist/admin_welcome.js:2
msgid "Show Go premium notices"
msgstr ""

#: dist/translation-strings.php:75
#: dist/admin_welcome.js:2
msgid "Generate Global Colors for native blocks"
msgstr ""

#: dist/translation-strings.php:76
#: src/deprecated/v2/init.php:72
#: dist/admin_welcome.js:2
msgid "Load version 2 blocks in the editor"
msgstr ""

#: dist/translation-strings.php:77
#: dist/admin_welcome.js:2
msgid "Load version 2 blocks in the editor only when the page was using version 2 blocks"
msgstr ""

#: dist/translation-strings.php:78
#: dist/admin_welcome.js:2
msgid "Load version 2 frontend block stylesheet and scripts for backward compatibility"
msgstr ""

#: dist/translation-strings.php:79
#: dist/admin_welcome.js:2
msgid "V2 Settings"
msgstr ""

#: dist/translation-strings.php:80
#: dist/admin_welcome.js:2
msgid "See example"
msgstr ""

#: dist/translation-strings.php:81
#: dist/admin_welcome.js:2
msgid "Error getting Stackable settings. We got the following error. Please contact your administrator."
msgstr ""

#: dist/translation-strings.php:82
#: dist/admin_welcome.js:2
msgid "Disable %s block?"
msgstr ""

#: dist/translation-strings.php:83
#: dist/admin_welcome.js:2
msgid "Enable %s block?"
msgstr ""

#: dist/translation-strings.php:84
#: dist/admin_welcome.js:2
msgid "Disabling this block will also disable these blocks that require this block to function:"
msgstr ""

#: dist/translation-strings.php:85
#: dist/admin_welcome.js:2
msgid "Enabling this block will also enable these blocks that are needed for this block to function:"
msgstr ""

#: dist/translation-strings.php:86
#: dist/admin_custom_fields__premium_only.js:2
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:182
msgid "Cancel"
msgstr ""

#: dist/translation-strings.php:87
#: dist/admin_welcome.js:2
msgid "Disable"
msgstr ""

#: dist/translation-strings.php:88
#: dist/admin_welcome.js:2
msgid "Enable"
msgstr ""

#: dist/translation-strings.php:89
#: dist/admin_welcome.js:2
msgid "There are unsaved changes"
msgstr ""

#: dist/translation-strings.php:90
#: dist/admin_welcome.js:2
msgid "Saved Succesfully!"
msgstr ""

#: dist/translation-strings.php:91
#: dist/admin_welcome.js:2
#: dist/translation-strings.js:283
msgid "Save Changes"
msgstr ""

#: dist/translation-strings.php:92
#: dist/admin_welcome.js:2
msgid "Search settings"
msgstr ""

#: dist/translation-strings.php:93
#: dist/admin_welcome.js:2
msgid "No matching settings"
msgstr ""

#: dist/translation-strings.php:94
#: dist/admin_welcome.js:2
msgid "Block Widths"
msgstr ""

#: dist/translation-strings.php:95
#: dist/admin_welcome.js:2
msgid "Adjust the width of Stackable blocks here."
msgstr ""

#: dist/translation-strings.php:96
#: dist/admin_welcome.js:2
msgid "The width used when a Columns block has its Content Width set to center. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula."
msgstr ""

#: dist/translation-strings.php:97
#: dist/admin_welcome.js:2
msgid "The width used when a Columns block has its Content Width set to wide. This is automatically detected from your theme. You can adjust it if your blocks are not aligned correctly. In px, you can also use other units or use a calc() formula."
msgstr ""

#: dist/translation-strings.php:98
#: dist/admin_welcome.js:2
msgid "If enabled, Stackable Text blocks will be added by default instead of the native Paragraph Block."
msgstr ""

#: dist/translation-strings.php:99
#: dist/admin_welcome.js:2
msgid "Adds a button on the top of the editor which gives access to a collection of pre-made block designs. Note: You can still access the Design Library by adding the Design Library block."
msgstr ""

#: dist/translation-strings.php:100
#: dist/admin_welcome.js:2
msgid "Adds a button on the top of the editor which gives access to Stackable settings. Note: You won't be able to access Stackable settings when this is disabled."
msgstr ""

#: dist/translation-strings.php:101
#: src/editor-settings.php:143
#: dist/admin_welcome.js:2
msgid "Gives you the ability to link columns. Any changes you make on one column will automatically get applied on the other columns."
msgstr ""

#: dist/translation-strings.php:102
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:213
msgid "Learn more"
msgstr ""

#: dist/translation-strings.php:103
#: dist/admin_welcome.js:2
msgid "Toolbar"
msgstr ""

#: dist/translation-strings.php:104
#: dist/admin_welcome.js:2
msgid "You can disable some toolbar features here."
msgstr ""

#: dist/translation-strings.php:105
#: src/editor-settings.php:178
#: dist/admin_welcome.js:2
msgid "Adds a toolbar button for highlighting text"
msgstr ""

#: dist/translation-strings.php:106
#: dist/admin_welcome.js:2
msgid "Adds a toolbar button for inserting and modifying dynamic content"
msgstr ""

#: dist/translation-strings.php:107
#: dist/admin_welcome.js:2
msgid "Adds a toolbar button for advanced copying and pasting block styles"
msgstr ""

#: dist/translation-strings.php:108
#: dist/admin_welcome.js:2
msgid "Adds a toolbar button for resetting the layout of a stackble block back to the original"
msgstr ""

#: dist/translation-strings.php:109
#: dist/admin_welcome.js:2
msgid "Adds a toolbar button for saving a block as the default block"
msgstr ""

#: dist/translation-strings.php:110
#: dist/admin_welcome.js:2
msgid "Inspector"
msgstr ""

#: dist/translation-strings.php:111
#: dist/admin_welcome.js:2
msgid "Disables the help video tooltips that appear in the inspector."
msgstr ""

#: dist/translation-strings.php:112
#: src/editor-settings.php:119
#: dist/admin_welcome.js:2
msgid "Collapse other inspector panels when opening another, keeping only one open at a time."
msgstr ""

#: dist/translation-strings.php:113
#: dist/admin_welcome.js:2
msgid "Dynamic Breakpoints"
msgstr ""

#: dist/translation-strings.php:114
#: dist/admin_welcome.js:2
msgid "Blocks can be styles differently for tablet and mobile screens, and some styles adjust to make them fit better in smaller screens. You can change the widths when tablet and mobile views are triggered. "
msgstr ""

#: dist/translation-strings.php:115
#: dist/admin_welcome.js:2
msgid "Here you can enable, hide and disable Stackable blocks. Hiding blocks will hide the block from the list of available blocks. Disabling blocks will prevent them from being registered at all. When using block variations or design library patterns, disabled blocks will be substituted with the relevant core blocks."
msgstr ""

#: dist/translation-strings.php:116
#: dist/admin_welcome.js:2
msgid "Enable All"
msgstr ""

#: dist/translation-strings.php:117
#: dist/admin_welcome.js:2
msgid "Hide All"
msgstr ""

#: dist/translation-strings.php:118
#: dist/admin_welcome.js:2
msgid "Disable All"
msgstr ""

#: dist/translation-strings.php:119
#: dist/admin_welcome.js:2
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:120
msgid "Enabled"
msgstr ""

#: dist/translation-strings.php:120
#: dist/admin_welcome.js:2
msgid "Hidden"
msgstr ""

#: dist/translation-strings.php:121
#: dist/admin_welcome.js:2
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:121
msgid "Disabled"
msgstr ""

#: dist/translation-strings.php:122
#: dist/admin_welcome.js:2
msgid "Optimizations"
msgstr ""

#: dist/translation-strings.php:123
#: dist/admin_welcome.js:2
msgid "Here you can adjust some optimization settings that are performed by Stackable."
msgstr ""

#: dist/translation-strings.php:124
#: dist/admin_welcome.js:2
msgid "Optimize inlined CSS styles. If this is enabled, similar selectors will be combined together, helpful if you changed Block Defaults."
msgstr ""

#: dist/translation-strings.php:125
#: dist/admin_welcome.js:2
msgid "Disable this if you encounter layout or spacing issues when using images inside carousel-type blocks because of image lazy loading."
msgstr ""

#: dist/translation-strings.php:126
#: dist/admin_welcome.js:2
msgid "Here you can tweak Global Settings that affect the styles across your entire site."
msgstr ""

#: dist/translation-strings.php:127
#: dist/admin_welcome.js:2
msgid "Not forced"
msgstr ""

#: dist/translation-strings.php:128
#: dist/admin_welcome.js:2
msgid "Force styles"
msgstr ""

#: dist/translation-strings.php:129
#: dist/admin_welcome.js:2
msgid "Lock the Block Editor's inspector for different user roles, and give clients edit access to only images and content. Content Editing Mode affects all blocks. "
msgstr ""

#: dist/translation-strings.php:130
#: dist/admin_welcome.js:2
msgid "This is only available in Stackable Premium. "
msgstr ""

#: dist/translation-strings.php:131
#: dist/translation-strings.php:1867
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:160
#: src/welcome/index.php:296
#: dist/admin_welcome.js:2
msgid "Go Premium"
msgstr ""

#: dist/translation-strings.php:132
#: dist/admin_welcome.js:2
msgid "Create Custom Fields that you can reference across your entire site. You can assign which roles can manage your Custom Fields. "
msgstr ""

#: dist/translation-strings.php:133
#: dist/admin_welcome.js:2
msgid "Integrations"
msgstr ""

#: dist/translation-strings.php:134
#: dist/admin_welcome.js:2
msgid "Here are settings for the different integrations available in Stackable."
msgstr ""

#: dist/translation-strings.php:135
#: dist/admin_welcome.js:2
msgid "You are using the version set in your Font Awesome Pro Kit."
msgstr ""

#: dist/translation-strings.php:136
#: dist/admin_welcome.js:2
msgid "Miscellaneous"
msgstr ""

#: dist/translation-strings.php:137
#: dist/admin_welcome.js:2
msgid "Below are other minor settings. Some may be useful when upgrading from older versions of Stackable."
msgstr ""

#: dist/translation-strings.php:138
#: dist/admin_welcome.js:2
msgid "Show \"Go premium\" notices"
msgstr ""

#: dist/translation-strings.php:139
#: dist/admin_welcome.js:2
msgid "Migration Settings"
msgstr ""

#: dist/translation-strings.php:140
#: dist/admin_welcome.js:2
msgid "After enabling the version 2 blocks, please refresh the page to re-fetch the blocks from the server."
msgstr ""

#: dist/translation-strings.php:141
#: dist/admin_welcome.js:2
msgid "Migrating from version 2 to version 3?"
msgstr ""

#: dist/translation-strings.php:142
#: dist/admin_welcome.js:2
msgid "Learn more about migration and the settings below"
msgstr ""

#: dist/translation-strings.php:143
#: dist/admin_welcome.js:2
msgid "🏃‍♂️ Optimization Settings"
msgstr ""

#: dist/translation-strings.php:144
#: dist/admin_welcome.js:2
msgid "Here are some settings that you can tweak to optimize Stackable."
msgstr ""

#: dist/translation-strings.php:145
#: dist/admin_welcome.js:2
msgid "Learn more."
msgstr ""

#: dist/translation-strings.php:146
#: dist/admin_welcome.js:2
msgid "This only works for version 2 blocks."
msgstr ""

#: dist/translation-strings.php:147
#: dist/admin_welcome.js:2
msgid "Enable & Disable Blocks"
msgstr ""

#: dist/translation-strings.php:148
#: dist/admin_welcome.js:2
msgid "Tutorials"
msgstr ""

#: dist/translation-strings.php:149
#: dist/admin_welcome.js:2
msgid "Get to know the plugin and start your journey with our brand new Stackable Courses."
msgstr ""

#: dist/translation-strings.php:150
#: src/welcome/index.php:137
#: dist/admin_welcome.js:2
msgid "Documentation"
msgstr ""

#: dist/translation-strings.php:151
#: dist/admin_welcome.js:2
msgid "Visit our knowledge base for troubleshooting, guides, FAQs and updates."
msgstr ""

#: dist/translation-strings.php:152
#: dist/admin_welcome.js:2
msgid "Community"
msgstr ""

#: dist/translation-strings.php:153
#: dist/admin_welcome.js:2
msgid "Join our very active Stackable Community on Facebook."
msgstr ""

#: dist/translation-strings.php:154
#: dist/admin_welcome.js:2
msgid "The Basics of Stackable Blocks"
msgstr ""

#: dist/translation-strings.php:155
#: dist/admin_welcome.js:2
msgid "Learn how to personalize and tailor Stackable Blocks to match your website's unique style and design."
msgstr ""

#: dist/translation-strings.php:156
#: dist/admin_welcome.js:2
msgid "Flexbox Controls"
msgstr ""

#: dist/translation-strings.php:157
#: dist/admin_welcome.js:2
msgid "Explore the powerful Flexbox controls in Stackable that allow you to effortlessly create dynamic and responsive layouts."
msgstr ""

#: dist/translation-strings.php:158
#: dist/admin_welcome.js:2
msgid "Introduction to Hover Styles"
msgstr ""

#: dist/translation-strings.php:159
#: dist/admin_welcome.js:2
msgid "Discover the exciting world of hover styles in Stackable and learn how to add interactive and engaging effects to your website."
msgstr ""

#: dist/translation-strings.php:160
#: dist/admin_welcome.js:2
msgid "How to Use Image Settings"
msgstr ""

#: dist/translation-strings.php:161
#: dist/admin_welcome.js:2
msgid "Master the art of optimizing and enhancing images using Stackable's intuitive image settings for a visually captivating website."
msgstr ""

#: dist/translation-strings.php:162
#: dist/admin_welcome.js:2
msgid "Introduction to the Design Library"
msgstr ""

#: dist/translation-strings.php:163
#: dist/admin_welcome.js:2
msgid "Dive into the fundamentals of the Stackable Design Library and learn how to streamline your website development process with ease."
msgstr ""

#: dist/translation-strings.php:164
#: dist/admin_welcome.js:2
msgid "All About Typography in Stackable"
msgstr ""

#: dist/translation-strings.php:165
#: dist/admin_welcome.js:2
msgid "Unlock the full potential of typography in Stackable and gain insights on how to create visually stunning and impactful text designs."
msgstr ""

#: dist/translation-strings.php:166
#: dist/admin_welcome.js:2
msgid "Unleash the Full Potential of the WordPress Block Editor by Turning It into a Page Builder"
msgstr ""

#: dist/translation-strings.php:167
#: dist/admin_welcome.js:2
msgid "Learn the essentials in just a few minutes by watching this video. Scroll down to see more quick tutorials."
msgstr ""

#: dist/translation-strings.php:168
#: src/welcome/index.php:114
#: dist/admin_welcome.js:2
msgid "Getting Started"
msgstr ""

#: dist/translation-strings.php:169
#: dist/admin_welcome.js:2
msgid "Create a new page"
msgstr ""

#: dist/translation-strings.php:170
#: dist/admin_welcome.js:2
msgid "Learn the essentials"
msgstr ""

#: dist/translation-strings.php:171
#: dist/admin_welcome.js:2
msgid "Check out our library of tutorials and guides"
msgstr ""

#: dist/translation-strings.php:172
#: dist/admin_welcome.js:2
msgid "Go to Stackable Learn"
msgstr ""

#: dist/translation-strings.php:173
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:141
msgid "General"
msgstr ""

#: dist/translation-strings.php:174
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Open at the start"
msgstr ""

#: dist/translation-strings.php:175
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Close adjacent on open"
msgstr ""

#: dist/translation-strings.php:176
#: dist/editor_blocks.js:2
msgid "Automatically closes adjacent accordion panels when clicked."
msgstr ""

#: dist/translation-strings.php:177
#: dist/editor_blocks.js:2
msgid "Enable FAQ Schema"
msgstr ""

#: dist/translation-strings.php:178
#: dist/editor_blocks.js:2
msgid "Open Icon"
msgstr ""

#: dist/translation-strings.php:179
#: dist/editor_blocks.js:2
msgid "The open icon will appear when the accordion is opened"
msgstr ""

#: dist/translation-strings.php:180
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Title for This Block"
msgstr ""

#: dist/translation-strings.php:181
#: dist/editor_blocks.js:2
msgid "Description for this block. Use this space for describing your block. Any text will do. Description for this block. You can use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:182
#: dist/editor_blocks.js:2
msgctxt "Block layout name"
msgid "%s Layout"
msgstr ""

#: dist/translation-strings.php:183
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:134
msgctxt "Heading placeholder"
msgid "Title for This Block"
msgstr ""

#: dist/translation-strings.php:184
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:135
msgctxt "Content placeholder"
msgid "Description for this block. Use this space for describing your block. Any text will do. Description for this block. You can use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:185
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Plain"
msgstr ""

#: dist/translation-strings.php:186
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:404
msgid "Shadow"
msgstr ""

#: dist/translation-strings.php:187
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Colored"
msgstr ""

#: dist/translation-strings.php:188
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Plus"
msgstr ""

#: dist/translation-strings.php:189
#: dist/editor_blocks.js:2
msgid "Quotation Mark Icons"
msgstr ""

#: dist/translation-strings.php:190
#: dist/editor_blocks.js:2
msgid "Pick another icon"
msgstr ""

#: dist/translation-strings.php:191
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Round Thin"
msgstr ""

#: dist/translation-strings.php:192
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Round"
msgstr ""

#: dist/translation-strings.php:193
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Round Thick"
msgstr ""

#: dist/translation-strings.php:194
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Round Fat"
msgstr ""

#: dist/translation-strings.php:195
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Square Thin"
msgstr ""

#: dist/translation-strings.php:196
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Square"
msgstr ""

#: dist/translation-strings.php:197
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Square Simple"
msgstr ""

#: dist/translation-strings.php:198
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Square Modern"
msgstr ""

#: dist/translation-strings.php:199
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Square Fat"
msgstr ""

#: dist/translation-strings.php:200
#: dist/editor_blocks.js:2
msgid "Simple"
msgstr ""

#: dist/translation-strings.php:201
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Highlighted"
msgstr ""

#: dist/translation-strings.php:202
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Huge"
msgstr ""

#: dist/translation-strings.php:203
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Centered Quote"
msgstr ""

#: dist/translation-strings.php:204
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Subtitle for This Block"
msgstr ""

#: dist/translation-strings.php:205
#: src/stk-block-types.php:65
#: src/stk-block-types.php:111
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:483
msgid "Button"
msgstr ""

#: dist/translation-strings.php:206
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:136
msgctxt "Subtitle placeholder"
msgid "Subtitle for This Block"
msgstr ""

#: dist/translation-strings.php:207
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:137
msgctxt "Button placeholder"
msgid "Button"
msgstr ""

#: dist/translation-strings.php:208
#: dist/editor_blocks.js:2
msgid "Default 2"
msgstr ""

#: dist/translation-strings.php:209
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Horizontal"
msgstr ""

#: dist/translation-strings.php:210
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:318
msgid "Full"
msgstr ""

#: dist/translation-strings.php:211
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Faded"
msgstr ""

#: dist/translation-strings.php:212
#: dist/editor_blocks.js:2
msgid "Horizontal 2"
msgstr ""

#: dist/translation-strings.php:213
#: dist/editor_blocks.js:2
msgid "Horizontal 3"
msgstr ""

#: dist/translation-strings.php:214
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Split Centered"
msgstr ""

#: dist/translation-strings.php:215
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Ghost"
msgstr ""

#: dist/translation-strings.php:216
#: src/stk-block-types.php:78
#: src/stk-block-types.php:98
#: src/stk-block-types.php:550
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:481
msgid "Link"
msgstr ""

#: dist/translation-strings.php:217
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Button text"
msgstr ""

#: dist/translation-strings.php:218
#: dist/editor_blocks.js:2
msgid "Call to action"
msgstr ""

#: dist/translation-strings.php:219
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Vertical"
msgstr ""

#: dist/translation-strings.php:220
#: dist/editor_blocks.js:2
msgid "%s Alignment"
msgstr ""

#: dist/translation-strings.php:221
#: dist/editor_blocks.js:2
msgid "Content"
msgstr ""

#: dist/translation-strings.php:222
#: dist/editor_blocks.js:2
msgid "Full Width Buttons"
msgstr ""

#: dist/translation-strings.php:223
#: dist/editor_blocks.js:2
msgid "Flex Wrap"
msgstr ""

#: dist/translation-strings.php:224
#: dist/editor_blocks.js:2
msgid "No Wrap"
msgstr ""

#: dist/translation-strings.php:225
#: dist/editor_blocks.js:2
msgid "Wrap"
msgstr ""

#: dist/translation-strings.php:226
#: dist/editor_blocks.js:2
msgid "Wrap Reverse"
msgstr ""

#: dist/translation-strings.php:227
#: dist/editor_blocks.js:2
msgid "Button Text"
msgstr ""

#: dist/translation-strings.php:228
#: src/stk-block-types.php:103
#: src/stk-block-types.php:537
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:482
msgid "Icon Button"
msgstr ""

#: dist/translation-strings.php:229
#: src/stk-block-types.php:119
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:484
msgid "Social Buttons"
msgstr ""

#: dist/translation-strings.php:230
#: src/stk-block-types.php:120
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:485
msgid "Add social buttons."
msgstr ""

#: dist/translation-strings.php:231
#: dist/editor_blocks.js:2
msgid "Slides"
msgstr ""

#: dist/translation-strings.php:232
#: dist/editor_blocks.js:2
msgid "Carousel Type"
msgstr ""

#: dist/translation-strings.php:233
#: dist/editor_blocks.js:2
msgid "Slide"
msgstr ""

#: dist/translation-strings.php:234
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Fade"
msgstr ""

#: dist/translation-strings.php:235
#: dist/editor_blocks.js:2
msgid "Infinite Scrolling"
msgstr ""

#: dist/translation-strings.php:236
#: dist/editor_blocks.js:2
msgid "Only visible in the frontend."
msgstr ""

#: dist/translation-strings.php:237
#: dist/editor_blocks.js:2
msgid "Slides to Show"
msgstr ""

#: dist/translation-strings.php:238
#: dist/editor_blocks.js:2
msgid "Slide Gap"
msgstr ""

#: dist/translation-strings.php:239
#: dist/editor_blocks.js:2
msgid "Fade duration"
msgstr ""

#: dist/translation-strings.php:240
#: dist/editor_blocks.js:2
msgid "Autoplay"
msgstr ""

#: dist/translation-strings.php:241
#: dist/editor_blocks.js:2
msgid "Speed (ms)"
msgstr ""

#: dist/translation-strings.php:242
#: dist/editor_blocks.js:2
msgid "Arrows"
msgstr ""

#: dist/translation-strings.php:243
#: dist/editor_blocks.js:2
msgid "Previous Slide Icon"
msgstr ""

#: dist/translation-strings.php:244
#: dist/editor_blocks.js:2
msgid "Next Slide Icon"
msgstr ""

#: dist/translation-strings.php:245
#: dist/editor_blocks.js:2
msgid "Arrow Position"
msgstr ""

#: dist/translation-strings.php:246
#: dist/editor_blocks.js:2
msgid "Inside"
msgstr ""

#: dist/translation-strings.php:247
#: dist/editor_blocks.js:2
msgid "Outside"
msgstr ""

#: dist/translation-strings.php:248
#: dist/editor_blocks.js:2
msgid "%s Justify"
msgstr ""

#: dist/translation-strings.php:249
#: dist/editor_blocks.js:2
msgid "%s Offset"
msgstr ""

#: dist/translation-strings.php:250
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Button Gap"
msgstr ""

#: dist/translation-strings.php:251
#: dist/editor_blocks.js:2
msgid "%s Color"
msgstr ""

#: dist/translation-strings.php:252
#: src/stk-block-types.php:497
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:440
msgid "Icon"
msgstr ""

#: dist/translation-strings.php:253
#: dist/editor_blocks.js:2
msgid "%s Width"
msgstr ""

#: dist/translation-strings.php:254
#: dist/editor_blocks.js:2
msgid "%s Height"
msgstr ""

#: dist/translation-strings.php:255
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:459
msgid "Border Radius"
msgstr ""

#: dist/translation-strings.php:256
#: dist/editor_blocks.js:2
msgid "%s Size"
msgstr ""

#: dist/translation-strings.php:257
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:167
msgid "Opacity"
msgstr ""

#: dist/translation-strings.php:258
#: dist/editor_blocks.js:2
msgid "Show %s on mobile"
msgstr ""

#: dist/translation-strings.php:259
#: dist/editor_blocks.js:2
msgid "arrows"
msgstr ""

#: dist/translation-strings.php:260
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Dots"
msgstr ""

#: dist/translation-strings.php:261
#: dist/editor_blocks.js:2
msgid "Dot Style"
msgstr ""

#: dist/translation-strings.php:262
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Solid"
msgstr ""

#: dist/translation-strings.php:263
#: src/stk-block-types.php:983
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:585
msgid "Outline"
msgstr ""

#: dist/translation-strings.php:264
#: dist/editor_blocks.js:2
msgid "Dot"
msgstr ""

#: dist/translation-strings.php:265
#: dist/editor_blocks.js:2
msgid "Active Dot"
msgstr ""

#: dist/translation-strings.php:266
#: dist/editor_blocks.js:2
msgid "%s Gap"
msgstr ""

#: dist/translation-strings.php:267
#: dist/editor_blocks.js:2
msgid "To improve accessibility, the clickable area of the dots will not go below %s."
msgstr ""

#: dist/translation-strings.php:268
#: dist/editor_blocks.js:2
msgid "dots"
msgstr ""

#: dist/translation-strings.php:269
#: dist/editor_blocks.js:2
msgid "Accessibility"
msgstr ""

#: dist/translation-strings.php:270
#: dist/editor_blocks.js:2
msgid "%s label"
msgstr ""

#: dist/translation-strings.php:271
#: dist/editor_blocks.js:2
msgid "Previous slide"
msgstr ""

#: dist/translation-strings.php:272
#: dist/editor_blocks.js:2
msgid "Next slide"
msgstr ""

#: dist/translation-strings.php:273
#: dist/editor_blocks.js:2
msgid "Use %%d to show the slide number."
msgstr ""

#: dist/translation-strings.php:274
#: dist/editor_blocks.js:2
msgid "Slide N of N"
msgstr ""

#: dist/translation-strings.php:275
#: dist/editor_blocks.js:2
msgid "Use two %%d to show the slide number and the total slides. e.g. Slide 1 of 3."
msgstr ""

#: dist/translation-strings.php:276
#: dist/editor_blocks.js:2
msgid "1,234.56"
msgstr ""

#: dist/translation-strings.php:277
#: dist/editor_blocks.js:2
msgid "Counter"
msgstr ""

#: dist/translation-strings.php:278
#: dist/editor_blocks.js:2
msgid "Duration (ms)"
msgstr ""

#: dist/translation-strings.php:279
#: dist/editor_blocks.js:2
msgid "1,234,567"
msgstr ""

#: dist/translation-strings.php:280
#: dist/editor_blocks.js:2
msgid "Column Spacing"
msgstr ""

#: dist/translation-strings.php:281
#: dist/editor_blocks.js:2
msgid "Sets the paddings between the column content and the border."
msgstr ""

#: dist/translation-strings.php:282
#: dist/editor_blocks.js:2
msgid "Align Last Block to Bottom"
msgstr ""

#: dist/translation-strings.php:283
#: dist/editor_blocks.js:2
msgid "Colon"
msgstr ""

#: dist/translation-strings.php:284
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Line"
msgstr ""

#: dist/translation-strings.php:285
#: src/stk-block-types.php:875
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:570
msgid "Separator"
msgstr ""

#: dist/translation-strings.php:286
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Size"
msgstr ""

#: dist/translation-strings.php:287
#: dist/editor_blocks.js:2
msgid "Top Offset"
msgstr ""

#: dist/translation-strings.php:288
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:163
msgid "Color"
msgstr ""

#: dist/translation-strings.php:289
#: dist/editor_blocks.js:2
msgid "Due Date"
msgstr ""

#: dist/translation-strings.php:290
#: dist/editor_blocks.js:2
msgid "Recurring"
msgstr ""

#: dist/translation-strings.php:291
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:74
msgid "None"
msgstr ""

#: dist/translation-strings.php:292
#: dist/editor_blocks.js:2
msgid "Hide Block"
msgstr ""

#: dist/translation-strings.php:293
#: dist/editor_blocks.js:2
msgid "Display Message Upon Expiration"
msgstr ""

#: dist/translation-strings.php:294
#: dist/editor_blocks.js:2
msgid "Days"
msgstr ""

#: dist/translation-strings.php:295
#: dist/editor_blocks.js:2
msgid "Hours"
msgstr ""

#: dist/translation-strings.php:296
#: dist/editor_blocks.js:2
msgid "Minutes"
msgstr ""

#: dist/translation-strings.php:297
#: dist/editor_blocks.js:2
msgid "Seconds"
msgstr ""

#: dist/translation-strings.php:298
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:63
msgid "End Date"
msgstr ""

#: dist/translation-strings.php:299
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:61
msgid "Start Date"
msgstr ""

#: dist/translation-strings.php:300
#: dist/editor_blocks.js:2
msgid "Timezone"
msgstr ""

#: dist/translation-strings.php:301
#: dist/editor_blocks.js:2
msgid "Action on Expiration"
msgstr ""

#: dist/translation-strings.php:302
#: dist/editor_blocks.js:2
msgid "Countdown Duration"
msgstr ""

#: dist/translation-strings.php:303
#: dist/editor_blocks.js:2
msgid "Restart Countdown After no. of Hours"
msgstr ""

#: dist/translation-strings.php:304
#: dist/editor_blocks.js:2
msgid "Enable Double Digit"
msgstr ""

#: dist/translation-strings.php:305
#: dist/editor_blocks.js:2
msgid "Box Gap"
msgstr ""

#: dist/translation-strings.php:306
#: dist/editor_blocks.js:2
msgid "Label Top Margin"
msgstr ""

#: dist/translation-strings.php:307
#: dist/editor_blocks.js:2
msgid "Digits"
msgstr ""

#: dist/translation-strings.php:308
#: dist/editor_blocks.js:2
msgid "Labels"
msgstr ""

#: dist/translation-strings.php:309
#: dist/editor_blocks.js:2
msgid "Content Alignment"
msgstr ""

#: dist/translation-strings.php:310
#: dist/editor_blocks.js:2
msgid "Expired Message"
msgstr ""

#: dist/translation-strings.php:311
#: dist/editor_blocks.js:2
msgid "Days Label"
msgstr ""

#: dist/translation-strings.php:312
#: dist/editor_blocks.js:2
msgid "Hours Label"
msgstr ""

#: dist/translation-strings.php:313
#: dist/editor_blocks.js:2
msgid "Minutes Label"
msgstr ""

#: dist/translation-strings.php:314
#: dist/editor_blocks.js:2
msgid "Seconds Label"
msgstr ""

#: dist/translation-strings.php:315
#: dist/editor_blocks.js:2
msgid "100"
msgstr ""

#: dist/translation-strings.php:316
#: dist/editor_blocks.js:2
msgid "One column"
msgstr ""

#: dist/translation-strings.php:317
#: dist/editor_blocks.js:2
msgid "50 / 50"
msgstr ""

#: dist/translation-strings.php:318
#: dist/editor_blocks.js:2
msgid "Two columns; equal split"
msgstr ""

#: dist/translation-strings.php:319
#: dist/editor_blocks.js:2
msgid "30 / 70"
msgstr ""

#: dist/translation-strings.php:320
#: dist/editor_blocks.js:2
msgid "Two columns; one-third, two-thirds split"
msgstr ""

#: dist/translation-strings.php:321
#: dist/editor_blocks.js:2
msgid "70 / 30"
msgstr ""

#: dist/translation-strings.php:322
#: dist/editor_blocks.js:2
msgid "Two columns; two-thirds, one-third split"
msgstr ""

#: dist/translation-strings.php:323
#: dist/editor_blocks.js:2
msgid "33 / 33 / 33"
msgstr ""

#: dist/translation-strings.php:324
#: dist/editor_blocks.js:2
msgid "Three columns; equal split"
msgstr ""

#: dist/translation-strings.php:325
#: dist/editor_blocks.js:2
msgid "25 / 50 / 25"
msgstr ""

#: dist/translation-strings.php:326
#: dist/editor_blocks.js:2
msgid "Three columns; wide center column"
msgstr ""

#: dist/translation-strings.php:327
#: dist/editor_blocks.js:2
msgid "25 / 25 / 25 / 25"
msgstr ""

#: dist/translation-strings.php:328
#: dist/editor_blocks.js:2
msgid "Four columns; equal split"
msgstr ""

#: dist/translation-strings.php:329
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Stackable Design Library"
msgstr ""

#: dist/translation-strings.php:330
#: dist/editor_blocks.js:2
msgid "Open the Design Library and select a pre-designed block or layout."
msgstr ""

#: dist/translation-strings.php:331
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Open Design Library"
msgstr ""

#: dist/translation-strings.php:332
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Bar"
msgstr ""

#: dist/translation-strings.php:333
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Asterisks"
msgstr ""

#: dist/translation-strings.php:334
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Width"
msgstr ""

#: dist/translation-strings.php:335
#: dist/editor_blocks.js:2
msgid "Height / Size"
msgstr ""

#: dist/translation-strings.php:336
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Some short text that can be expanded to show more details."
msgstr ""

#: dist/translation-strings.php:337
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Show more"
msgstr ""

#: dist/translation-strings.php:338
#: dist/editor_blocks.js:2
msgid "Some long text that will be expanded when the show more button is clicked by the visitor."
msgstr ""

#: dist/translation-strings.php:339
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Show less"
msgstr ""

#: dist/translation-strings.php:340
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Less text"
msgstr ""

#: dist/translation-strings.php:341
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "More text"
msgstr ""

#: dist/translation-strings.php:342
#: dist/editor_blocks.js:2
msgid "Add heading text here"
msgstr ""

#: dist/translation-strings.php:343
#: src/stk-block-types.php:1072
#: dist/admin_custom_fields__premium_only.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:388
msgid "Text"
msgstr ""

#: dist/translation-strings.php:344
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Top Line"
msgstr ""

#: dist/translation-strings.php:345
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Line Color"
msgstr ""

#: dist/translation-strings.php:346
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Height"
msgstr ""

#: dist/translation-strings.php:347
#: dist/editor_blocks.js:2
msgid "Margin"
msgstr ""

#: dist/translation-strings.php:348
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:424
msgid "Align"
msgstr ""

#: dist/translation-strings.php:349
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Bottom Line"
msgstr ""

#: dist/translation-strings.php:350
#: dist/editor_blocks.js:2
msgid "Header Title"
msgstr ""

#: dist/translation-strings.php:351
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Half Overlay"
msgstr ""

#: dist/translation-strings.php:352
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Center Overlay"
msgstr ""

#: dist/translation-strings.php:353
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Side Overlay"
msgstr ""

#: dist/translation-strings.php:354
#: dist/editor_blocks.js:2
msgid "Half Layout"
msgstr ""

#: dist/translation-strings.php:355
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Half"
msgstr ""

#: dist/translation-strings.php:356
#: dist/editor_blocks.js:2
msgid "Float"
msgstr ""

#: dist/translation-strings.php:357
#: src/stk-block-types.php:437
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:138
msgid "Title"
msgstr ""

#: dist/translation-strings.php:358
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Large Mid"
msgstr ""

#: dist/translation-strings.php:359
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:154
msgid "Offset"
msgstr ""

#: dist/translation-strings.php:360
#: dist/editor_blocks.js:2
msgid "Zizag"
msgstr ""

#: dist/translation-strings.php:361
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Zigzag"
msgstr ""

#: dist/translation-strings.php:362
#: dist/editor_blocks.js:2
msgid "Description for this block. Use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:363
#: dist/editor_blocks.js:2
msgctxt "Content placeholder"
msgid "Description for this block. Use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:364
#: dist/editor_blocks.js:2
msgid "Overlap Shape 1"
msgstr ""

#: dist/translation-strings.php:365
#: dist/editor_blocks.js:2
msgid "Overlap Shape 2"
msgstr ""

#: dist/translation-strings.php:366
#: dist/editor_blocks.js:2
msgid "Overlap Shape 3"
msgstr ""

#: dist/translation-strings.php:367
#: dist/editor_blocks.js:2
msgid "Overlap Shape 4"
msgstr ""

#: dist/translation-strings.php:368
#: dist/editor_blocks.js:2
msgid "Overlap Shape 5"
msgstr ""

#: dist/translation-strings.php:369
#: dist/editor_blocks.js:2
msgid "Overlap Background 1"
msgstr ""

#: dist/translation-strings.php:370
#: dist/editor_blocks.js:2
msgid "Overlap Background 2"
msgstr ""

#: dist/translation-strings.php:371
#: dist/editor_blocks.js:2
msgid "Overlap Background 3"
msgstr ""

#: dist/translation-strings.php:372
#: dist/editor_blocks.js:2
msgid "Overlap Background 4"
msgstr ""

#: dist/translation-strings.php:373
#: dist/editor_blocks.js:2
msgid "Overlap Background 5"
msgstr ""

#: dist/translation-strings.php:374
#: dist/editor_blocks.js:2
msgid "Snapping"
msgstr ""

#: dist/translation-strings.php:375
#: dist/editor_blocks.js:2
msgid "Snap to Start"
msgstr ""

#: dist/translation-strings.php:376
#: dist/editor_blocks.js:2
msgid "Snap to Center"
msgstr ""

#: dist/translation-strings.php:377
#: dist/editor_blocks.js:2
msgid "No Snapping"
msgstr ""

#: dist/translation-strings.php:378
#: dist/editor_blocks.js:2
msgid "Item Width"
msgstr ""

#: dist/translation-strings.php:379
#: dist/editor_blocks.js:2
msgid "Inner Column Spacing"
msgstr ""

#: dist/translation-strings.php:380
#: dist/editor_blocks.js:2
msgid "Gap"
msgstr ""

#: dist/translation-strings.php:381
#: dist/editor_blocks.js:2
msgid "Left Offset"
msgstr ""

#: dist/translation-strings.php:382
#: dist/editor_blocks.js:2
msgid "Scrollbar"
msgstr ""

#: dist/translation-strings.php:383
#: dist/editor_blocks.js:2
msgid "Track Color"
msgstr ""

#: dist/translation-strings.php:384
#: dist/editor_blocks.js:2
msgid "Thumb Color"
msgstr ""

#: dist/translation-strings.php:385
#: dist/editor_blocks.js:2
msgid "Thumb Radius"
msgstr ""

#: dist/translation-strings.php:386
#: dist/editor_blocks.js:2
msgid "123"
msgstr ""

#: dist/translation-strings.php:387
#: dist/editor_blocks.js:2
msgid "456"
msgstr ""

#: dist/translation-strings.php:388
#: dist/editor_blocks.js:2
msgid "789"
msgstr ""

#: dist/translation-strings.php:389
#: src/stk-block-types.php:516
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:526
msgid "Icon Box"
msgstr ""

#: dist/translation-strings.php:390
#: src/stk-block-types.php:560
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:528
msgid "Icon Label"
msgstr ""

#: dist/translation-strings.php:391
#: dist/editor_blocks.js:2
msgid "Pill"
msgstr ""

#: dist/translation-strings.php:392
#: dist/editor_blocks.js:2
msgid "Unordered List"
msgstr ""

#: dist/translation-strings.php:393
#: dist/editor_blocks.js:2
msgid "Ordered List"
msgstr ""

#: dist/translation-strings.php:394
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "List"
msgstr ""

#: dist/translation-strings.php:395
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Grid"
msgstr ""

#: dist/translation-strings.php:396
#: src/stk-block-types.php:279
#: src/stk-block-types.php:790
#: dist/admin_custom_fields__premium_only.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:389
msgid "Number"
msgstr ""

#: dist/translation-strings.php:397
#: dist/editor_blocks.js:2
msgid "Padded Number"
msgstr ""

#: dist/translation-strings.php:398
#: dist/editor_blocks.js:2
msgid "Lowercase Roman"
msgstr ""

#: dist/translation-strings.php:399
#: dist/editor_blocks.js:2
msgid "Uppercase Roman"
msgstr ""

#: dist/translation-strings.php:400
#: dist/editor_blocks.js:2
msgid "Lowercase Letters"
msgstr ""

#: dist/translation-strings.php:401
#: dist/editor_blocks.js:2
msgid "Uppercase Letters"
msgstr ""

#: dist/translation-strings.php:402
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Dashed"
msgstr ""

#: dist/translation-strings.php:403
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Dotted"
msgstr ""

#: dist/translation-strings.php:404
#: dist/editor_blocks.js:2
msgid "List Item"
msgstr ""

#: dist/translation-strings.php:405
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Full Width"
msgstr ""

#: dist/translation-strings.php:406
#: dist/editor_blocks.js:2
msgid "More noticeable when using wide layouts or list item borders"
msgstr ""

#: dist/translation-strings.php:407
#: src/stk-block-types.php:242
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:497
msgid "Columns"
msgstr ""

#: dist/translation-strings.php:408
#: dist/editor_blocks.js:2
msgid "List Display Style"
msgstr ""

#: dist/translation-strings.php:409
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:430
msgid "Column Gap"
msgstr ""

#: dist/translation-strings.php:410
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:431
msgid "Row Gap"
msgstr ""

#: dist/translation-strings.php:411
#: dist/editor_blocks.js:2
msgid "Icon Gap"
msgstr ""

#: dist/translation-strings.php:412
#: dist/editor_blocks.js:2
msgid "Indentation"
msgstr ""

#: dist/translation-strings.php:413
#: dist/editor_blocks.js:2
msgid "Icons & Numbers"
msgstr ""

#: dist/translation-strings.php:414
#: dist/editor_blocks.js:2
msgid "List Type"
msgstr ""

#: dist/translation-strings.php:415
#: dist/editor_blocks.js:2
msgid "With Period"
msgstr ""

#: dist/translation-strings.php:416
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:216
msgid "Icon Opacity"
msgstr ""

#: dist/translation-strings.php:417
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Icon Rotation"
msgstr ""

#: dist/translation-strings.php:418
#: dist/editor_blocks.js:2
msgid "Icon Vertical Alignment"
msgstr ""

#: dist/translation-strings.php:419
#: dist/editor_blocks.js:2
msgid "This is more visible if you have long text in your list."
msgstr ""

#: dist/translation-strings.php:420
#: dist/editor_blocks.js:2
msgid "Icon Vertical Offset"
msgstr ""

#: dist/translation-strings.php:421
#: dist/editor_blocks.js:2
msgid "Icon List Item Borders"
msgstr ""

#: dist/translation-strings.php:422
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Borders"
msgstr ""

#: dist/translation-strings.php:423
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Border Width"
msgstr ""

#: dist/translation-strings.php:424
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Border Color"
msgstr ""

#: dist/translation-strings.php:425
#: dist/editor_blocks.js:2
msgid "Line 1"
msgstr ""

#: dist/translation-strings.php:426
#: dist/editor_blocks.js:2
msgid "Line 2"
msgstr ""

#: dist/translation-strings.php:427
#: dist/editor_blocks.js:2
msgid "Line 3"
msgstr ""

#: dist/translation-strings.php:428
#: dist/editor_blocks.js:2
msgid "Line 4"
msgstr ""

#: dist/translation-strings.php:429
#: dist/editor_blocks.js:2
msgid "Line 5"
msgstr ""

#: dist/translation-strings.php:430
#: dist/editor_blocks.js:2
msgid "Line 6"
msgstr ""

#: dist/translation-strings.php:431
#: dist/editor_blocks.js:2
msgid "Image Caption"
msgstr ""

#: dist/translation-strings.php:432
#: dist/editor_blocks.js:2
msgid "Caption"
msgstr ""

#: dist/translation-strings.php:433
#: dist/editor_blocks.js:2
msgid "Caption Alignment"
msgstr ""

#: dist/translation-strings.php:434
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:139
msgctxt "Text placeholder"
msgid "Text for This Block"
msgstr ""

#: dist/translation-strings.php:435
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Box"
msgstr ""

#: dist/translation-strings.php:436
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Captioned"
msgstr ""

#: dist/translation-strings.php:437
#: src/stk-block-types.php:673
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:542
msgid "Map"
msgstr ""

#: dist/translation-strings.php:438
#: dist/editor_blocks.js:2
msgid "Embedded content from Google Map Platform."
msgstr ""

#: dist/translation-strings.php:439
#: dist/editor_blocks.js:2
msgid "Some map features require a Google API Key."
msgstr ""

#: dist/translation-strings.php:440
#: dist/editor_blocks.js:2
msgid "Add API key here."
msgstr ""

#: dist/translation-strings.php:441
#: dist/editor_blocks.js:2
msgid "Location"
msgstr ""

#: dist/translation-strings.php:442
#: dist/editor_blocks.js:2
msgid "Enter an address or location"
msgstr ""

#: dist/translation-strings.php:443
#: dist/editor_blocks.js:2
msgid "Zoom"
msgstr ""

#: dist/translation-strings.php:444
#: dist/editor_blocks.js:2
msgid "Enable Dragging"
msgstr ""

#: dist/translation-strings.php:445
#: dist/editor_blocks.js:2
msgid "Full Screen Button"
msgstr ""

#: dist/translation-strings.php:446
#: dist/editor_blocks.js:2
msgid "Map Type Buttons"
msgstr ""

#: dist/translation-strings.php:447
#: dist/editor_blocks.js:2
msgid "Street View Button"
msgstr ""

#: dist/translation-strings.php:448
#: dist/editor_blocks.js:2
msgid "Zoom Buttons"
msgstr ""

#: dist/translation-strings.php:449
#: dist/editor_blocks.js:2
msgid "Map Style"
msgstr ""

#: dist/translation-strings.php:450
#: dist/editor_blocks.js:2
msgid "Custom Map Style (Paste JSON here)"
msgstr ""

#: dist/translation-strings.php:451
#: dist/editor_blocks.js:2
msgid "Learn how to use Custom Map Styles"
msgstr ""

#: dist/translation-strings.php:452
#: dist/editor_blocks.js:2
msgid "Map Marker"
msgstr ""

#: dist/translation-strings.php:453
#: dist/editor_blocks.js:2
msgid "Uploaded Icon and Icon Color settings are not fully compatible."
msgstr ""

#: dist/translation-strings.php:454
#: dist/editor_blocks.js:2
msgid "Horizontal Icon Anchor Point"
msgstr ""

#: dist/translation-strings.php:455
#: dist/editor_blocks.js:2
msgid "Vertical Icon Anchor Point"
msgstr ""

#: dist/translation-strings.php:456
#: dist/editor_blocks.js:2
msgid "Map Example"
msgstr ""

#: dist/translation-strings.php:457
#: dist/editor_blocks.js:2
msgid "Type in a pair of latitude longitude coordinates. You can also type in the name of the location if your API Key has Geocoding API and Places API enabled."
msgstr ""

#: dist/translation-strings.php:458
#: dist/editor_blocks.js:2
msgid "Silver"
msgstr ""

#: dist/translation-strings.php:459
#: dist/editor_blocks.js:2
msgid "Retro"
msgstr ""

#: dist/translation-strings.php:460
#: dist/editor_blocks.js:2
msgid "Dark"
msgstr ""

#: dist/translation-strings.php:461
#: dist/editor_blocks.js:2
msgid "Night"
msgstr ""

#: dist/translation-strings.php:462
#: dist/editor_blocks.js:2
msgid "Aubergine"
msgstr ""

#: dist/translation-strings.php:463
#: dist/editor_blocks.js:2
msgid "Embedded content from Google Maps Platform."
msgstr ""

#: dist/translation-strings.php:464
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:201
msgid "Shape"
msgstr ""

#: dist/translation-strings.php:465
#: dist/editor_blocks.js:2
msgid "01"
msgstr ""

#: dist/translation-strings.php:466
#: src/stk-block-types.php:744
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:553
msgid "Posts"
msgstr ""

#: dist/translation-strings.php:467
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "No posts found."
msgstr ""

#: dist/translation-strings.php:468
#: dist/editor_blocks.js:2
msgid "Content Arrangement"
msgstr ""

#: dist/translation-strings.php:469
#: dist/editor_blocks.js:2
msgid "Sets the order of the items displayed (category, title, meta, excerpt, read more button, image) for each post"
msgstr ""

#: dist/translation-strings.php:470
#: dist/editor_blocks.js:2
msgid "Query"
msgstr ""

#: dist/translation-strings.php:471
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:426
msgid "Number of items"
msgstr ""

#: dist/translation-strings.php:472
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Order by"
msgstr ""

#: dist/translation-strings.php:473
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Newest to Oldest"
msgstr ""

#: dist/translation-strings.php:474
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Oldest to Newest"
msgstr ""

#: dist/translation-strings.php:475
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "A → Z"
msgstr ""

#: dist/translation-strings.php:476
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Z → A"
msgstr ""

#: dist/translation-strings.php:477
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Last Modified to Oldest"
msgstr ""

#: dist/translation-strings.php:478
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Oldest Modified to Last"
msgstr ""

#: dist/translation-strings.php:479
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Menu Order"
msgstr ""

#: dist/translation-strings.php:480
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Random"
msgstr ""

#: dist/translation-strings.php:481
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Spacing"
msgstr ""

#: dist/translation-strings.php:482
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Featured Image"
msgstr ""

#: dist/translation-strings.php:483
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Category"
msgstr ""

#: dist/translation-strings.php:484
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Excerpt"
msgstr ""

#: dist/translation-strings.php:485
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Meta"
msgstr ""

#: dist/translation-strings.php:486
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Read More Link"
msgstr ""

#: dist/translation-strings.php:487
#: dist/editor_blocks.js:2
msgid "Apply hover effect when container is hovered"
msgstr ""

#: dist/translation-strings.php:488
#: dist/editor_blocks.js:2
msgid "Highlight Color"
msgstr ""

#: dist/translation-strings.php:489
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:311
msgid "Excerpt Length"
msgstr ""

#: dist/translation-strings.php:490
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Show Author"
msgstr ""

#: dist/translation-strings.php:491
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Show Date"
msgstr ""

#: dist/translation-strings.php:492
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Show Comments"
msgstr ""

#: dist/translation-strings.php:493
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Default (Dot)"
msgstr ""

#: dist/translation-strings.php:494
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Space"
msgstr ""

#: dist/translation-strings.php:495
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Comma"
msgstr ""

#: dist/translation-strings.php:496
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Dash"
msgstr ""

#: dist/translation-strings.php:497
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Pipe"
msgstr ""

#: dist/translation-strings.php:498
#: dist/editor_blocks.js:2
msgid "Sets the separators between meta items (dot, space, comma, dash, pipe)"
msgstr ""

#: dist/translation-strings.php:499
#: dist/editor_blocks.js:2
msgid "Add post links to images"
msgstr ""

#: dist/translation-strings.php:500
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:320
msgid "Image Size"
msgstr ""

#: dist/translation-strings.php:501
#: dist/editor_blocks.js:2
msgid "Sets the image display size to thumbnail, medium, large or full size. A smaller image size will also load faster."
msgstr ""

#: dist/translation-strings.php:502
#: src/block/posts/index.php:268
#: dist/editor_blocks.js:2
msgid "Continue Reading"
msgstr ""

#: dist/translation-strings.php:503
#: dist/editor_blocks.js:2
msgid "Read More Button"
msgstr ""

#: dist/translation-strings.php:504
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "featured"
msgstr ""

#: dist/translation-strings.php:505
#: src/block/posts/index.php:59
#: src/deprecated/v2/block/blog-posts/index.php:251
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "(Untitled)"
msgstr ""

#: dist/translation-strings.php:506
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Image Card"
msgstr ""

#: dist/translation-strings.php:507
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Portfolio"
msgstr ""

#: dist/translation-strings.php:508
#: dist/editor_blocks.js:2
msgid "Portfolio 2"
msgstr ""

#: dist/translation-strings.php:509
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Vertical Card"
msgstr ""

#: dist/translation-strings.php:510
#: dist/editor_blocks.js:2
msgid "Vertical Card 2"
msgstr ""

#: dist/translation-strings.php:511
#: dist/editor_blocks.js:2
msgid "$"
msgstr ""

#: dist/translation-strings.php:512
#: dist/editor_blocks.js:2
msgid "99"
msgstr ""

#: dist/translation-strings.php:513
#: dist/editor_blocks.js:2
msgid ".00"
msgstr ""

#: dist/translation-strings.php:514
#: dist/editor_blocks.js:2
msgid "Package inclusion one"
msgstr ""

#: dist/translation-strings.php:515
#: dist/editor_blocks.js:2
msgid "Package inclusion two"
msgstr ""

#: dist/translation-strings.php:516
#: dist/editor_blocks.js:2
msgid "Package inclusion three"
msgstr ""

#: dist/translation-strings.php:517
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:173
msgid "one"
msgstr ""

#: dist/translation-strings.php:518
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:174
msgid "two"
msgstr ""

#: dist/translation-strings.php:519
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:175
msgid "three"
msgstr ""

#: dist/translation-strings.php:520
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:176
msgid "Package inclusion %s"
msgstr ""

#: dist/translation-strings.php:521
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Compact"
msgstr ""

#: dist/translation-strings.php:522
#: dist/editor_blocks.js:2
msgid "Banner Layout"
msgstr ""

#: dist/translation-strings.php:523
#: dist/editor_blocks.js:2
msgid "Banner"
msgstr ""

#: dist/translation-strings.php:524
#: dist/editor_blocks.js:2
msgid "Colored Layout"
msgstr ""

#: dist/translation-strings.php:525
#: dist/editor_blocks.js:2
msgid "Sectioned Layout"
msgstr ""

#: dist/translation-strings.php:526
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Sectioned"
msgstr ""

#: dist/translation-strings.php:527
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:166
msgid "Flip Horizontally"
msgstr ""

#: dist/translation-strings.php:528
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Flip Vertically"
msgstr ""

#: dist/translation-strings.php:529
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Notification Type"
msgstr ""

#: dist/translation-strings.php:530
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Success"
msgstr ""

#: dist/translation-strings.php:531
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Error"
msgstr ""

#: dist/translation-strings.php:532
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Warning"
msgstr ""

#: dist/translation-strings.php:533
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Information"
msgstr ""

#: dist/translation-strings.php:534
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Dismissible"
msgstr ""

#: dist/translation-strings.php:535
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Icon Size"
msgstr ""

#: dist/translation-strings.php:536
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:215
msgid "Icon Color"
msgstr ""

#: dist/translation-strings.php:537
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Side"
msgstr ""

#: dist/translation-strings.php:538
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Bordered"
msgstr ""

#: dist/translation-strings.php:539
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Outlined"
msgstr ""

#: dist/translation-strings.php:540
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Large Icon"
msgstr ""

#: dist/translation-strings.php:541
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Label"
msgstr ""

#: dist/translation-strings.php:542
#: src/stk-block-types.php:894
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:573
msgid "Spacer"
msgstr ""

#: dist/translation-strings.php:543
#: dist/editor_blocks.js:2
msgid "Type / to choose a block"
msgstr ""

#: dist/translation-strings.php:544
#: dist/editor_blocks.js:2
msgid "Subtitle for this block."
msgstr ""

#: dist/translation-strings.php:545
#: dist/editor_blocks.js:2
msgid "Classic"
msgstr ""

#: dist/translation-strings.php:546
#: dist/editor_blocks.js:2
msgid "Centered Pills"
msgstr ""

#: dist/translation-strings.php:547
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:445
msgid "Left"
msgstr ""

#: dist/translation-strings.php:548
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:446
msgid "Right"
msgstr ""

#: dist/translation-strings.php:549
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Top"
msgstr ""

#: dist/translation-strings.php:550
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Bottom"
msgstr ""

#: dist/translation-strings.php:551
#: dist/editor_blocks.js:2
msgid "Are you sure you want to delete this tab?"
msgstr ""

#: dist/translation-strings.php:552
#: dist/editor_blocks.js:2
msgid "Tab Label"
msgstr ""

#: dist/translation-strings.php:553
#: dist/editor_blocks.js:2
msgid "Move left"
msgstr ""

#: dist/translation-strings.php:554
#: dist/editor_blocks.js:2
msgid "Move right"
msgstr ""

#: dist/translation-strings.php:555
#: dist/editor_blocks.js:2
msgid "Add tab"
msgstr ""

#: dist/translation-strings.php:556
#: dist/editor_blocks.js:2
msgid "Duplicate tab"
msgstr ""

#: dist/translation-strings.php:557
#: dist/editor_blocks.js:2
msgid "Delete tab"
msgstr ""

#: dist/translation-strings.php:558
#: dist/editor_blocks.js:2
msgid "Fixed Icon Position"
msgstr ""

#: dist/translation-strings.php:559
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Icon Position"
msgstr ""

#: dist/translation-strings.php:560
#: dist/editor_blocks.js:2
msgid "Tab Alignment"
msgstr ""

#: dist/translation-strings.php:561
#: dist/editor_blocks.js:2
msgid "Text Alignment"
msgstr ""

#: dist/translation-strings.php:562
#: dist/editor_blocks.js:2
msgid "Scrollable Tabs on Mobile"
msgstr ""

#: dist/translation-strings.php:563
#: dist/editor_blocks.js:2
msgid "Tab"
msgstr ""

#: dist/translation-strings.php:564
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Text Color"
msgstr ""

#: dist/translation-strings.php:565
#: dist/editor_blocks.js:2
msgid "Tab Active State"
msgstr ""

#: dist/translation-strings.php:566
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Typography"
msgstr ""

#: dist/translation-strings.php:567
#: dist/editor_blocks.js:2
msgid "Change icons individually by clicking on each tab's icon."
msgstr ""

#: dist/translation-strings.php:568
#: dist/editor_blocks.js:2
msgid "Tab Anchors"
msgstr ""

#: dist/translation-strings.php:569
#: dist/editor_blocks.js:2
msgid "Assign unique anchor names to each tab so you'll be able to link directly and open each one."
msgstr ""

#: dist/translation-strings.php:570
#: dist/editor_blocks.js:2
msgid "%s Anchor"
msgstr ""

#: dist/translation-strings.php:571
#: dist/editor_blocks.js:2
msgid "Tab Anchor"
msgstr ""

#: dist/translation-strings.php:572
#: dist/editor_blocks.js:2
msgid "Unordered"
msgstr ""

#: dist/translation-strings.php:573
#: dist/editor_blocks.js:2
msgctxt "%s is a heading level, e.g. H1"
msgid "Include %s"
msgstr ""

#: dist/translation-strings.php:574
#: dist/editor_blocks.js:2
msgid "Auto-generate missing anchor ids"
msgstr ""

#: dist/translation-strings.php:575
#: dist/editor_blocks.js:2
msgid "Scrolling"
msgstr ""

#: dist/translation-strings.php:576
#: dist/editor_blocks.js:2
msgid "Use smooth scroll"
msgstr ""

#: dist/translation-strings.php:577
#: dist/editor_blocks.js:2
msgid "Scroll Top Offset "
msgstr ""

#: dist/translation-strings.php:578
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Introduction"
msgstr ""

#: dist/translation-strings.php:579
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Chapter 1: Abstract"
msgstr ""

#: dist/translation-strings.php:580
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Chapter 2: History"
msgstr ""

#: dist/translation-strings.php:581
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Chapter 3: Main Content"
msgstr ""

#: dist/translation-strings.php:582
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Chapter 4: Additional Thoughts"
msgstr ""

#: dist/translation-strings.php:583
#: dist/editor_blocks.js:2
msgctxt "Table of Contents example text"
msgid "Conclusion"
msgstr ""

#: dist/translation-strings.php:584
#: src/stk-block-types.php:971
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:581
msgid "Table of Contents"
msgstr ""

#: dist/translation-strings.php:585
#: dist/editor_blocks.js:2
msgid "Include heading"
msgstr ""

#: dist/translation-strings.php:586
#: dist/editor_blocks.js:2
msgid "Exclude heading"
msgstr ""

#: dist/translation-strings.php:587
#: src/stk-block-types.php:427
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:519
msgid "Heading"
msgstr ""

#: dist/translation-strings.php:588
#: dist/editor_blocks.js:2
msgid "Tab %d"
msgstr ""

#: dist/translation-strings.php:589
#: src/stk-block-types.php:992
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:586
msgid "Tabs"
msgstr ""

#: dist/translation-strings.php:590
#: dist/editor_blocks.js:2
msgid "Initial Tab Open"
msgstr ""

#: dist/translation-strings.php:591
#: dist/editor_blocks.js:2
msgid "Equal tab height"
msgstr ""

#: dist/translation-strings.php:592
#: dist/editor_blocks.js:2
msgid "Tab Orientation"
msgstr ""

#: dist/translation-strings.php:593
#: dist/editor_blocks.js:2
msgid "Tab Panel Offset"
msgstr ""

#: dist/translation-strings.php:594
#: dist/editor_blocks.js:2
msgid "Tab Panel Effect"
msgstr ""

#: dist/translation-strings.php:595
#: dist/editor_blocks.js:2
msgid "Immediate"
msgstr ""

#: dist/translation-strings.php:596
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:177
msgid "Name"
msgstr ""

#: dist/translation-strings.php:597
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:178
msgid "Position"
msgstr ""

#: dist/translation-strings.php:598
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Cover"
msgstr ""

#: dist/translation-strings.php:599
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Bubble"
msgstr ""

#: dist/translation-strings.php:600
#: dist/editor_blocks.js:2
msgid "Inverted Vertical"
msgstr ""

#: dist/translation-strings.php:601
#: dist/editor_blocks.js:2
msgid "Text for this block. Use this space for describing your block. Any text will do. Text for this block. You can use this space for describing your block."
msgstr ""

#: dist/translation-strings.php:602
#: dist/admin_welcome.js:2
msgid "Align Left"
msgstr ""

#: dist/translation-strings.php:603
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Align Center"
msgstr ""

#: dist/translation-strings.php:604
#: dist/admin_welcome.js:2
msgid "Align Right"
msgstr ""

#: dist/translation-strings.php:605
#: dist/admin_welcome.js:2
msgid "Justified"
msgstr ""

#: dist/translation-strings.php:606
msgid "Content Min. Height"
msgstr ""

#: dist/translation-strings.php:607
msgid "Content Max Width"
msgstr ""

#: dist/translation-strings.php:608
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Content Horizontal Align"
msgstr ""

#: dist/translation-strings.php:609
msgid "Sets the placement of the column container to left, center or right. Not available when block width is set to full width."
msgstr ""

#: dist/translation-strings.php:610
msgid "Adjusts the placement of all content in the block to align left, center or right"
msgstr ""

#: dist/translation-strings.php:611
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:437
msgid "Column"
msgstr ""

#: dist/translation-strings.php:612
msgid "Sets the horizontal position and spacing of the inner columns."
msgstr ""

#: dist/translation-strings.php:613
msgid "Column contents need to be narrow for effect to show."
msgstr ""

#: dist/translation-strings.php:614
msgid "Sets the vertical position of the inner columns relative to the columns block."
msgstr ""

#: dist/translation-strings.php:615
msgid "%s Direction"
msgstr ""

#: dist/translation-strings.php:616
msgid "Inner Block"
msgstr ""

#: dist/translation-strings.php:617
msgid "Sets the horizontal/vertical position and spacing of the inner blocks."
msgstr ""

#: dist/translation-strings.php:618
msgid "Aligns the horizontal/vertical position of the inner blocks."
msgstr ""

#: dist/translation-strings.php:619
msgid "Set Content Min. Height for alignment to display properly"
msgstr ""

#: dist/translation-strings.php:620
msgid "Inner Block Wrapping"
msgstr ""

#: dist/translation-strings.php:621
msgid "%s %s"
msgstr ""

#: dist/translation-strings.php:622
msgid "Sets the gap between the columns of inner blocks."
msgstr ""

#: dist/translation-strings.php:623
msgid "Sets the gap between inner blocks."
msgstr ""

#: dist/translation-strings.php:624
msgid "Sets the gap between the rows of inner blocks."
msgstr ""

#: dist/translation-strings.php:625
#: dist/editor_blocks.js:2
msgid "Play Video"
msgstr ""

#: dist/translation-strings.php:626
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Popup Option #1: Upload Video"
msgstr ""

#: dist/translation-strings.php:627
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Use .mp4 format for videos"
msgstr ""

#: dist/translation-strings.php:628
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Popup Option #2: Video URL"
msgstr ""

#: dist/translation-strings.php:629
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Paste a Youtube / Vimeo URL"
msgstr ""

#: dist/translation-strings.php:630
#: dist/editor_blocks.js:2
msgid "Allow fullscreen"
msgstr ""

#: dist/translation-strings.php:631
#: dist/editor_blocks.js:2
msgid "Allow download video"
msgstr ""

#: dist/translation-strings.php:632
#: dist/editor_blocks.js:2
msgid "Loop video"
msgstr ""

#: dist/translation-strings.php:633
msgid "Block Size & Spacing"
msgstr ""

#: dist/translation-strings.php:634
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Background"
msgstr ""

#: dist/translation-strings.php:635
#: dist/editor_blocks.js:2
msgid "Color Scheme"
msgstr ""

#: dist/translation-strings.php:636
msgid "Borders & Shadows"
msgstr ""

#: dist/translation-strings.php:637
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Address"
msgstr ""

#: dist/translation-strings.php:638
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Article"
msgstr ""

#: dist/translation-strings.php:639
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Aside"
msgstr ""

#: dist/translation-strings.php:640
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Blockquote"
msgstr ""

#: dist/translation-strings.php:641
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Div"
msgstr ""

#: dist/translation-strings.php:642
msgctxt "HTML Tag"
msgid "Details"
msgstr ""

#: dist/translation-strings.php:643
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Footer"
msgstr ""

#: dist/translation-strings.php:644
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Header"
msgstr ""

#: dist/translation-strings.php:645
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Hgroup"
msgstr ""

#: dist/translation-strings.php:646
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Main"
msgstr ""

#: dist/translation-strings.php:647
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Nav"
msgstr ""

#: dist/translation-strings.php:648
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "HTML Tag"
msgid "Section"
msgstr ""

#: dist/translation-strings.php:649
msgctxt "HTML Tag"
msgid "Summary"
msgstr ""

#: dist/translation-strings.php:650
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "component"
msgid "%s HTML Tag"
msgstr ""

#: dist/translation-strings.php:651
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block"
msgstr ""

#: dist/translation-strings.php:652
msgid "Overflow"
msgstr ""

#: dist/translation-strings.php:653
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Auto"
msgstr ""

#: dist/translation-strings.php:654
msgid "Scroll"
msgstr ""

#: dist/translation-strings.php:655
msgid "Visible"
msgstr ""

#: dist/translation-strings.php:656
msgid "Clear"
msgstr ""

#: dist/translation-strings.php:657
msgid "Both"
msgstr ""

#: dist/translation-strings.php:658
msgid "Adjusts the transparency of the entire block"
msgstr ""

#: dist/translation-strings.php:659
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Z-Index"
msgstr ""

#: dist/translation-strings.php:660
msgid "Sets the stack order of different blocks to make one appear in front of another. A block with a higher z-index will show up on top of another block with a lower z-index."
msgstr ""

#: dist/translation-strings.php:661
msgid "Sticky position may not work across all themes"
msgstr ""

#: dist/translation-strings.php:662
msgid "Static"
msgstr ""

#: dist/translation-strings.php:663
msgid "Relative"
msgstr ""

#: dist/translation-strings.php:664
msgid "Absolute"
msgstr ""

#: dist/translation-strings.php:665
msgid "Sticky"
msgstr ""

#: dist/translation-strings.php:666
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:143
msgctxt "Content placeholder"
msgid "Description for this block. Use this space for describing your block. Any text will do."
msgstr ""

#: dist/translation-strings.php:667
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:348
msgid "Single"
msgstr ""

#: dist/translation-strings.php:668
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:210
msgid "Gradient"
msgstr ""

#: dist/translation-strings.php:669
#: dist/editor_blocks.js:2
msgid "Content Position"
msgstr ""

#: dist/translation-strings.php:670
#: src/stk-block-types.php:1091
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:595
msgid "Timeline"
msgstr ""

#: dist/translation-strings.php:671
#: dist/editor_blocks.js:2
msgid "Accent Anchor Position"
msgstr ""

#: dist/translation-strings.php:672
#: dist/editor_blocks.js:2
msgid "Succeeding timeline blocks will also use this value."
msgstr ""

#: dist/translation-strings.php:673
#: dist/editor_blocks.js:2
msgid "%s Border Radius"
msgstr ""

#: dist/translation-strings.php:674
#: dist/editor_blocks.js:2
msgid "Line Thickness"
msgstr ""

#: dist/translation-strings.php:675
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:206
msgid "Horizontal Offset"
msgstr ""

#: dist/translation-strings.php:676
#: dist/editor_blocks.js:2
msgctxt "option title"
msgid "%s #%d"
msgstr ""

#: dist/translation-strings.php:677
#: dist/editor_blocks.js:2
msgid "Timeline Accent Color"
msgstr ""

#: dist/translation-strings.php:678
#: dist/editor_blocks.js:2
msgid "Timeline Background Color"
msgstr ""

#: dist/translation-strings.php:679
msgid "Supports links to images, videos, YouTube, Vimeo, and web pages that allow embedding. Opens inner image block if no link is provided"
msgstr ""

#: dist/translation-strings.php:680
msgid "Styles"
msgstr ""

#: dist/translation-strings.php:681
msgid "Conditional Display"
msgstr ""

#: dist/translation-strings.php:682
msgid "When enabled, the last column will be cloned instead of adding a blank column."
msgstr ""

#: dist/translation-strings.php:683
#: src/welcome/index.php:119
#: src/welcome/index.php:284
#: dist/admin_welcome.js:2
msgid "Settings"
msgstr ""

#: dist/translation-strings.php:684
msgid "Allow Column Wrapping"
msgstr ""

#: dist/translation-strings.php:685
#: dist/editor_blocks.js:2
msgid "Sets column paddings, the space inside the block between the block elements and the column container border"
msgstr ""

#: dist/translation-strings.php:686
#: dist/editor_blocks.js:2
msgid "Sets the distance between two or more columns"
msgstr ""

#: dist/translation-strings.php:687
msgid "Block Width"
msgstr ""

#: dist/translation-strings.php:688
msgid "Align Wide"
msgstr ""

#: dist/translation-strings.php:689
msgid "Align Full"
msgstr ""

#: dist/translation-strings.php:690
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Content Width"
msgstr ""

#: dist/translation-strings.php:691
msgid "Set the content to be smaller than the block width"
msgstr ""

#: dist/translation-strings.php:692
#: src/stk-block-types.php:252
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:461
msgid "Container"
msgstr ""

#: dist/translation-strings.php:693
msgid "Container Min. Height"
msgstr ""

#: dist/translation-strings.php:694
msgid "Max Container Width"
msgstr ""

#: dist/translation-strings.php:695
msgid "Container Horizontal Align"
msgstr ""

#: dist/translation-strings.php:696
msgid "Container Vertical Align"
msgstr ""

#: dist/translation-strings.php:697
msgid "Container Background"
msgstr ""

#: dist/translation-strings.php:698
msgid "Trigger hover state on nested blocks"
msgstr ""

#: dist/translation-strings.php:699
msgid "Container Borders & Shadow"
msgstr ""

#: dist/translation-strings.php:700
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Custom Attributes"
msgstr ""

#: dist/translation-strings.php:701
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:197
msgid "Custom CSS"
msgstr ""

#: dist/translation-strings.php:702
#: src/welcome/index.php:205
msgid "Motion Effects"
msgstr ""

#: dist/translation-strings.php:703
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:203
msgid "Shape Color"
msgstr ""

#: dist/translation-strings.php:704
msgid "Shape Border Radius"
msgstr ""

#: dist/translation-strings.php:705
msgid "Shape Padding"
msgstr ""

#: dist/translation-strings.php:706
msgid "Shape Outline Width"
msgstr ""

#: dist/translation-strings.php:707
msgid "Shape Outline Color"
msgstr ""

#: dist/translation-strings.php:708
msgid "Icon Shape"
msgstr ""

#: dist/translation-strings.php:709
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:200
msgid "Background Shape"
msgstr ""

#: dist/translation-strings.php:710
msgid "Select Image"
msgstr ""

#: dist/translation-strings.php:711
msgid "Image Url"
msgstr ""

#: dist/translation-strings.php:712
msgid "Aspect Ratio"
msgstr ""

#: dist/translation-strings.php:713
msgid "Original"
msgstr ""

#: dist/translation-strings.php:714
msgid "Square 1:1"
msgstr ""

#: dist/translation-strings.php:715
msgid "Standard 4:3"
msgstr ""

#: dist/translation-strings.php:716
msgid "Classic 3:2"
msgstr ""

#: dist/translation-strings.php:717
msgid "Wide 16:9"
msgstr ""

#: dist/translation-strings.php:718
msgid "Cinematic 2:1"
msgstr ""

#: dist/translation-strings.php:719
msgid "Ultra Wide 3:1"
msgstr ""

#: dist/translation-strings.php:720
msgid "Panoramic 4:1"
msgstr ""

#: dist/translation-strings.php:721
msgid "Portrait 3:4"
msgstr ""

#: dist/translation-strings.php:722
msgid "Classic Portrait 2:3"
msgstr ""

#: dist/translation-strings.php:723
msgid "Tall 9:16"
msgstr ""

#: dist/translation-strings.php:724
msgid "Image width"
msgstr ""

#: dist/translation-strings.php:725
msgid "Adjusts the image width"
msgstr ""

#: dist/translation-strings.php:726
msgid "Image height"
msgstr ""

#: dist/translation-strings.php:727
msgid "Adjusts the image height"
msgstr ""

#: dist/translation-strings.php:728
msgid "Open Image in Lightbox"
msgstr ""

#: dist/translation-strings.php:729
msgid "Image Alt"
msgstr ""

#: dist/translation-strings.php:730
msgid "Show Empty Alt Attribute"
msgstr ""

#: dist/translation-strings.php:731
#: dist/editor_blocks.js:2
msgid "Image Shadow"
msgstr ""

#: dist/translation-strings.php:732
#: dist/editor_blocks.js:2
msgid "Adjusts the intensity of the image shadow"
msgstr ""

#: dist/translation-strings.php:733
#: dist/editor_blocks.js:2
msgid "Adjusts the radius of image corners to make them more rounded"
msgstr ""

#: dist/translation-strings.php:734
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay Color"
msgstr ""

#: dist/translation-strings.php:735
msgid "Overlay Opacity"
msgstr ""

#: dist/translation-strings.php:736
msgid "Overlay Blend Mode"
msgstr ""

#: dist/translation-strings.php:737
msgid "Sets how the overlay color blends with the image"
msgstr ""

#: dist/translation-strings.php:738
msgid "Focal point"
msgstr ""

#: dist/translation-strings.php:739
msgid "Image Fit"
msgstr ""

#: dist/translation-strings.php:740
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Contain"
msgstr ""

#: dist/translation-strings.php:741
msgid "Fill"
msgstr ""

#: dist/translation-strings.php:742
msgid "Scale Down"
msgstr ""

#: dist/translation-strings.php:743
msgid "Sets the fit to default, contain, cover, fill, none, and scale down. Image fit specifies how an image resizes in a container."
msgstr ""

#: dist/translation-strings.php:744
msgid "Image Shape"
msgstr ""

#: dist/translation-strings.php:745
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:202
msgid "Change the shape of the image"
msgstr ""

#: dist/translation-strings.php:746
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Flip Shape Horizontally"
msgstr ""

#: dist/translation-strings.php:747
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Flip Shape Vertically"
msgstr ""

#: dist/translation-strings.php:748
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Stretch Shape Mask"
msgstr ""

#: dist/translation-strings.php:749
msgid "Image Filter"
msgstr ""

#: dist/translation-strings.php:750
#: src/stk-block-types.php:632
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:432
msgid "Image"
msgstr ""

#: dist/translation-strings.php:751
msgid "When linked, styling this block would also style other linked blocks in adjacent columns."
msgstr ""

#: dist/translation-strings.php:752
msgid "Learn more about linking"
msgstr ""

#: dist/translation-strings.php:753
#: src/stk-block-types.php:856
#: dist/translation-strings.js:568
msgid "Progress Circle"
msgstr ""

#: dist/translation-strings.php:754
#: src/stk-block-types.php:837
#: dist/translation-strings.js:565
msgid "Progress Bar"
msgstr ""

#: dist/translation-strings.php:755
msgid "Progress"
msgstr ""

#: dist/translation-strings.php:756
msgid "Maximum Progress"
msgstr ""

#: dist/translation-strings.php:757
msgid "Apply border radius to bar"
msgstr ""

#: dist/translation-strings.php:758
msgid "Thickness"
msgstr ""

#: dist/translation-strings.php:759
msgid "Bar Color #%s"
msgstr ""

#: dist/translation-strings.php:760
msgid "Bar Color"
msgstr ""

#: dist/translation-strings.php:761
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:209
msgid "Gradient Direction (degrees)"
msgstr ""

#: dist/translation-strings.php:762
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Adv. Gradient Color Settings"
msgstr ""

#: dist/translation-strings.php:763
msgid "Gradient Direction"
msgstr ""

#: dist/translation-strings.php:764
msgid "Sets the direction (in degrees) of the colors"
msgstr ""

#: dist/translation-strings.php:765
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Color %d Location"
msgstr ""

#: dist/translation-strings.php:766
msgid "Sets the placement of each color in relation to the other color"
msgstr ""

#: dist/translation-strings.php:767
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Gradient Blend Mode"
msgstr ""

#: dist/translation-strings.php:768
msgid "Sets how this background gradient/image blends into the other background"
msgstr ""

#: dist/translation-strings.php:769
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Background Color"
msgstr ""

#: dist/translation-strings.php:770
#: dist/admin_welcome.js:2
msgid "Rounded"
msgstr ""

#: dist/translation-strings.php:771
msgid "Animate"
msgstr ""

#: dist/translation-strings.php:772
msgid "Accessibility Label"
msgstr ""

#: dist/translation-strings.php:773
msgid "Progress Bar Text"
msgstr ""

#: dist/translation-strings.php:774
msgid "Progress Prefix"
msgstr ""

#: dist/translation-strings.php:775
msgid "Progress Suffix"
msgstr ""

#: dist/translation-strings.php:776
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Responsive"
msgstr ""

#: dist/translation-strings.php:777
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Hide on Desktop"
msgstr ""

#: dist/translation-strings.php:778
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Hide on Tablet"
msgstr ""

#: dist/translation-strings.php:779
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Hide on Mobile"
msgstr ""

#: dist/translation-strings.php:780
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:455
msgctxt "Nth Title"
msgid "%s %d"
msgstr ""

#: dist/translation-strings.php:781
#: dist/admin_welcome.js:2
msgid "Wave"
msgstr ""

#: dist/translation-strings.php:782
#: dist/admin_welcome.js:2
msgid "Straight"
msgstr ""

#: dist/translation-strings.php:783
#: dist/admin_welcome.js:2
msgid "Slant"
msgstr ""

#: dist/translation-strings.php:784
#: dist/admin_welcome.js:2
msgid "Curve"
msgstr ""

#: dist/translation-strings.php:785
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Design"
msgstr ""

#: dist/translation-strings.php:786
msgid "Separator Height"
msgstr ""

#: dist/translation-strings.php:787
msgid "Adjusts the height of the separator to stretch or compress vertically"
msgstr ""

#: dist/translation-strings.php:788
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Separator Width"
msgstr ""

#: dist/translation-strings.php:789
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:460
msgid "Shadow / Outline"
msgstr ""

#: dist/translation-strings.php:790
msgid "Adjusts the intensity of the separator shadow and makes the separator more prominent"
msgstr ""

#: dist/translation-strings.php:791
msgid "Invert Design"
msgstr ""

#: dist/translation-strings.php:792
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Bring to Front"
msgstr ""

#: dist/translation-strings.php:793
msgid "Brings the separator layer in front of other block elements"
msgstr ""

#: dist/translation-strings.php:794
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Top Separator"
msgstr ""

#: dist/translation-strings.php:795
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Bottom Separator"
msgstr ""

#: dist/translation-strings.php:796
msgid "Transform & Transition"
msgstr ""

#: dist/translation-strings.php:797
#: dist/admin_welcome.js:2
msgid "Darken"
msgstr ""

#: dist/translation-strings.php:798
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:405
msgid "Lift"
msgstr ""

#: dist/translation-strings.php:799
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:219
msgid "Scale"
msgstr ""

#: dist/translation-strings.php:800
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Lift & Scale"
msgstr ""

#: dist/translation-strings.php:801
msgid "Lift More"
msgstr ""

#: dist/translation-strings.php:802
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Scale More"
msgstr ""

#: dist/translation-strings.php:803
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Lift & Scale More"
msgstr ""

#: dist/translation-strings.php:804
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:413
msgid "Hover Effect"
msgstr ""

#: dist/translation-strings.php:805
msgid "Hover effect"
msgstr ""

#: dist/translation-strings.php:806
msgid "Triggers animation or effects when you mouse over"
msgstr ""

#: dist/translation-strings.php:807
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
msgid "Button Color"
msgstr ""

#: dist/translation-strings.php:808
msgid "Button Colors"
msgstr ""

#: dist/translation-strings.php:809
#: dist/editor_blocks.js:2
msgid "Min. Button Height"
msgstr ""

#: dist/translation-strings.php:810
msgid "Button Width"
msgstr ""

#: dist/translation-strings.php:811
#: dist/editor_blocks.js:2
msgid "Button Padding"
msgstr ""

#: dist/translation-strings.php:812
#: dist/editor_blocks.js:2
msgid "Button padding"
msgstr ""

#: dist/translation-strings.php:813
#: dist/editor_blocks.js:2
msgid "Adjusts the space between the button text and button borders"
msgstr ""

#: dist/translation-strings.php:814
msgid "Button Size & Spacing"
msgstr ""

#: dist/translation-strings.php:815
msgid "Button Borders & Shadows"
msgstr ""

#: dist/translation-strings.php:816
msgid "Use theme heading margins"
msgstr ""

#: dist/translation-strings.php:817
#: dist/admin_welcome.js:2
msgid "Font Family"
msgstr ""

#: dist/translation-strings.php:818
msgid "Theme Default"
msgstr ""

#: dist/translation-strings.php:819
msgid "Sets the font set to be used for the element"
msgstr ""

#: dist/translation-strings.php:820
msgid "Weight"
msgstr ""

#: dist/translation-strings.php:821
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:227
msgid "Normal"
msgstr ""

#: dist/translation-strings.php:822
msgid "Bold"
msgstr ""

#: dist/translation-strings.php:823
msgid "Font weight"
msgstr ""

#: dist/translation-strings.php:824
msgid "Sets the thinness or thickness of text characters"
msgstr ""

#: dist/translation-strings.php:825
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:262
msgid "Transform"
msgstr ""

#: dist/translation-strings.php:826
msgid "Uppercase"
msgstr ""

#: dist/translation-strings.php:827
msgid "Lowercase"
msgstr ""

#: dist/translation-strings.php:828
msgid "Capitalize"
msgstr ""

#: dist/translation-strings.php:829
msgid "Sets the usage of upper or lower case"
msgstr ""

#: dist/translation-strings.php:830
msgid "Font Style"
msgstr ""

#: dist/translation-strings.php:831
msgid "Italic"
msgstr ""

#: dist/translation-strings.php:832
msgid "Oblique"
msgstr ""

#: dist/translation-strings.php:833
msgid "Line-Height"
msgstr ""

#: dist/translation-strings.php:834
msgid "Line height"
msgstr ""

#: dist/translation-strings.php:835
msgid "Sets the vertical distance between lines of text"
msgstr ""

#: dist/translation-strings.php:836
msgid "Letter Spacing"
msgstr ""

#: dist/translation-strings.php:837
msgid "Letter spacing"
msgstr ""

#: dist/translation-strings.php:838
msgid "Sets the distance or space between letters"
msgstr ""

#: dist/translation-strings.php:839
msgid "Font size"
msgstr ""

#: dist/translation-strings.php:840
msgid "Sets the size of text characters"
msgstr ""

#: dist/translation-strings.php:841
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:310
msgid "Remove"
msgstr ""

#: dist/translation-strings.php:842
msgid "#%s (no title)"
msgstr ""

#: dist/translation-strings.php:843
msgid "No posts found"
msgstr ""

#: dist/translation-strings.php:844
msgid "Pick a post"
msgstr ""

#: dist/translation-strings.php:845
#: dist/admin_welcome.js:2
msgid "view demo"
msgstr ""

#: dist/translation-strings.php:846
#: dist/admin_welcome.js:2
msgid "No matches found"
msgstr ""

#: dist/translation-strings.php:847
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:60
msgid "Reset"
msgstr ""

#: dist/translation-strings.php:848
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Start"
msgstr ""

#: dist/translation-strings.php:849
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Center"
msgstr ""

#: dist/translation-strings.php:850
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "End"
msgstr ""

#: dist/translation-strings.php:851
#: dist/admin_welcome.js:2
msgid "Space Between"
msgstr ""

#: dist/translation-strings.php:852
#: dist/admin_welcome.js:2
msgid "Space Around"
msgstr ""

#: dist/translation-strings.php:853
#: dist/admin_welcome.js:2
msgid "Space Evenly"
msgstr ""

#: dist/translation-strings.php:854
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Stretch"
msgstr ""

#: dist/translation-strings.php:855
#: dist/admin_welcome.js:2
msgid "Baseline"
msgstr ""

#: dist/translation-strings.php:856
#: dist/admin_welcome.js:2
msgid "Normal State"
msgstr ""

#: dist/translation-strings.php:857
#: dist/admin_welcome.js:2
msgid "Hovered State"
msgstr ""

#: dist/translation-strings.php:858
#: dist/admin_welcome.js:2
msgid "Parent Container Hovered State"
msgstr ""

#: dist/translation-strings.php:859
#: dist/admin_welcome.js:2
msgid "Collapsed"
msgstr ""

#: dist/translation-strings.php:860
#: dist/admin_welcome.js:2
msgid "Parent Hovered"
msgstr ""

#: dist/translation-strings.php:861
#: dist/admin_welcome.js:2
msgid "Add a Container Background to a parent block to enable this state."
msgstr ""

#: dist/translation-strings.php:862
#: dist/admin_welcome.js:2
msgid "Unit"
msgstr ""

#: dist/translation-strings.php:863
#: dist/admin_welcome.js:2
msgid "Multiply"
msgstr ""

#: dist/translation-strings.php:864
#: dist/admin_welcome.js:2
msgid "Screen"
msgstr ""

#: dist/translation-strings.php:865
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay"
msgstr ""

#: dist/translation-strings.php:866
#: dist/admin_welcome.js:2
msgid "Lighten"
msgstr ""

#: dist/translation-strings.php:867
#: dist/admin_welcome.js:2
msgid "Color Dodge"
msgstr ""

#: dist/translation-strings.php:868
#: dist/admin_welcome.js:2
msgid "Color Burn"
msgstr ""

#: dist/translation-strings.php:869
#: dist/admin_welcome.js:2
msgid "Hard Light"
msgstr ""

#: dist/translation-strings.php:870
#: dist/admin_welcome.js:2
msgid "Soft Light"
msgstr ""

#: dist/translation-strings.php:871
#: dist/admin_welcome.js:2
msgid "Difference"
msgstr ""

#: dist/translation-strings.php:872
#: dist/admin_welcome.js:2
msgid "Exclusion"
msgstr ""

#: dist/translation-strings.php:873
#: dist/admin_welcome.js:2
msgid "Hue"
msgstr ""

#: dist/translation-strings.php:874
#: dist/admin_welcome.js:2
msgid "Saturation"
msgstr ""

#: dist/translation-strings.php:875
#: dist/admin_welcome.js:2
msgid "Luminosity"
msgstr ""

#: dist/translation-strings.php:876
#: dist/admin_welcome.js:2
msgid "Initial"
msgstr ""

#: dist/translation-strings.php:877
#: dist/admin_welcome.js:2
msgid "Inherit"
msgstr ""

#: dist/translation-strings.php:878
#: dist/admin_welcome.js:2
msgid "Unset"
msgstr ""

#: dist/translation-strings.php:879
#: dist/admin_welcome.js:2
msgid "Mix Blend Mode"
msgstr ""

#: dist/translation-strings.php:880
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Edit"
msgstr ""

#: dist/translation-strings.php:881
#: dist/admin_welcome.js:2
msgid "Global Gradients"
msgstr ""

#: dist/translation-strings.php:882
#: dist/admin_welcome.js:2
#: dist/editor_blocks.js:2
msgid "Global Colors"
msgstr ""

#: dist/translation-strings.php:883
#: dist/admin_welcome.js:2
msgid "Add Column"
msgstr ""

#: dist/translation-strings.php:884
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Column Widths"
msgstr ""

#: dist/translation-strings.php:885
msgid "Adjust the individual widths of each column"
msgstr ""

#: dist/translation-strings.php:886
msgid "You can type in custom HTML attributes for this block in the field above."
msgstr ""

#: dist/translation-strings.php:887
msgid "Example:"
msgstr ""

#: dist/translation-strings.php:888
msgid "Learn more about Custom Attributes"
msgstr ""

#: dist/translation-strings.php:889
msgid "There is an error in your custom attribute"
msgstr ""

#: dist/translation-strings.php:890
#: dist/admin_welcome.js:2
msgid "Group into Container"
msgstr ""

#: dist/translation-strings.php:891
#: dist/admin_welcome.js:2
msgid "Ungroup from Container"
msgstr ""

#: dist/translation-strings.php:892
#: dist/admin_welcome.js:2
msgid "No designs found"
msgstr ""

#: dist/translation-strings.php:893
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Layout"
msgstr ""

#: dist/translation-strings.php:894
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:305
msgid "Dynamic Fields"
msgstr ""

#: dist/translation-strings.php:895
msgid "Default Heading"
msgstr ""

#: dist/translation-strings.php:896
msgid "Default Body"
msgstr ""

#: dist/translation-strings.php:897
#: dist/admin_welcome.js:2
msgid "Font Size"
msgstr ""

#: dist/translation-strings.php:898
#: dist/admin_welcome.js:2
msgid "Individual sides"
msgstr ""

#: dist/translation-strings.php:899
#: dist/admin_welcome.js:2
msgid "All sides"
msgstr ""

#: dist/translation-strings.php:900
#: dist/admin_welcome.js:2
msgid "Top and Bottom"
msgstr ""

#: dist/translation-strings.php:901
#: dist/admin_welcome.js:2
msgid "Left and Right"
msgstr ""

#: dist/translation-strings.php:902
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:264
msgid "Top Left"
msgstr ""

#: dist/translation-strings.php:903
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:266
msgid "Top Right"
msgstr ""

#: dist/translation-strings.php:904
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:270
msgid "Bottom Left"
msgstr ""

#: dist/translation-strings.php:905
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:272
msgid "Bottom Right"
msgstr ""

#: dist/translation-strings.php:906
msgid "There are no blocks in this group, please add one."
msgstr ""

#: dist/translation-strings.php:907
msgid "Add Block"
msgstr ""

#: dist/translation-strings.php:908
#: dist/admin_welcome.js:2
msgid "Stop showing tooltips"
msgstr ""

#: dist/translation-strings.php:909
#: src/stk-block-types.php:1082
#: dist/admin_welcome.js:2
#: dist/translation-strings.js:594
msgid "Paragraph"
msgstr ""

#: dist/translation-strings.php:910
#: dist/admin_welcome.js:2
msgid "auto"
msgstr ""

#: dist/translation-strings.php:911
#: dist/admin_welcome.js:2
msgid "Type to search icon"
msgstr ""

#: dist/translation-strings.php:912
#: dist/admin_welcome.js:2
msgid "Icon Settings"
msgstr ""

#: dist/translation-strings.php:913
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:376
msgid "Upload SVG"
msgstr ""

#: dist/translation-strings.php:914
#: dist/admin_welcome.js:2
msgid "Clear icon"
msgstr ""

#: dist/translation-strings.php:915
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:375
msgid "Drop your SVG here"
msgstr ""

#: dist/translation-strings.php:916
msgid "Describe the purpose of the image"
msgstr ""

#: dist/translation-strings.php:917
msgid "Leave empty if the image is purely decorative."
msgstr ""

#: dist/translation-strings.php:918
#: dist/admin_welcome.js:2
msgid "Alt Text (Alternative Text)"
msgstr ""

#: dist/translation-strings.php:919
#: dist/admin_welcome.js:2
msgid "preview"
msgstr ""

#: dist/translation-strings.php:920
#: dist/admin_welcome.js:2
msgid "Upload"
msgstr ""

#: dist/translation-strings.php:921
#: dist/admin_welcome.js:2
msgid "Replace"
msgstr ""

#: dist/translation-strings.php:922
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:241
msgid "Blur"
msgstr ""

#: dist/translation-strings.php:923
#: dist/admin_welcome.js:2
msgid "Brightness"
msgstr ""

#: dist/translation-strings.php:924
#: dist/admin_welcome.js:2
msgid "Contrast"
msgstr ""

#: dist/translation-strings.php:925
#: dist/admin_welcome.js:2
msgid "Grayscale"
msgstr ""

#: dist/translation-strings.php:926
#: dist/admin_welcome.js:2
msgid "Hue Rotate"
msgstr ""

#: dist/translation-strings.php:927
#: dist/admin_welcome.js:2
msgid "Invert"
msgstr ""

#: dist/translation-strings.php:928
#: dist/admin_welcome.js:2
msgid "Saturate"
msgstr ""

#: dist/translation-strings.php:929
#: dist/admin_welcome.js:2
msgid "Sepia"
msgstr ""

#: dist/translation-strings.php:930
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Circle"
msgstr ""

#: dist/translation-strings.php:931
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:344
msgid "Blob %s"
msgstr ""

#: dist/translation-strings.php:932
msgid "Click on any inner block in the editor to style it."
msgstr ""

#: dist/translation-strings.php:933
msgid "Back"
msgstr ""

#: dist/translation-strings.php:934
msgid "Duplicate"
msgstr ""

#: dist/translation-strings.php:935
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:181
msgid "Delete"
msgstr ""

#: dist/translation-strings.php:936
#: dist/admin_welcome.js:2
msgid "System Fonts"
msgstr ""

#: dist/translation-strings.php:937
#: dist/admin_welcome.js:2
msgid "Modern Font Stacks"
msgstr ""

#: dist/translation-strings.php:938
#: dist/admin_welcome.js:2
msgid "Google Fonts"
msgstr ""

#: dist/translation-strings.php:939
#: dist/admin_welcome.js:2
msgid "Theme Fonts"
msgstr ""

#: dist/translation-strings.php:940
msgid "Looking for other settings? They've moved to the %s tab."
msgstr ""

#: dist/translation-strings.php:941
msgid "style"
msgstr ""

#: dist/translation-strings.php:942
msgid "Dismiss hint"
msgstr ""

#: dist/translation-strings.php:943
msgid "layout"
msgstr ""

#: dist/translation-strings.php:944
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "All"
msgstr ""

#: dist/translation-strings.php:945
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Designs"
msgstr ""

#: dist/translation-strings.php:946
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "UI Kits"
msgstr ""

#: dist/translation-strings.php:947
#: dist/admin_welcome.js:2
msgid "Wireframes"
msgstr ""

#: dist/translation-strings.php:948
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Latest Design Library"
msgstr ""

#: dist/translation-strings.php:949
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "V2 Design Library"
msgstr ""

#: dist/translation-strings.php:950
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "E.g. light, dark, red, minimalist…"
msgstr ""

#: dist/translation-strings.php:951
#: dist/admin_welcome.js:2
msgid "Select Multiple"
msgstr ""

#: dist/translation-strings.php:952
#: dist/admin_welcome.js:2
msgid "Select"
msgstr ""

#: dist/translation-strings.php:953
#: dist/admin_welcome.js:2
msgid "Deselect All"
msgstr ""

#: dist/translation-strings.php:954
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Refresh Library"
msgstr ""

#: dist/translation-strings.php:955
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Large preview"
msgstr ""

#: dist/translation-strings.php:956
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Medium preview"
msgstr ""

#: dist/translation-strings.php:957
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Small preview"
msgstr ""

#: dist/translation-strings.php:958
#: dist/admin_welcome.js:2
msgid "Add Designs"
msgstr ""

#: dist/translation-strings.php:959
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Layout Tab"
msgstr ""

#: dist/translation-strings.php:960
#: dist/admin_welcome.js:2
msgid "Style"
msgstr ""

#: dist/translation-strings.php:961
#: dist/admin_welcome.js:2
msgid "Style Tab"
msgstr ""

#: dist/translation-strings.php:962
#: dist/admin_welcome.js:2
msgid "Advanced"
msgstr ""

#: dist/translation-strings.php:963
#: dist/admin_welcome.js:2
msgid "Advanced Tab"
msgstr ""

#: dist/translation-strings.php:964
#: dist/admin_welcome.js:2
msgid "Get More Image Shapes"
msgstr ""

#: dist/translation-strings.php:965
#: dist/admin_welcome.js:2
msgid "Mask images with a variety of blob-like shapes"
msgstr ""

#: dist/translation-strings.php:966
#: dist/admin_welcome.js:2
msgid "Choose from over 50 different shapes"
msgstr ""

#: dist/translation-strings.php:967
#: dist/admin_welcome.js:2
msgid "Enhances the overall aesthetic of images"
msgstr ""

#: dist/translation-strings.php:968
#: dist/admin_welcome.js:2
msgid "Use Dynamic Content"
msgstr ""

#: dist/translation-strings.php:969
#: dist/admin_welcome.js:2
msgid "Add dynamic content from posts or post meta"
msgstr ""

#: dist/translation-strings.php:970
#: dist/admin_welcome.js:2
msgid "Use third-party plugins as dynamic sources such as ACF, Meta Box, Toolset, and more"
msgstr ""

#: dist/translation-strings.php:971
#: dist/admin_welcome.js:2
msgid "Build custom loop design with the Native Query Loop"
msgstr ""

#: dist/translation-strings.php:972
#: dist/admin_welcome.js:2
msgid "Separator Layers"
msgstr ""

#: dist/translation-strings.php:973
#: dist/admin_welcome.js:2
msgid "Add a second and third layer to separators"
msgstr ""

#: dist/translation-strings.php:974
#: dist/admin_welcome.js:2
msgid "Change layer color, size and opacity"
msgstr ""

#: dist/translation-strings.php:975
#: dist/admin_welcome.js:2
msgid "Greater creativity in designing separators"
msgstr ""

#: dist/translation-strings.php:976
#: dist/admin_welcome.js:2
msgid "Elevate Your Icons"
msgstr ""

#: dist/translation-strings.php:977
#: dist/admin_welcome.js:2
msgid "Liven up icons with gradient fills, multiple colors and background shapes"
msgstr ""

#: dist/translation-strings.php:978
#: dist/admin_welcome.js:2
msgid "More design options and customization for icons"
msgstr ""

#: dist/translation-strings.php:979
#: dist/admin_welcome.js:2
msgid "Choose from over 50 background shapes"
msgstr ""

#: dist/translation-strings.php:980
#: dist/admin_welcome.js:2
msgid "Greater visual interest and variety for your icons"
msgstr ""

#: dist/translation-strings.php:981
#: dist/admin_welcome.js:2
msgid "Liven up icons with background shapes"
msgstr ""

#: dist/translation-strings.php:982
#: dist/admin_welcome.js:2
msgid "Adjust timing of CSS transitions"
msgstr ""

#: dist/translation-strings.php:983
#: dist/admin_welcome.js:2
msgid "Change X and Y position of blocks"
msgstr ""

#: dist/translation-strings.php:984
#: dist/admin_welcome.js:2
msgid "Scale or rotate blocks"
msgstr ""

#: dist/translation-strings.php:985
#: dist/admin_welcome.js:2
msgid "Perfect for hover animations"
msgstr ""

#: dist/translation-strings.php:986
#: dist/admin_welcome.js:2
msgid "Entrance fade-ins and animations when scrolling to blocks"
msgstr ""

#: dist/translation-strings.php:987
#: dist/admin_welcome.js:2
msgid "Smooth scroll animations based on scrolling position"
msgstr ""

#: dist/translation-strings.php:988
#: dist/admin_welcome.js:2
msgid "Create a more visually engaging and interactive experience"
msgstr ""

#: dist/translation-strings.php:989
#: dist/admin_welcome.js:2
msgid "Show or hide blocks based on conditions"
msgstr ""

#: dist/translation-strings.php:990
#: dist/admin_welcome.js:2
msgid "Display blocks based on time, role, meta, custom PHP, option, taxonomy and more"
msgstr ""

#: dist/translation-strings.php:991
#: dist/admin_welcome.js:2
msgid "Use multiple conditions"
msgstr ""

#: dist/translation-strings.php:992
#: dist/admin_welcome.js:2
msgid "Show targeted content and personalization"
msgstr ""

#: dist/translation-strings.php:993
#: dist/admin_welcome.js:2
msgid "Greater control over the visibility of content"
msgstr ""

#: dist/translation-strings.php:994
#: dist/admin_welcome.js:2
msgid "Add custom CSS rules specific for each block"
msgstr ""

#: dist/translation-strings.php:995
#: dist/admin_welcome.js:2
msgid "Support for media queries"
msgstr ""

#: dist/translation-strings.php:996
#: dist/admin_welcome.js:2
msgid "Fine-tune styling on a per block basis"
msgstr ""

#: dist/translation-strings.php:997
#: dist/admin_welcome.js:2
msgid "Adjust Column Arrangement"
msgstr ""

#: dist/translation-strings.php:998
#: dist/admin_welcome.js:2
msgid "Adjust the arrangement of columns when collapsed on tablet and mobile"
msgstr ""

#: dist/translation-strings.php:999
#: dist/admin_welcome.js:2
msgid "Ensure that content remains organized and easily readable on mobile"
msgstr ""

#: dist/translation-strings.php:1000
#: dist/admin_welcome.js:2
msgid "This is a Premium Design"
msgstr ""

#: dist/translation-strings.php:1001
#: dist/admin_welcome.js:2
msgid "Unlock access to the entire design library and set your website apart from the rest."
msgstr ""

#: dist/translation-strings.php:1002
#: dist/admin_welcome.js:2
msgid "Get More Post Options"
msgstr ""

#: dist/translation-strings.php:1003
#: dist/admin_welcome.js:2
msgid "New Custom Post Type option"
msgstr ""

#: dist/translation-strings.php:1004
#: dist/admin_welcome.js:2
msgid "Offset, exclude, include specific posts"
msgstr ""

#: dist/translation-strings.php:1005
#: dist/admin_welcome.js:2
msgid "Hide the current post - great for synced patterns"
msgstr ""

#: dist/translation-strings.php:1006
#: dist/admin_welcome.js:2
msgid "Unlock Your Icon Library"
msgstr ""

#: dist/translation-strings.php:1007
#: dist/admin_welcome.js:2
msgid "Add your custom SVG icons"
msgstr ""

#: dist/translation-strings.php:1008
#: dist/admin_welcome.js:2
msgid "Easily access your custom icons in the icon picker"
msgstr ""

#: dist/translation-strings.php:1009
#: dist/admin_welcome.js:2
msgid "Organize your custom icons in your library"
msgstr ""

#: dist/translation-strings.php:1010
#: dist/admin_welcome.js:2
msgid "Premium Typography"
msgstr ""

#: dist/translation-strings.php:1011
#: dist/admin_welcome.js:2
msgid "Access to 90+ curated font pairs"
msgstr ""

#: dist/translation-strings.php:1012
#: dist/admin_welcome.js:2
msgid "Create your own custom font pairs"
msgstr ""

#: dist/translation-strings.php:1013
#: dist/admin_welcome.js:2
msgid "Premium Color Schemes"
msgstr ""

#: dist/translation-strings.php:1014
#: dist/admin_welcome.js:2
msgid "Access to 50+ curated color scheme presets"
msgstr ""

#: dist/translation-strings.php:1015
#: dist/admin_welcome.js:2
msgid "Create your own color schemes"
msgstr ""

#: dist/translation-strings.php:1016
#: dist/admin_welcome.js:2
msgid "Set default color schemes for blocks and sections"
msgstr ""

#: dist/translation-strings.php:1017
#: dist/admin_welcome.js:2
msgid "Streamline your design workflow"
msgstr ""

#: dist/translation-strings.php:1018
#: dist/admin_welcome.js:2
msgid "This Is a Premium Feature"
msgstr ""

#: dist/translation-strings.php:1019
#: src/welcome/index.php:219
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Learn More"
msgstr ""

#: dist/translation-strings.php:1020
#: dist/admin_welcome.js:2
msgid "View Demo"
msgstr ""

#: dist/translation-strings.php:1021
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "You can hide premium hints in the settings"
msgstr ""

#: dist/translation-strings.php:1022
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Designs"
msgstr ""

#: dist/translation-strings.php:1023
msgid "Pick a design to start from, this will override your block settings"
msgstr ""

#: dist/translation-strings.php:1024
#: dist/admin_welcome.js:2
msgid "Desktop"
msgstr ""

#: dist/translation-strings.php:1025
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Tablet"
msgstr ""

#: dist/translation-strings.php:1026
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Mobile"
msgstr ""

#: dist/translation-strings.php:1027
#: dist/admin_welcome.js:2
msgid "Inset"
msgstr ""

#: dist/translation-strings.php:1028
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:207
msgid "Vertical Offset"
msgstr ""

#: dist/translation-strings.php:1029
#: dist/admin_welcome.js:2
msgid "Shadow Spread"
msgstr ""

#: dist/translation-strings.php:1030
#: dist/admin_welcome.js:2
msgid "Shadow Color"
msgstr ""

#: dist/translation-strings.php:1031
#: dist/admin_welcome.js:2
msgid "Advanced Shadow Options"
msgstr ""

#: dist/translation-strings.php:1032
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:300
msgid "Custom"
msgstr ""

#: dist/translation-strings.php:1033
#: dist/admin_welcome.js:2
msgid "Shadow Settings"
msgstr ""

#: dist/translation-strings.php:1034
#: dist/admin_welcome.js:2
msgid "Shadow/Outline"
msgstr ""

#: dist/translation-strings.php:1035
#: dist/admin_welcome.js:2
msgid "Adjusts the intensity of the shadow/outline of the block and the appearance of the block border"
msgstr ""

#: dist/translation-strings.php:1036
#: dist/admin_welcome.js:2
msgid "Column Arrangement"
msgstr ""

#: dist/translation-strings.php:1037
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:259
#: dist/admin_welcome.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:18
msgid "Post Type"
msgstr ""

#: dist/translation-strings.php:1038
#: dist/admin_welcome.js:2
msgid "Filter by Taxonomy"
msgstr ""

#: dist/translation-strings.php:1039
#: dist/admin_welcome.js:2
msgid "Taxonomy Filter Type"
msgstr ""

#: dist/translation-strings.php:1040
#: dist/admin_welcome.js:2
msgid "Included In"
msgstr ""

#: dist/translation-strings.php:1041
#: dist/admin_welcome.js:2
msgid "Not In"
msgstr ""

#: dist/translation-strings.php:1042
#: dist/admin_welcome.js:2
msgid "Opens in new tab"
msgstr ""

#: dist/translation-strings.php:1043
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Nofollow link"
msgstr ""

#: dist/translation-strings.php:1044
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Sponsored"
msgstr ""

#: dist/translation-strings.php:1045
#: dist/admin_welcome.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "UGC"
msgstr ""

#: dist/translation-strings.php:1046
msgid "Search or type url"
msgstr ""

#: dist/translation-strings.php:1047
#: dist/admin_welcome.js:2
msgid "URL"
msgstr ""

#: dist/translation-strings.php:1048
msgid "Upgrade to Premium to get more design variations."
msgstr ""

#: dist/translation-strings.php:1049
#: dist/editor_blocks.js:2
msgid "Color & Highlight"
msgstr ""

#: dist/translation-strings.php:1050
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Highlight"
msgstr ""

#: dist/translation-strings.php:1051
#: dist/editor_blocks.js:2
msgid "Low"
msgstr ""

#: dist/translation-strings.php:1052
#: dist/editor_blocks.js:2
msgid "Highlight Text"
msgstr ""

#: dist/translation-strings.php:1053
#: dist/editor_blocks.js:2
msgid "Reset layout"
msgstr ""

#: dist/translation-strings.php:1054
#: dist/editor_blocks.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:378
msgid "Icon Library"
msgstr ""

#: dist/translation-strings.php:1055
#: dist/editor_blocks.js:2
msgid "Set global styles and settings for your Stackable blocks to create a consistent design across your site. All the settings below will apply globally."
msgstr ""

#: dist/translation-strings.php:1056
msgid "The page will now reload for the old blocks to load. Please reload it if it does not refresh automatically."
msgstr ""

#: dist/translation-strings.php:1057
msgid "Stackable V2 Block Detected"
msgstr ""

#: dist/translation-strings.php:1058
msgid "Hello! 👋"
msgstr ""

#: dist/translation-strings.php:1059
msgid "We noticed that the page you are editing contains old Stackable version 2 blocks. Starting Stackable version 3, old v2 blocks are not anymore loaded in the editor. This means that you would not be able to edit the old v2 blocks in the editor and any old v2 block would show a block error in the editor."
msgstr ""

#: dist/translation-strings.php:1060
msgid "Do you want to enable backward compatibility, so the old blocks will be loaded in the editor?"
msgstr ""

#: dist/translation-strings.php:1061
msgid "Yes, load V2 Blocks in the editor, but only when V2 blocks are present already"
msgstr ""

#: dist/translation-strings.php:1062
msgid "Yes, always load V2 blocks in the editor"
msgstr ""

#: dist/translation-strings.php:1063
msgid "No, don't load V2 blocks in the editor"
msgstr ""

#: dist/translation-strings.php:1064
#: dist/editor_blocks.js:2
msgid "Error saving block, please make sure you only have a %s block"
msgstr ""

#: dist/translation-strings.php:1065
#: dist/editor_blocks.js:2
msgid "Default %s Block saved"
msgstr ""

#: dist/translation-strings.php:1066
#: dist/editor_blocks.js:2
msgid "%s Block style saved"
msgstr ""

#: dist/translation-strings.php:1067
#: dist/editor_blocks.js:2
msgid "Default %s Block Saved!"
msgstr ""

#: dist/translation-strings.php:1068
#: dist/editor_blocks.js:2
msgid "Save as Default %s Block"
msgstr ""

#: dist/translation-strings.php:1069
#: dist/editor_blocks.js:2
msgid "Default %s Block Deleted!"
msgstr ""

#: dist/translation-strings.php:1070
#: dist/editor_blocks.js:2
msgid "Reset Default %s Block"
msgstr ""

#: dist/translation-strings.php:1071
#: dist/editor_blocks.js:2
msgid "Couldn't update block styles, got this error:"
msgstr ""

#: dist/translation-strings.php:1072
#: dist/editor_blocks.js:2
msgid "Please refresh the page and try again."
msgstr ""

#: dist/translation-strings.php:1073
#: dist/admin_welcome.js:2
msgid "Facebook"
msgstr ""

#: dist/translation-strings.php:1074
#: dist/admin_welcome.js:2
msgid "Twitter"
msgstr ""

#: dist/translation-strings.php:1075
#: dist/admin_welcome.js:2
msgid "Instagram"
msgstr ""

#: dist/translation-strings.php:1076
#: dist/admin_welcome.js:2
msgid "Pinterest"
msgstr ""

#: dist/translation-strings.php:1077
#: dist/admin_welcome.js:2
msgid "LinkedIn"
msgstr ""

#: dist/translation-strings.php:1078
#: src/stk-block-types.php:1121
#: dist/admin_welcome.js:2
#: dist/translation-strings.js:601
msgid "YouTube"
msgstr ""

#: dist/translation-strings.php:1079
#: dist/admin_welcome.js:2
msgid "Email"
msgstr ""

#: dist/translation-strings.php:1080
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Image or Video"
msgstr ""

#: dist/translation-strings.php:1081
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Image"
msgstr ""

#: dist/translation-strings.php:1082
msgid "Background Image Url"
msgstr ""

#: dist/translation-strings.php:1083
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Media Tint Strength"
msgstr ""

#: dist/translation-strings.php:1084
msgid "Adjusts the intensity of the background media tint"
msgstr ""

#: dist/translation-strings.php:1085
msgid "Background Video Thumbnail"
msgstr ""

#: dist/translation-strings.php:1086
msgid "Note: Fixed Background works on Desktop and Android devices only."
msgstr ""

#: dist/translation-strings.php:1087
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Fixed Background"
msgstr ""

#: dist/translation-strings.php:1088
msgid "Keeps the background image fixed in place while scrolling"
msgstr ""

#: dist/translation-strings.php:1089
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Adv. Background Image Settings"
msgstr ""

#: dist/translation-strings.php:1090
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Image Position"
msgstr ""

#: dist/translation-strings.php:1091
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:265
msgid "Top Center"
msgstr ""

#: dist/translation-strings.php:1092
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:267
msgid "Center Left"
msgstr ""

#: dist/translation-strings.php:1093
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:268
msgid "Center Center"
msgstr ""

#: dist/translation-strings.php:1094
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:269
msgid "Center Right"
msgstr ""

#: dist/translation-strings.php:1095
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:271
msgid "Bottom Center"
msgstr ""

#: dist/translation-strings.php:1096
msgid "Chooses which part of the background image will be the focal point"
msgstr ""

#: dist/translation-strings.php:1097
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Image Repeat"
msgstr ""

#: dist/translation-strings.php:1098
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "No-Repeat"
msgstr ""

#: dist/translation-strings.php:1099
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Repeat"
msgstr ""

#: dist/translation-strings.php:1100
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Repeat-X"
msgstr ""

#: dist/translation-strings.php:1101
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Repeat-Y"
msgstr ""

#: dist/translation-strings.php:1102
msgid "Covers the background with tiled images"
msgstr ""

#: dist/translation-strings.php:1103
msgid "Sets the display image size"
msgstr ""

#: dist/translation-strings.php:1104
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Custom Size"
msgstr ""

#: dist/translation-strings.php:1105
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Image Blend Mode"
msgstr ""

#: dist/translation-strings.php:1106
#: dist/editor_blocks.js:2
msgid "Adjusts the radius of block corners to make them more rounded"
msgstr ""

#: dist/translation-strings.php:1107
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Link / URL"
msgstr ""

#: dist/translation-strings.php:1108
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:322
msgid "Open in new tab"
msgstr ""

#: dist/translation-strings.php:1109
msgid "Open Link in Lightbox"
msgstr ""

#: dist/translation-strings.php:1110
msgid "Displaying a Google Map in a Lightbox? Use the embed iframe URL instead. Need help finding it?"
msgstr ""

#: dist/translation-strings.php:1111
msgid " Check out our docs."
msgstr ""

#: dist/translation-strings.php:1112
msgid "Link rel"
msgstr ""

#: dist/translation-strings.php:1113
msgid "Link relationship keywords, e.g. nofollow noreferrer prefetch"
msgstr ""

#: dist/translation-strings.php:1114
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Link Title"
msgstr ""

#: dist/translation-strings.php:1115
msgid "Also used for lightbox caption"
msgstr ""

#: dist/translation-strings.php:1116
msgid "Anchor ID"
msgstr ""

#: dist/translation-strings.php:1117
msgid "Add an id attribute to the anchor tag."
msgstr ""

#: dist/translation-strings.php:1118
msgid "Supports links to images, videos, YouTube, Vimeo, and web pages that allow embedding"
msgstr ""

#: dist/translation-strings.php:1119
msgid "Min. Height"
msgstr ""

#: dist/translation-strings.php:1120
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Max. Content Width"
msgstr ""

#: dist/translation-strings.php:1121
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:466
msgid "Content Vertical Align"
msgstr ""

#: dist/translation-strings.php:1122
msgid "Adjusts the minimum allowable height of the block"
msgstr ""

#: dist/translation-strings.php:1123
msgid "Sets the placement of the content to top, center or bottom. Available when the min. block height is set to higher than default."
msgstr ""

#: dist/translation-strings.php:1124
msgid "Adjusts the maximum allowable width of the block. The settings will depend on the block width you set on the toolbar."
msgstr ""

#: dist/translation-strings.php:1125
msgid "Sets the placement of the content to left, center or right. Not available when block width is set to full width."
msgstr ""

#: dist/translation-strings.php:1126
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Paddings"
msgstr ""

#: dist/translation-strings.php:1127
#: dist/editor_blocks.js:2
msgid "Margins"
msgstr ""

#: dist/translation-strings.php:1128
#: dist/editor_blocks.js:2
msgid "Sets the block paddings, i.e the space between the inner columns and the block border"
msgstr ""

#: dist/translation-strings.php:1129
msgid "Sets the block margin, i.e. the space outside the block between the block border and the next block."
msgstr ""

#: dist/translation-strings.php:1130
#: dist/admin_welcome.js:2
msgid "Frontend JS & CSS Files"
msgstr ""

#: dist/translation-strings.php:1131
#: dist/admin_welcome.js:2
msgid "Load across entire site"
msgstr ""

#: dist/translation-strings.php:1132
#: dist/admin_welcome.js:2
msgid "Load only in posts with Stackable blocks"
msgstr ""

#: dist/translation-strings.php:1133
#: dist/admin_welcome.js:2
#: src/block/accordion/block.json
#: src/deprecated/v2/block/accordion/block.json
msgctxt "block title"
msgid "Accordion"
msgstr ""

#: dist/translation-strings.php:1134
#: dist/admin_welcome.js:2
#: src/deprecated/v2/block/text/block.json
msgctxt "block title"
msgid "Advanced Text"
msgstr ""

#: dist/translation-strings.php:1135
#: dist/admin_welcome.js:2
#: src/deprecated/v2/block/heading/block.json
msgctxt "block title"
msgid "Advanced Heading"
msgstr ""

#: dist/translation-strings.php:1136
#: dist/admin_welcome.js:2
#: src/block/blockquote/block.json
#: src/deprecated/v2/block/blockquote/block.json
msgctxt "block title"
msgid "Blockquote"
msgstr ""

#: dist/translation-strings.php:1137
#: dist/admin_welcome.js:2
msgctxt "block title"
msgid "Blog Posts"
msgstr ""

#: dist/translation-strings.php:1138
#: dist/admin_welcome.js:2
#: src/block/button/block.json
#: src/deprecated/v2/block/button/block.json
msgctxt "block title"
msgid "Button"
msgstr ""

#: dist/translation-strings.php:1139
#: dist/admin_welcome.js:2
#: src/block/call-to-action/block.json
#: src/deprecated/v2/block/call-to-action/block.json
msgctxt "block title"
msgid "Call to Action"
msgstr ""

#: dist/translation-strings.php:1140
#: dist/admin_welcome.js:2
#: src/block/card/block.json
#: src/deprecated/v2/block/card/block.json
msgctxt "block title"
msgid "Card"
msgstr ""

#: dist/translation-strings.php:1141
#: dist/admin_welcome.js:2
#: src/block/columns/block.json
msgctxt "block title"
msgid "Columns"
msgstr ""

#: dist/translation-strings.php:1142
#: dist/admin_welcome.js:2
#: src/deprecated/v2/block/container/block.json
msgctxt "block title"
msgid "Container"
msgstr ""

#: dist/translation-strings.php:1143
#: dist/admin_welcome.js:2
#: src/block/count-up/block.json
#: src/deprecated/v2/block/count-up/block.json
msgctxt "block title"
msgid "Count Up"
msgstr ""

#: dist/translation-strings.php:1144
#: src/block/design-library/block.json
msgctxt "block title"
msgid "Design Library"
msgstr ""

#: dist/translation-strings.php:1145
#: dist/admin_welcome.js:2
#: src/block/divider/block.json
#: src/deprecated/v2/block/divider/block.json
msgctxt "block title"
msgid "Divider"
msgstr ""

#: dist/translation-strings.php:1146
#: dist/admin_welcome.js:2
#: src/block/expand/block.json
#: src/deprecated/v2/block/expand/block.json
msgctxt "block title"
msgid "Expand / Show More"
msgstr ""

#: dist/translation-strings.php:1147
#: dist/admin_welcome.js:2
#: src/block/feature-grid/block.json
#: src/deprecated/v2/block/feature-grid/block.json
msgctxt "block title"
msgid "Feature Grid"
msgstr ""

#: dist/translation-strings.php:1148
#: dist/admin_welcome.js:2
#: src/block/feature/block.json
#: src/deprecated/v2/block/feature/block.json
msgctxt "block title"
msgid "Feature"
msgstr ""

#: dist/translation-strings.php:1149
#: dist/admin_welcome.js:2
#: src/deprecated/v2/block/header/block.json
msgctxt "block title"
msgid "Header"
msgstr ""

#: dist/translation-strings.php:1150
#: dist/admin_welcome.js:2
#: src/block/icon/block.json
#: src/deprecated/v2/block/icon/block.json
msgctxt "block title"
msgid "Icon"
msgstr ""

#: dist/translation-strings.php:1151
#: dist/admin_welcome.js:2
#: src/block/icon-list/block.json
#: src/deprecated/v2/block/icon-list/block.json
msgctxt "block title"
msgid "Icon List"
msgstr ""

#: dist/translation-strings.php:1152
#: dist/admin_welcome.js:2
#: src/block/image-box/block.json
#: src/deprecated/v2/block/image-box/block.json
msgctxt "block title"
msgid "Image Box"
msgstr ""

#: dist/translation-strings.php:1153
#: dist/admin_welcome.js:2
#: src/block/notification/block.json
#: src/deprecated/v2/block/notification/block.json
msgctxt "block title"
msgid "Notification"
msgstr ""

#: dist/translation-strings.php:1154
#: dist/admin_welcome.js:2
#: src/block/number-box/block.json
#: src/deprecated/v2/block/number-box/block.json
msgctxt "block title"
msgid "Number Box"
msgstr ""

#: dist/translation-strings.php:1155
#: dist/admin_welcome.js:2
#: src/block/pricing-box/block.json
#: src/deprecated/v2/block/pricing-box/block.json
msgctxt "block title"
msgid "Pricing Box"
msgstr ""

#: dist/translation-strings.php:1156
#: dist/admin_welcome.js:2
#: src/block/separator/block.json
#: src/deprecated/v2/block/separator/block.json
msgctxt "block title"
msgid "Separator"
msgstr ""

#: dist/translation-strings.php:1157
#: dist/admin_welcome.js:2
#: src/block/spacer/block.json
#: src/deprecated/v2/block/spacer/block.json
msgctxt "block title"
msgid "Spacer"
msgstr ""

#: dist/translation-strings.php:1158
#: dist/admin_welcome.js:2
#: src/block/team-member/block.json
#: src/deprecated/v2/block/team-member/block.json
msgctxt "block title"
msgid "Team Member"
msgstr ""

#: dist/translation-strings.php:1159
#: dist/admin_welcome.js:2
#: src/block/testimonial/block.json
#: src/deprecated/v2/block/testimonial/block.json
msgctxt "block title"
msgid "Testimonial"
msgstr ""

#: dist/translation-strings.php:1160
#: dist/admin_welcome.js:2
#: src/block/video-popup/block.json
#: src/deprecated/v2/block/video-popup/block.json
msgctxt "block title"
msgid "Video Popup"
msgstr ""

#: dist/translation-strings.php:1161
#: dist/editor_blocks.js:2
msgid "Block Defaults"
msgstr ""

#: dist/translation-strings.php:1162
#: dist/editor_blocks.js:2
msgid "Manage how Stackable blocks look when they're inserted."
msgstr ""

#: dist/translation-strings.php:1163
#: dist/editor_blocks.js:2
msgid "Learn more about Block Defaults"
msgstr ""

#: dist/translation-strings.php:1164
#: dist/editor_blocks.js:2
msgid "Global Spacing & Borders"
msgstr ""

#: dist/translation-strings.php:1165
#: dist/editor_blocks.js:2
msgid "Hover States"
msgstr ""

#: dist/translation-strings.php:1166
#: dist/editor_blocks.js:2
msgid "When editing block layouts in the hover states, select a block to view the applied styles."
msgstr ""

#: dist/translation-strings.php:1167
#: dist/editor_blocks.js:2
msgid "Globally style spacings and borders across all our blocks."
msgstr ""

#: dist/translation-strings.php:1168
#: dist/editor_blocks.js:2
msgid "Learn more about Global Styles"
msgstr ""

#: dist/translation-strings.php:1169
#: dist/editor_blocks.js:2
msgid "Are you sure you want to reset all %s styles to their default values?"
msgstr ""

#: dist/translation-strings.php:1170
#: dist/editor_blocks.js:2
msgid "Reset All"
msgstr ""

#: dist/translation-strings.php:1171
#: dist/editor_blocks.js:2
msgid "These styles are applied to all our Blocks."
msgstr ""

#: dist/translation-strings.php:1172
#: dist/editor_blocks.js:2
msgid "Block Margin Bottom"
msgstr ""

#: dist/translation-strings.php:1173
#: dist/editor_blocks.js:2
msgid "Sets the block margin bottom, i.e. the space outside the block between the block border and the next block."
msgstr ""

#: dist/translation-strings.php:1174
#: dist/editor_blocks.js:2
msgid "These styles are applied to Inner Column blocks."
msgstr ""

#: dist/translation-strings.php:1175
#: dist/editor_blocks.js:2
msgid " Inner Column Spacing"
msgstr ""

#: dist/translation-strings.php:1176
#: dist/editor_blocks.js:2
msgid "These styles are applied to blocks that have the \"Background\" option enabled in the Style Tab."
msgstr ""

#: dist/translation-strings.php:1177
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Padding"
msgstr ""

#: dist/translation-strings.php:1178
#: dist/editor_blocks.js:2
msgid "Containers"
msgstr ""

#: dist/translation-strings.php:1179
#: dist/editor_blocks.js:2
msgid "These styles are applied to blocks that have the \"Container\" option enabled in the Layout Tab."
msgstr ""

#: dist/translation-strings.php:1180
#: dist/editor_blocks.js:2
msgid "These styles are applied to all images in Stackable Blocks."
msgstr ""

#: dist/translation-strings.php:1181
#: dist/editor_blocks.js:2
msgid "The %s cannot be changed in any hover state when using a gradient."
msgstr ""

#: dist/translation-strings.php:1182
#: dist/editor_blocks.js:2
msgid "Changing the %s is not allowed for %s state."
msgstr ""

#: dist/translation-strings.php:1183
#: dist/editor_blocks.js:2
msgid "parent-hover"
msgstr ""

#: dist/translation-strings.php:1184
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Heading Color"
msgstr ""

#: dist/translation-strings.php:1185
#: dist/editor_blocks.js:2
msgid "hover"
msgstr ""

#: dist/translation-strings.php:1186
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
msgid "Link Color"
msgstr ""

#: dist/translation-strings.php:1187
#: dist/editor_blocks.js:2
msgid "Accent Color"
msgstr ""

#: dist/translation-strings.php:1188
#: dist/editor_blocks.js:2
msgid "Button Text Color"
msgstr ""

#: dist/translation-strings.php:1189
#: dist/editor_blocks.js:2
msgid "Button Outline Color"
msgstr ""

#: dist/translation-strings.php:1190
#: dist/editor_blocks.js:2
msgid "Are you sure you want to reset this color scheme to their default values?"
msgstr ""

#: dist/translation-strings.php:1191
#: dist/editor_blocks.js:2
msgid "Do you want to duplicate this color scheme?"
msgstr ""

#: dist/translation-strings.php:1192
#: dist/editor_blocks.js:2
msgid "Editing %s"
msgstr ""

#: dist/translation-strings.php:1193
#: dist/editor_blocks.js:2
msgid "Default Scheme"
msgstr ""

#: dist/translation-strings.php:1194
#: dist/editor_blocks.js:2
msgid "Background Scheme"
msgstr ""

#: dist/translation-strings.php:1195
#: dist/editor_blocks.js:2
msgid "Change the color scheme used for all blocks, and when the container option is enabled for a block."
msgstr ""

#: dist/translation-strings.php:1196
#: dist/editor_blocks.js:2
msgid "Change the color scheme applied when the background option is enabled for a block."
msgstr ""

#: dist/translation-strings.php:1197
#: dist/editor_blocks.js:2
msgid "Editing this scheme will also change all blocks that currently use this color scheme."
msgstr ""

#: dist/translation-strings.php:1198
#: dist/editor_blocks.js:2
msgid "Color Scheme Name"
msgstr ""

#: dist/translation-strings.php:1199
#: dist/editor_blocks.js:2
msgid "Color Scheme Presets"
msgstr ""

#: dist/translation-strings.php:1200
#: dist/editor_blocks.js:2
msgid "Show Scheme Colors in Color Pickers"
msgstr ""

#: dist/translation-strings.php:1201
#: dist/editor_blocks.js:2
msgid "Note: This background color is used when the container option of the block is enabled."
msgstr ""

#: dist/translation-strings.php:1202
#: dist/editor_blocks.js:2
msgid "Note: Background color is not used for Base Color Scheme."
msgstr ""

#: dist/translation-strings.php:1203
#: dist/editor_blocks.js:2
msgid "Global Color Schemes"
msgstr ""

#: dist/translation-strings.php:1204
#: dist/editor_blocks.js:2
msgid "When editing color schemes in the hover states, select a block to view the applied colors."
msgstr ""

#: dist/translation-strings.php:1205
#: dist/editor_blocks.js:2
msgid "Color schemes are applied to all blocks and sections of your entire website."
msgstr ""

#: dist/translation-strings.php:1206
#: dist/editor_blocks.js:2
msgid "Learn more about Global Color Schemes"
msgstr ""

#: dist/translation-strings.php:1207
#: dist/editor_blocks.js:2
msgid "Color Schemes"
msgstr ""

#: dist/translation-strings.php:1208
#: dist/editor_blocks.js:2
msgid "Show Global Color Schemes"
msgstr ""

#: dist/translation-strings.php:1209
#: dist/editor_blocks.js:2
msgid "Any blocks that use this color will become unlinked with this global color. Delete this color?"
msgstr ""

#: dist/translation-strings.php:1210
#: dist/editor_blocks.js:2
msgid "Custom Color %s"
msgstr ""

#: dist/translation-strings.php:1211
#: dist/editor_blocks.js:2
msgid "Global Color Palette"
msgstr ""

#: dist/translation-strings.php:1212
#: dist/editor_blocks.js:2
msgid "Change your color palette for all your blocks across your site."
msgstr ""

#: dist/translation-strings.php:1213
#: dist/editor_blocks.js:2
msgid "Learn more about Global Colors"
msgstr ""

#: dist/translation-strings.php:1214
#: dist/editor_blocks.js:2
msgid "Show Theme Colors"
msgstr ""

#: dist/translation-strings.php:1215
#: dist/editor_blocks.js:2
msgid "Show Default Colors"
msgstr ""

#: dist/translation-strings.php:1216
#: dist/editor_blocks.js:2
msgid "Show Site Editor Custom Colors"
msgstr ""

#: dist/translation-strings.php:1217
#: dist/editor_blocks.js:2
msgid "Global Buttons & Icons"
msgstr ""

#: dist/translation-strings.php:1218
#: dist/editor_blocks.js:2
msgid "Globally style buttons and icons across all our blocks."
msgstr ""

#: dist/translation-strings.php:1219
#: dist/editor_blocks.js:2
msgid "Buttons"
msgstr ""

#: dist/translation-strings.php:1220
#: dist/editor_blocks.js:2
msgid "These styles are applied to Button Blocks."
msgstr ""

#: dist/translation-strings.php:1221
#: dist/editor_blocks.js:2
msgid "Button Ghost Border Width"
msgstr ""

#: dist/translation-strings.php:1222
#: dist/editor_blocks.js:2
msgid "Icon Buttons"
msgstr ""

#: dist/translation-strings.php:1223
#: dist/editor_blocks.js:2
msgid "Additional settings that apply to Icon Button Blocks."
msgstr ""

#: dist/translation-strings.php:1224
#: dist/editor_blocks.js:2
msgid " Button Padding"
msgstr ""

#: dist/translation-strings.php:1225
#: src/stk-block-types.php:583
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:533
msgid "Icon List"
msgstr ""

#: dist/translation-strings.php:1226
#: dist/editor_blocks.js:2
msgid "These styles are applied to Icon List Blocks."
msgstr ""

#: dist/translation-strings.php:1227
#: dist/editor_blocks.js:2
msgid "Icons"
msgstr ""

#: dist/translation-strings.php:1228
#: dist/editor_blocks.js:2
msgid "These styles are applied to Icon Blocks."
msgstr ""

#: dist/translation-strings.php:1229
#: dist/editor_blocks.js:2
msgid "Heading %d"
msgstr ""

#: dist/translation-strings.php:1230
#: dist/editor_blocks.js:2
msgid "Body Text"
msgstr ""

#: dist/translation-strings.php:1231
#: src/stk-block-types.php:909
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks.js:2
#: dist/translation-strings.js:575
msgid "Subtitle"
msgstr ""

#: dist/translation-strings.php:1232
msgid "To apply this typography style, just add `%s` in your block\\'s Additional CSS classes. Also make sure that `%s` tag is set to avoid conflict with other typography styles"
msgstr ""

#: dist/translation-strings.php:1233
#: dist/editor_blocks.js:2
msgid "Picking a new font pair will overwrite the existing typography settings. Are you sure?"
msgstr ""

#: dist/translation-strings.php:1234
#: dist/editor_blocks.js:2
msgid "Global Typography"
msgstr ""

#: dist/translation-strings.php:1235
#: dist/editor_blocks.js:2
msgid "Change the typography of your headings for all your blocks in your site."
msgstr ""

#: dist/translation-strings.php:1236
#: dist/editor_blocks.js:2
msgid "Learn more about Global Typography"
msgstr ""

#: dist/translation-strings.php:1237
#: dist/editor_blocks.js:2
msgid "Apply Typography Styles to"
msgstr ""

#: dist/translation-strings.php:1238
#: dist/editor_blocks.js:2
msgid "Stackable and native blocks only"
msgstr ""

#: dist/translation-strings.php:1239
#: dist/editor_blocks.js:2
msgid "Stackable blocks only"
msgstr ""

#: dist/translation-strings.php:1240
#: dist/editor_blocks.js:2
msgid "Stackable and all other blocks"
msgstr ""

#: dist/translation-strings.php:1241
#: dist/editor_blocks.js:2
msgid "Reset %s Global Typography Style"
msgstr ""

#: dist/translation-strings.php:1242
#: dist/editor_blocks.js:2
msgid "Resetting this typography style will revert all typography to its original style. Proceed?"
msgstr ""

#: dist/translation-strings.php:1243
msgid "Corporate"
msgstr ""

#: dist/translation-strings.php:1244
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Basic"
msgstr ""

#: dist/translation-strings.php:1245
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Lined to Colored"
msgstr ""

#: dist/translation-strings.php:1246
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Reverse arrow"
msgstr ""

#: dist/translation-strings.php:1247
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Closed State Background Color"
msgstr ""

#: dist/translation-strings.php:1248
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Title Color"
msgstr ""

#: dist/translation-strings.php:1249
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Arrow"
msgstr ""

#: dist/translation-strings.php:1250
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Border"
msgstr ""

#: dist/translation-strings.php:1251
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Quotation Mark"
msgstr ""

#: dist/translation-strings.php:1252
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:217
msgid "Horizontal Position"
msgstr ""

#: dist/translation-strings.php:1253
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:218
msgid "Vertical Position"
msgstr ""

#: dist/translation-strings.php:1254
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Spread"
msgstr ""

#: dist/translation-strings.php:1255
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Grouped"
msgstr ""

#: dist/translation-strings.php:1256
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Collapse Buttons On"
msgstr ""

#: dist/translation-strings.php:1257
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Don't collapse"
msgstr ""

#: dist/translation-strings.php:1258
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:214
msgctxt "Panel title"
msgid "%s #%d"
msgstr ""

#: dist/translation-strings.php:1259
#: dist/admin_custom_fields__premium_only.js:2
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/translation-strings.js:396
msgid "Description"
msgstr ""

#: dist/translation-strings.php:1260
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Description Color"
msgstr ""

#: dist/translation-strings.php:1261
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Horizontal Card"
msgstr ""

#: dist/translation-strings.php:1262
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Content Order"
msgstr ""

#: dist/translation-strings.php:1263
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Posts Settings"
msgstr ""

#: dist/translation-strings.php:1264
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Read More"
msgstr ""

#: dist/translation-strings.php:1265
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:140
msgid "Load More"
msgstr ""

#: dist/translation-strings.php:1266
#: pro__premium_only/src/stk-block-types.php:43
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:418
msgid "Pagination"
msgstr ""

#: dist/translation-strings.php:1267
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:434
msgid "Image Width"
msgstr ""

#: dist/translation-strings.php:1268
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:433
msgid "Image Height"
msgstr ""

#: dist/translation-strings.php:1269
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Hover Color"
msgstr ""

#: dist/translation-strings.php:1270
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Customize Read More Link"
msgstr ""

#: dist/translation-strings.php:1271
#: pro__premium_only/src/stk-block-types.php:15
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:425
msgid "Load More Button"
msgstr ""

#: dist/translation-strings.php:1272
#: src/deprecated/v2/block/blog-posts/index.php:316
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Continue reading"
msgstr ""

#: dist/translation-strings.php:1273
#: src/deprecated/v2/block/blog-posts/attributes.php:1845
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:416
msgid "Next »"
msgstr ""

#: dist/translation-strings.php:1274
#: src/deprecated/v2/block/blog-posts/attributes.php:1849
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:417
msgid "« Previous"
msgstr ""

#: dist/translation-strings.php:1275
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Subtitle for this block"
msgstr ""

#: dist/translation-strings.php:1276
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Subtitle Color"
msgstr ""

#: dist/translation-strings.php:1277
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Text Colors"
msgstr ""

#: dist/translation-strings.php:1278
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Link Hover Color"
msgstr ""

#: dist/translation-strings.php:1279
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "The colors above might not apply to some nested blocks."
msgstr ""

#: dist/translation-strings.php:1280
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Short"
msgstr ""

#: dist/translation-strings.php:1281
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Tall"
msgstr ""

#: dist/translation-strings.php:1282
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Half-screen height"
msgstr ""

#: dist/translation-strings.php:1283
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Full-screen height"
msgstr ""

#: dist/translation-strings.php:1284
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Restrict to Content Width"
msgstr ""

#: dist/translation-strings.php:1285
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Abstract"
msgstr ""

#: dist/translation-strings.php:1286
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Boxed"
msgstr ""

#: dist/translation-strings.php:1287
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Number Color"
msgstr ""

#: dist/translation-strings.php:1288
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Vertical Margin"
msgstr ""

#: dist/translation-strings.php:1289
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Uneven"
msgstr ""

#: dist/translation-strings.php:1290
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Tiled"
msgstr ""

#: dist/translation-strings.php:1291
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Layouts"
msgstr ""

#: dist/translation-strings.php:1292
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "No Paddings"
msgstr ""

#: dist/translation-strings.php:1293
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Custom height"
msgstr ""

#: dist/translation-strings.php:1294
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Custom Height"
msgstr ""

#: dist/translation-strings.php:1295
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:464
msgid "Column Vertical Align"
msgstr ""

#: dist/translation-strings.php:1296
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Reverse Horizontally"
msgstr ""

#: dist/translation-strings.php:1297
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Full Height"
msgstr ""

#: dist/translation-strings.php:1298
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay Background"
msgstr ""

#: dist/translation-strings.php:1299
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Color Opacity"
msgstr ""

#: dist/translation-strings.php:1300
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "This affects other buttons in this block"
msgstr ""

#: dist/translation-strings.php:1301
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Half Background"
msgstr ""

#: dist/translation-strings.php:1302
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlap Shape %s"
msgstr ""

#: dist/translation-strings.php:1303
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlap Background %s"
msgstr ""

#: dist/translation-strings.php:1304
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Image Column Width"
msgstr ""

#: dist/translation-strings.php:1305
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Container Width"
msgstr ""

#: dist/translation-strings.php:1306
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Container Offset"
msgstr ""

#: dist/translation-strings.php:1307
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Number of Icons / Columns"
msgstr ""

#: dist/translation-strings.php:1308
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Title on Top"
msgstr ""

#: dist/translation-strings.php:1309
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Display as a grid (left to right & evenly spaced)"
msgstr ""

#: dist/translation-strings.php:1310
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "List Gap"
msgstr ""

#: dist/translation-strings.php:1311
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "You can click on each icon in the Icon List block to change them individually."
msgstr ""

#: dist/translation-strings.php:1312
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "List Text"
msgstr ""

#: dist/translation-strings.php:1313
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Text for this block"
msgstr ""

#: dist/translation-strings.php:1314
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Line %d"
msgstr ""

#: dist/translation-strings.php:1315
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgctxt "Nth Title with description"
msgid "%s %d %s"
msgstr ""

#: dist/translation-strings.php:1316
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Number Shape"
msgstr ""

#: dist/translation-strings.php:1317
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:205
msgid "Shape Size"
msgstr ""

#: dist/translation-strings.php:1318
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Number Background Color"
msgstr ""

#: dist/translation-strings.php:1319
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay Color Type"
msgstr ""

#: dist/translation-strings.php:1320
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay Hover Color"
msgstr ""

#: dist/translation-strings.php:1321
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:414
msgid "Effects"
msgstr ""

#: dist/translation-strings.php:1322
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Image Hover Effect"
msgstr ""

#: dist/translation-strings.php:1323
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Zoom In"
msgstr ""

#: dist/translation-strings.php:1324
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Zoom Out"
msgstr ""

#: dist/translation-strings.php:1325
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Separator Color"
msgstr ""

#: dist/translation-strings.php:1326
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Padding Top"
msgstr ""

#: dist/translation-strings.php:1327
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Padding Bottom"
msgstr ""

#: dist/translation-strings.php:1328
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Margin Top"
msgstr ""

#: dist/translation-strings.php:1329
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Use this to pull up/down the separator to the block above it"
msgstr ""

#: dist/translation-strings.php:1330
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Margin Bottom"
msgstr ""

#: dist/translation-strings.php:1331
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Use this to pull up/down the separator to the block below it"
msgstr ""

#: dist/translation-strings.php:1332
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:456
msgid "Layer"
msgstr ""

#: dist/translation-strings.php:1333
#: src/stk-block-types.php:778
#: src/stk-block-types.php:816
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/translation-strings.js:558
msgid "Price"
msgstr ""

#: dist/translation-strings.php:1334
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Sub Price"
msgstr ""

#: dist/translation-strings.php:1335
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Price Prefix"
msgstr ""

#: dist/translation-strings.php:1336
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Price Suffix"
msgstr ""

#: dist/translation-strings.php:1337
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Overlay Simple"
msgstr ""

#: dist/translation-strings.php:1338
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Social"
msgstr ""

#: dist/translation-strings.php:1339
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Social Button Gap"
msgstr ""

#: dist/translation-strings.php:1340
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Name Color"
msgstr ""

#: dist/translation-strings.php:1341
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Position Color"
msgstr ""

#: dist/translation-strings.php:1342
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Vertical Inverse"
msgstr ""

#: dist/translation-strings.php:1343
#: src/stk-block-types.php:1044
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/translation-strings.js:591
msgid "Testimonial"
msgstr ""

#: dist/translation-strings.php:1344
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Testimonial Color"
msgstr ""

#: dist/translation-strings.php:1345
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Play Button"
msgstr ""

#: dist/translation-strings.php:1346
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Button Style"
msgstr ""

#: dist/translation-strings.php:1347
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Normal Play Button"
msgstr ""

#: dist/translation-strings.php:1348
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Play Button with Circle"
msgstr ""

#: dist/translation-strings.php:1349
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Outline Play Button"
msgstr ""

#: dist/translation-strings.php:1350
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Align Top"
msgstr ""

#: dist/translation-strings.php:1351
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Align Bottom"
msgstr ""

#: dist/translation-strings.php:1352
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Side Title"
msgstr ""

#: dist/translation-strings.php:1353
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Reverse Title"
msgstr ""

#: dist/translation-strings.php:1354
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Column Rule"
msgstr ""

#: dist/translation-strings.php:1355
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Vertical Align"
msgstr ""

#: dist/translation-strings.php:1356
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Subtitle on Top"
msgstr ""

#: dist/translation-strings.php:1357
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Title"
msgstr ""

#: dist/translation-strings.php:1358
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Border Thickness"
msgstr ""

#: dist/translation-strings.php:1359
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Icon Location"
msgstr ""

#: dist/translation-strings.php:1360
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Color Type"
msgstr ""

#: dist/translation-strings.php:1361
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Hover Opacity"
msgstr ""

#: dist/translation-strings.php:1362
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Hover Colors"
msgstr ""

#: dist/translation-strings.php:1363
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:467
msgid "Column Paddings"
msgstr ""

#: dist/translation-strings.php:1364
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Free"
msgstr ""

#: dist/translation-strings.php:1365
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Premium"
msgstr ""

#: dist/translation-strings.php:1366
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Shaped"
msgstr ""

#: dist/translation-strings.php:1367
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:345
msgid "Icon Color Type"
msgstr ""

#: dist/translation-strings.php:1368
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:211
msgid "Multicolor"
msgstr ""

#: dist/translation-strings.php:1369
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Icon Shape / Outline Color"
msgstr ""

#: dist/translation-strings.php:1370
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Icon Shape Border Radius"
msgstr ""

#: dist/translation-strings.php:1371
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Outline Border Radius"
msgstr ""

#: dist/translation-strings.php:1372
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Icon Shape Padding"
msgstr ""

#: dist/translation-strings.php:1373
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Outline Padding"
msgstr ""

#: dist/translation-strings.php:1374
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Outline Width"
msgstr ""

#: dist/translation-strings.php:1375
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Open link in new tab"
msgstr ""

#: dist/translation-strings.php:1376
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Use social colors"
msgstr ""

#: dist/translation-strings.php:1377
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Change to Normal Button on Hover"
msgstr ""

#: dist/translation-strings.php:1378
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Button Size"
msgstr ""

#: dist/translation-strings.php:1379
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Tiny"
msgstr ""

#: dist/translation-strings.php:1380
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Small"
msgstr ""

#: dist/translation-strings.php:1381
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:316
msgid "Medium"
msgstr ""

#: dist/translation-strings.php:1382
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:317
msgid "Large"
msgstr ""

#: dist/translation-strings.php:1383
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Vertical Padding"
msgstr ""

#: dist/translation-strings.php:1384
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Horizontal Padding"
msgstr ""

#: dist/translation-strings.php:1385
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Adv. Icon Settings"
msgstr ""

#: dist/translation-strings.php:1386
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Icon Spacing"
msgstr ""

#: dist/translation-strings.php:1387
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Image Position"
msgstr ""

#: dist/translation-strings.php:1388
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Image Repeat"
msgstr ""

#: dist/translation-strings.php:1389
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Background Image Size"
msgstr ""

#: dist/translation-strings.php:1390
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Force square image"
msgstr ""

#: dist/translation-strings.php:1391
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Blend Mode"
msgstr ""

#: dist/translation-strings.php:1392
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgctxt "name"
msgid "%s Link"
msgstr ""

#: dist/translation-strings.php:1393
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "All Block Designs"
msgstr ""

#: dist/translation-strings.php:1394
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Free Designs"
msgstr ""

#: dist/translation-strings.php:1395
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Premium Designs"
msgstr ""

#: dist/translation-strings.php:1396
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Pick a layout or design"
msgstr ""

#: dist/translation-strings.php:1397
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Select a variation to start with."
msgstr ""

#: dist/translation-strings.php:1398
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Or pick from our Design Library."
msgstr ""

#: dist/translation-strings.php:1399
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Select a design from our library to start with."
msgstr ""

#: dist/translation-strings.php:1400
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Skip"
msgstr ""

#: dist/translation-strings.php:1401
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Spacing"
msgstr ""

#: dist/translation-strings.php:1402
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Min. Block Height"
msgstr ""

#: dist/translation-strings.php:1403
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Margins"
msgstr ""

#: dist/translation-strings.php:1404
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Paddings"
msgstr ""

#: dist/translation-strings.php:1405
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Background"
msgstr ""

#: dist/translation-strings.php:1406
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Align center"
msgstr ""

#: dist/translation-strings.php:1407
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Wide width"
msgstr ""

#: dist/translation-strings.php:1408
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Full width"
msgstr ""

#: dist/translation-strings.php:1409
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Change Alignment"
msgstr ""

#: dist/translation-strings.php:1410
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "You will not lose your block content when changing designs."
msgstr ""

#: dist/translation-strings.php:1411
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Max Width"
msgstr ""

#: dist/translation-strings.php:1412
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Horizontal Align"
msgstr ""

#: dist/translation-strings.php:1413
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Text Align"
msgstr ""

#: dist/translation-strings.php:1414
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Block Description"
msgstr ""

#: dist/translation-strings.php:1415
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Container Link"
msgstr ""

#: dist/translation-strings.php:1416
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Link / URL #%d"
msgstr ""

#: dist/translation-strings.php:1417
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Link %d Title"
msgstr ""

#: dist/translation-strings.php:1418
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
msgid "Column Spacing & More"
msgstr ""

#: dist/translation-strings.php:1419
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Check"
msgstr ""

#: dist/translation-strings.php:1420
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Cross"
msgstr ""

#: dist/translation-strings.php:1421
#: dist/deprecated/editor_blocks_deprecated_v2.js:2
msgid "Star"
msgstr ""

#: dist/translation-strings.php:1422
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:9
msgid "Login Status"
msgstr ""

#: dist/translation-strings.php:1423
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:10
msgid "Role"
msgstr ""

#: dist/translation-strings.php:1424
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:11
msgid "Date & Time"
msgstr ""

#: dist/translation-strings.php:1425
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:12
msgid "Custom PHP"
msgstr ""

#: dist/translation-strings.php:1426
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:13
msgid "Conditional Tag"
msgstr ""

#: dist/translation-strings.php:1427
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:14
msgid "Query String"
msgstr ""

#: dist/translation-strings.php:1428
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:15
msgid "Post Meta"
msgstr ""

#: dist/translation-strings.php:1429
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:16
msgid "Site Option"
msgstr ""

#: dist/translation-strings.php:1430
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:17
msgid "Post IDs"
msgstr ""

#: dist/translation-strings.php:1431
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:235
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:19
msgid "Post Taxonomy"
msgstr ""

#: dist/translation-strings.php:1432
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:92
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:102
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:109
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:116
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:123
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:130
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:137
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:144
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:151
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:158
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:165
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:172
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:179
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:186
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:194
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:201
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:208
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:215
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:222
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:229
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:236
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:243
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:250
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:257
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:264
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:271
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:278
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:20
msgid "WooCommerce"
msgstr ""

#: dist/translation-strings.php:1433
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:21
msgid "Enter Conditional Tag"
msgstr ""

#: dist/translation-strings.php:1434
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:22
msgid "Home"
msgstr ""

#: dist/translation-strings.php:1435
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:23
msgid "Front Page"
msgstr ""

#: dist/translation-strings.php:1436
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:24
msgid "404 Not Found Page"
msgstr ""

#: dist/translation-strings.php:1437
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:25
msgid "Single Page"
msgstr ""

#: dist/translation-strings.php:1438
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:26
msgid "Attachment"
msgstr ""

#: dist/translation-strings.php:1439
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:27
msgid "Preview"
msgstr ""

#: dist/translation-strings.php:1440
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:28
msgid "Any Page"
msgstr ""

#: dist/translation-strings.php:1441
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:29
msgid "Privacy Policy Page"
msgstr ""

#: dist/translation-strings.php:1442
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:30
msgid "Any Archive Page"
msgstr ""

#: dist/translation-strings.php:1443
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:31
msgid "Category Page"
msgstr ""

#: dist/translation-strings.php:1444
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:32
msgid "Tag Page"
msgstr ""

#: dist/translation-strings.php:1445
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:33
msgid "Taxonomy Page"
msgstr ""

#: dist/translation-strings.php:1446
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:34
msgid "Author Page"
msgstr ""

#: dist/translation-strings.php:1447
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:35
msgid "Date Archive Page"
msgstr ""

#: dist/translation-strings.php:1448
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:36
msgid "Yearly Archive Page"
msgstr ""

#: dist/translation-strings.php:1449
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:37
msgid "Search Result Page"
msgstr ""

#: dist/translation-strings.php:1450
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:38
msgid "Trackback"
msgstr ""

#: dist/translation-strings.php:1451
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:39
msgid "Dynamic Sidebar"
msgstr ""

#: dist/translation-strings.php:1452
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:40
msgid "RTL Reading"
msgstr ""

#: dist/translation-strings.php:1453
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:41
msgid "Multisite"
msgstr ""

#: dist/translation-strings.php:1454
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:42
msgid "Main Site"
msgstr ""

#: dist/translation-strings.php:1455
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:43
msgid "Child Theme"
msgstr ""

#: dist/translation-strings.php:1456
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:44
msgid "Customize Preview"
msgstr ""

#: dist/translation-strings.php:1457
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:45
msgid "Multi-author Site"
msgstr ""

#: dist/translation-strings.php:1458
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:46
msgid "Feed"
msgstr ""

#: dist/translation-strings.php:1459
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:47
msgid "Sticky Post"
msgstr ""

#: dist/translation-strings.php:1460
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:48
msgid "Hierarchical Post Type"
msgstr ""

#: dist/translation-strings.php:1461
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:49
msgid "Archive Post Type"
msgstr ""

#: dist/translation-strings.php:1462
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:50
msgid "Comments Open"
msgstr ""

#: dist/translation-strings.php:1463
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:51
msgid "Pings Open"
msgstr ""

#: dist/translation-strings.php:1464
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:52
msgid "Has Excerpt"
msgstr ""

#: dist/translation-strings.php:1465
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:53
msgid "Has Post Thumbnail"
msgstr ""

#: dist/translation-strings.php:1466
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:54
msgid "Has Tags"
msgstr ""

#: dist/translation-strings.php:1467
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:55
msgid "Has Terms"
msgstr ""

#: dist/translation-strings.php:1468
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:56
msgid "Has Primary Nav Menu"
msgstr ""

#: dist/translation-strings.php:1469
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:57
msgid "The Custom PHP allows you to configure the block’s visibility based on the expression entered. If the expression evaluates to true, the block will be displayed."
msgstr ""

#: dist/translation-strings.php:1470
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:58
msgid "Sample PHP code:"
msgstr ""

#: dist/translation-strings.php:1471
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:59
msgid "If a syntax error is present, check your PHP code"
msgstr ""

#: dist/translation-strings.php:1472
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:62
msgid "Now"
msgstr ""

#: dist/translation-strings.php:1473
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:64
msgid "Never"
msgstr ""

#: dist/translation-strings.php:1474
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:65
msgid "Days of the Week"
msgstr ""

#: dist/translation-strings.php:1475
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:66
msgid "If set, the block will be displayed / hidden on selected days."
msgstr ""

#: dist/translation-strings.php:1476
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:67
msgid "Sunday"
msgstr ""

#: dist/translation-strings.php:1477
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:68
msgid "Monday"
msgstr ""

#: dist/translation-strings.php:1478
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:69
msgid "Tuesday"
msgstr ""

#: dist/translation-strings.php:1479
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:70
msgid "Wednesday"
msgstr ""

#: dist/translation-strings.php:1480
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:71
msgid "Thursday"
msgstr ""

#: dist/translation-strings.php:1481
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:72
msgid "Friday"
msgstr ""

#: dist/translation-strings.php:1482
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:73
msgid "Saturday"
msgstr ""

#: dist/translation-strings.php:1483
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:75
msgid "Logged-In Users"
msgstr ""

#: dist/translation-strings.php:1484
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:76
msgid "Logged-Out Users"
msgstr ""

#: dist/translation-strings.php:1485
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:77
msgid "Enter Post IDs"
msgstr ""

#: dist/translation-strings.php:1486
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:78
msgid "Post Meta Key"
msgstr ""

#: dist/translation-strings.php:1487
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:79
msgid "Operator"
msgstr ""

#: dist/translation-strings.php:1488
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:80
msgid "True"
msgstr ""

#: dist/translation-strings.php:1489
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:81
msgid "False"
msgstr ""

#: dist/translation-strings.php:1490
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:82
msgid "Equal"
msgstr ""

#: dist/translation-strings.php:1491
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:83
msgid "Not Equal"
msgstr ""

#: dist/translation-strings.php:1492
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:84
msgid "Less Than"
msgstr ""

#: dist/translation-strings.php:1493
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:85
msgid "Less Than & Equal To"
msgstr ""

#: dist/translation-strings.php:1494
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:86
msgid "Greater Than"
msgstr ""

#: dist/translation-strings.php:1495
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:87
msgid "Greater Than & Equal To"
msgstr ""

#: dist/translation-strings.php:1496
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:88
msgid "Contains"
msgstr ""

#: dist/translation-strings.php:1497
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:89
msgid "Does Not Contain"
msgstr ""

#: dist/translation-strings.php:1498
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:90
msgid "Regular Expression"
msgstr ""

#: dist/translation-strings.php:1499
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:91
msgid "Enter Value"
msgstr ""

#: dist/translation-strings.php:1500
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:92
msgid "Value to compare with the post meta value."
msgstr ""

#: dist/translation-strings.php:1501
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:93
msgid "Enter Post Types"
msgstr ""

#: dist/translation-strings.php:1502
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:94
msgid "Enter Queries"
msgstr ""

#: dist/translation-strings.php:1503
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:95
msgid "Enter one query string per line. The block will be displayed / hidden if any of the query strings match."
msgstr ""

#: dist/translation-strings.php:1504
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:96
msgid "Enter Role"
msgstr ""

#: dist/translation-strings.php:1505
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:97
msgid "Option Name"
msgstr ""

#: dist/translation-strings.php:1506
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:98
msgid "Value to compare with the option value."
msgstr ""

#: dist/translation-strings.php:1507
#: pro__premium_only/src/dynamic-content/sources/current-page.php:31
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:99
msgid "Current Post"
msgstr ""

#: dist/translation-strings.php:1508
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:100
msgid "Choose Product"
msgstr ""

#: dist/translation-strings.php:1509
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:101
msgid "Property"
msgstr ""

#: dist/translation-strings.php:1510
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:102
msgid "Sales"
msgstr ""

#: dist/translation-strings.php:1511
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:103
msgid "Stock Quantity"
msgstr ""

#: dist/translation-strings.php:1512
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:104
msgid "Is Downloadable"
msgstr ""

#: dist/translation-strings.php:1513
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:105
msgid "Is Featured"
msgstr ""

#: dist/translation-strings.php:1514
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:106
msgid "Is in Stock"
msgstr ""

#: dist/translation-strings.php:1515
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:107
msgid "Is on Backorder"
msgstr ""

#: dist/translation-strings.php:1516
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:108
msgid "Is on Sale"
msgstr ""

#: dist/translation-strings.php:1517
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:109
msgid "Is Purchasable"
msgstr ""

#: dist/translation-strings.php:1518
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:110
msgid "Is Shipping Taxable"
msgstr ""

#: dist/translation-strings.php:1519
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:111
msgid "Is Sold Individually"
msgstr ""

#: dist/translation-strings.php:1520
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:112
msgid "Is Taxable"
msgstr ""

#: dist/translation-strings.php:1521
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:113
msgid "Value"
msgstr ""

#: dist/translation-strings.php:1522
#: pro__premium_only/src/dynamic-content/sources/acf.php:249
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:315
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:325
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:284
#: pro__premium_only/src/dynamic-content/sources/metabox.php:329
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:114
msgid "%s Placeholder"
msgstr ""

#: dist/translation-strings.php:1523
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:115
msgid "Placeholder"
msgstr ""

#: dist/translation-strings.php:1524
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:116
msgid "Full Access"
msgstr ""

#: dist/translation-strings.php:1525
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:117
msgid "Manager"
msgstr ""

#: dist/translation-strings.php:1526
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:118
msgid "No Access"
msgstr ""

#: dist/translation-strings.php:1527
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:122
msgid "Full editing mode"
msgstr ""

#: dist/translation-strings.php:1528
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:123
msgid "Content only editing"
msgstr ""

#: dist/translation-strings.php:1529
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:124
msgid "Let me enter my Font Awesome Pro Kit code"
msgstr ""

#: dist/translation-strings.php:1530
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:125
msgid "Don't show me this again"
msgstr ""

#: dist/translation-strings.php:1531
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:127
msgid "Paste your Kit code %s"
msgstr ""

#: dist/translation-strings.php:1532
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:128
msgid "Verify"
msgstr ""

#: dist/translation-strings.php:1533
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:129
msgid "Please make sure you have Pro icons selected in your kit. Edit your kit settings at: "
msgstr ""

#: dist/translation-strings.php:1534
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:130
msgid "Click here to check again"
msgstr ""

#: dist/translation-strings.php:1535
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:131
msgid "Please enter a valid Font Awesome Pro Kit code."
msgstr ""

#: dist/translation-strings.php:1536
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:132
msgid "If you have Font Awesome Pro, you can use your Pro icons by inputting your Pro Kit code here."
msgstr ""

#: dist/translation-strings.php:1537
#: dist/admin_welcome__premium_only.js:2
#: dist/translation-strings.js:133
msgid "Need help? Read our guide."
msgstr ""

#: dist/translation-strings.php:1538
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:142
msgid "Number of Items"
msgstr ""

#: dist/translation-strings.php:1539
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:144
msgid "URL Query String"
msgstr ""

#: dist/translation-strings.php:1540
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:145
msgid "This is the string appended to the URL when changing pages."
msgstr ""

#: dist/translation-strings.php:1541
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:146
msgid "Show Next & Previous Button"
msgstr ""

#: dist/translation-strings.php:1542
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:147
msgid "Next Label"
msgstr ""

#: dist/translation-strings.php:1543
#: pro__premium_only/src/block/pagination/index.php:74
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:148
msgid "Next"
msgstr ""

#: dist/translation-strings.php:1544
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:149
msgid "Previous Label"
msgstr ""

#: dist/translation-strings.php:1545
#: pro__premium_only/src/block/pagination/index.php:73
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:150
msgid "Previous"
msgstr ""

#: dist/translation-strings.php:1546
#: pro__premium_only/src/block/pagination/index.php:278
#: pro__premium_only/src/deprecated/v2/block/blog-posts/pagination.php:198
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:151
msgid "Previous page"
msgstr ""

#: dist/translation-strings.php:1547
#: pro__premium_only/src/block/pagination/index.php:299
#: pro__premium_only/src/deprecated/v2/block/blog-posts/pagination.php:219
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:152
msgid "Page %s"
msgstr ""

#: dist/translation-strings.php:1548
#: pro__premium_only/src/block/pagination/index.php:324
#: pro__premium_only/src/deprecated/v2/block/blog-posts/pagination.php:244
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:153
msgid "Next page"
msgstr ""

#: dist/translation-strings.php:1549
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:155
msgid "Offset your posts by a specific number of items."
msgstr ""

#: dist/translation-strings.php:1550
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:156
msgid "Exclude Post IDs"
msgstr ""

#: dist/translation-strings.php:1551
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:157
msgid "Excludes specific IDs from the display. Enter post IDs separated by a commas"
msgstr ""

#: dist/translation-strings.php:1552
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:158
msgid "Hide the current post"
msgstr ""

#: dist/translation-strings.php:1553
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:159
msgid "Removes the current post from the posts list"
msgstr ""

#: dist/translation-strings.php:1554
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:160
msgid "Display Specific Post IDs"
msgstr ""

#: dist/translation-strings.php:1555
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:161
msgid "Limit display to only these specific IDs. Enter post IDs separated by a commas"
msgstr ""

#: dist/translation-strings.php:1556
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:172
msgid "Sale"
msgstr ""

#: dist/translation-strings.php:1557
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:162
msgid "Layer %s"
msgstr ""

#: dist/translation-strings.php:1558
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:164
msgid "Layer Height"
msgstr ""

#: dist/translation-strings.php:1559
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:165
msgid "Layer Width"
msgstr ""

#: dist/translation-strings.php:1560
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:168
msgid "Layer Opacity"
msgstr ""

#: dist/translation-strings.php:1561
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:169
msgid "Adjusts the transparency of the separator layer"
msgstr ""

#: dist/translation-strings.php:1562
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:170
msgid "Layer Blend mode"
msgstr ""

#: dist/translation-strings.php:1563
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:171
msgid "Sets how the sepator layer is blended into the background"
msgstr ""

#: dist/translation-strings.php:1564
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:179
msgid "Delete Condition"
msgstr ""

#: dist/translation-strings.php:1565
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:180
msgid "Deleting will remove this condition for the block. Proceed?"
msgstr ""

#: dist/translation-strings.php:1566
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:183
msgid "Condition Type"
msgstr ""

#: dist/translation-strings.php:1567
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:184
msgid "Visibility"
msgstr ""

#: dist/translation-strings.php:1568
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:185
msgid "Show on condition match"
msgstr ""

#: dist/translation-strings.php:1569
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:186
msgid "Hide on condition match"
msgstr ""

#: dist/translation-strings.php:1570
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:187
msgid "No conditions yet. Add your first condition."
msgstr ""

#. translators: This is the separator between conditions: OR / AND.
#: dist/translation-strings.php:1571
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:189
msgid "OR"
msgstr ""

#. translators: This is the separator between conditions: OR / AND.
#. translators: This is the separator between conditions: OR / AND.
#: dist/translation-strings.php:1572
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:191
msgid "AND"
msgstr ""

#. translators: This is the separator between conditions: OR / AND.
#: dist/translation-strings.php:1573
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:192
msgid "Trigger if ANY condition matches"
msgstr ""

#: dist/translation-strings.php:1574
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:193
msgid "Trigger if ALL conditions match"
msgstr ""

#: dist/translation-strings.php:1575
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:194
msgid "Add New"
msgstr ""

#: dist/translation-strings.php:1576
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:195
msgid "Add New Condition"
msgstr ""

#: dist/translation-strings.php:1577
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:196
msgid "Learn more about Conditional Display"
msgstr ""

#: dist/translation-strings.php:1578
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:198
msgid "You can use this area to further customize your block. Any custom CSS added here will only affect this block."
msgstr ""

#: dist/translation-strings.php:1579
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:199
msgid "Media queries are supported. Use the widths 1024px and 768px for tablet and mobile breakpoints."
msgstr ""

#: dist/translation-strings.php:1580
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:220
msgid "Rotate"
msgstr ""

#: dist/translation-strings.php:1581
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:221
msgid "Effect"
msgstr ""

#: dist/translation-strings.php:1582
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:222
msgid "Entrance Animation"
msgstr ""

#: dist/translation-strings.php:1583
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:223
msgid "Scroll Animation"
msgstr ""

#: dist/translation-strings.php:1584
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:224
msgid "Start Position"
msgstr ""

#: dist/translation-strings.php:1585
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:225
msgid "Entrance Animation Speed"
msgstr ""

#: dist/translation-strings.php:1586
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:226
msgid "Slow"
msgstr ""

#: dist/translation-strings.php:1587
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:228
msgid "Fast"
msgstr ""

#: dist/translation-strings.php:1588
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:229
msgid "Entrance Animation Delay"
msgstr ""

#: dist/translation-strings.php:1589
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:230
msgid "Smoothen Scroll Animation"
msgstr ""

#: dist/translation-strings.php:1590
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:231
msgid "Use 3D Transforms"
msgstr ""

#: dist/translation-strings.php:1591
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:232
msgid "Perspective"
msgstr ""

#: dist/translation-strings.php:1592
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:233
msgid "Exit Animation"
msgstr ""

#: dist/translation-strings.php:1593
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:234
msgid "This block has Transforms assigned to it, Motion Effects may not work as expected."
msgstr ""

#: dist/translation-strings.php:1594
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:235
msgid "Learn more about Motion Effects"
msgstr ""

#: dist/translation-strings.php:1595
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:236
msgid "Translate X"
msgstr ""

#: dist/translation-strings.php:1596
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:237
msgid "TranslateY"
msgstr ""

#: dist/translation-strings.php:1597
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:238
msgid "TranslateZ"
msgstr ""

#: dist/translation-strings.php:1598
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:239
msgid "RotateX"
msgstr ""

#: dist/translation-strings.php:1599
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:240
msgid "RotateY"
msgstr ""

#: dist/translation-strings.php:1600
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:242
msgid "Skew X"
msgstr ""

#: dist/translation-strings.php:1601
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:243
msgid "Skew Y"
msgstr ""

#: dist/translation-strings.php:1602
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:204
msgid "Shape Opacity"
msgstr ""

#: dist/translation-strings.php:1603
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:208
msgid "Icon Color #%s"
msgstr ""

#: dist/translation-strings.php:1604
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:212
msgid "Multicolor only works for custom uploaded icons that have multiple path elements or for Font Awesome Pro Duotones."
msgstr ""

#: dist/translation-strings.php:1605
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:244
msgid "Separator Layer %s"
msgstr ""

#: dist/translation-strings.php:1606
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:245
msgid "Transition Duration (secs)"
msgstr ""

#: dist/translation-strings.php:1607
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:246
msgid "Transition Function"
msgstr ""

#: dist/translation-strings.php:1608
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:248
msgid "Ease"
msgstr ""

#: dist/translation-strings.php:1609
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:249
msgid "Ease In"
msgstr ""

#: dist/translation-strings.php:1610
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:250
msgid "Ease Out"
msgstr ""

#: dist/translation-strings.php:1611
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:251
msgid "Ease In Out"
msgstr ""

#: dist/translation-strings.php:1612
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:252
msgid "Linear"
msgstr ""

#: dist/translation-strings.php:1613
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:253
msgid "Ease In Quad"
msgstr ""

#: dist/translation-strings.php:1614
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:254
msgid "Ease Out Quad"
msgstr ""

#: dist/translation-strings.php:1615
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:255
msgid "Ease In Out Quad"
msgstr ""

#: dist/translation-strings.php:1616
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:256
msgid "Ease In Expo"
msgstr ""

#: dist/translation-strings.php:1617
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:257
msgid "Ease Out Expo"
msgstr ""

#: dist/translation-strings.php:1618
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:258
msgid "Ease In Out Expo"
msgstr ""

#: dist/translation-strings.php:1619
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:259
msgid "Ease In Back"
msgstr ""

#: dist/translation-strings.php:1620
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:260
msgid "Ease Out Back"
msgstr ""

#: dist/translation-strings.php:1621
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:261
msgid "Ease In Out Back"
msgstr ""

#: dist/translation-strings.php:1622
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:263
msgid "Transform Origin"
msgstr ""

#: dist/translation-strings.php:1623
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:273
msgid "This block has Motion Effects assigned to it, applying transforms above may prevent the Motion Effects from working as expected."
msgstr ""

#: dist/translation-strings.php:1624
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:274
msgid "Translate Y"
msgstr ""

#: dist/translation-strings.php:1625
#: dist/translation-strings.js:275
msgid "No saved designs yet"
msgstr ""

#: dist/translation-strings.php:1626
#: dist/translation-strings.js:276
msgid "Click here to save your block's design"
msgstr ""

#: dist/translation-strings.php:1627
#: dist/translation-strings.js:277
msgid "Error Getting Designs"
msgstr ""

#: dist/translation-strings.php:1628
#: dist/translation-strings.js:278
msgid "Click here to retry fetching your saved designs"
msgstr ""

#: dist/translation-strings.php:1629
#: dist/translation-strings.js:279
msgid "Save as new block design"
msgstr ""

#: dist/translation-strings.php:1630
#: dist/translation-strings.js:280
msgid "Manage saved designs"
msgstr ""

#: dist/translation-strings.php:1631
#: dist/translation-strings.js:281
msgid "(default)"
msgstr ""

#: dist/translation-strings.php:1632
#: dist/translation-strings.js:282
msgid "Favorite"
msgstr ""

#: dist/translation-strings.php:1633
#: dist/translation-strings.js:284
msgid "Saved Block Designs"
msgstr ""

#: dist/translation-strings.php:1634
#: dist/translation-strings.js:285
msgid "Save designs to reuse them across your site. Note that using saved designs will override your current block settings."
msgstr ""

#: dist/translation-strings.php:1635
#: dist/translation-strings.js:286
msgid "You have unsaved changes, discard them?"
msgstr ""

#: dist/translation-strings.php:1636
#: dist/translation-strings.js:287
msgid "Manage Saved Designs"
msgstr ""

#: dist/translation-strings.php:1637
#: dist/translation-strings.js:288
msgid "Design Name"
msgstr ""

#: dist/translation-strings.php:1638
#: dist/translation-strings.js:289
msgid "Set as a favorite design"
msgstr ""

#: dist/translation-strings.php:1639
#: dist/translation-strings.js:290
msgid "Design name"
msgstr ""

#: dist/translation-strings.php:1640
#: dist/translation-strings.js:291
msgid "Set as default block design"
msgstr ""

#: dist/translation-strings.php:1641
#: dist/translation-strings.js:292
msgid "My Block Design"
msgstr ""

#: dist/translation-strings.php:1642
#: dist/translation-strings.js:293
msgid "Save as New Block Design"
msgstr ""

#: dist/translation-strings.php:1643
#: dist/translation-strings.js:294
msgid "Set as favorite"
msgstr ""

#: dist/translation-strings.php:1644
#: dist/translation-strings.js:295
msgid "Place at the top of the list of saved designs"
msgstr ""

#: dist/translation-strings.php:1645
#: dist/translation-strings.js:296
msgid "New blocks created will use this design automatically"
msgstr ""

#: dist/translation-strings.php:1646
#: dist/translation-strings.js:297
msgid "Add New Design"
msgstr ""

#: dist/translation-strings.php:1647
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:298
msgid "Show as link"
msgstr ""

#: dist/translation-strings.php:1648
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:299
msgid "Custom Text"
msgstr ""

#: dist/translation-strings.php:1649
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:301
msgid "Date Format"
msgstr ""

#: dist/translation-strings.php:1650
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:302
msgid "Custom Format"
msgstr ""

#: dist/translation-strings.php:1651
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:303
msgid "Change the date format of your dynamic content."
msgstr ""

#: dist/translation-strings.php:1652
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:304
msgid "Learn more about date formats"
msgstr ""

#: dist/translation-strings.php:1653
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:306
msgid "Dynamic Source"
msgstr ""

#: dist/translation-strings.php:1654
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:307
msgid "Field"
msgstr ""

#: dist/translation-strings.php:1655
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:308
msgid "Learn how to use Dynamic Content"
msgstr ""

#: dist/translation-strings.php:1656
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:309
msgid "Apply"
msgstr ""

#: dist/translation-strings.php:1657
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:312
msgid "Add Custom Format"
msgstr ""

#: dist/translation-strings.php:1658
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:313
msgid "Content Format"
msgstr ""

#: dist/translation-strings.php:1659
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:314
msgid "Add your custom format by adding %s."
msgstr ""

#: dist/translation-strings.php:1660
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:315
msgid "Thumbnail"
msgstr ""

#: dist/translation-strings.php:1661
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:319
msgid "Image Quality"
msgstr ""

#: dist/translation-strings.php:1662
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:321
msgid "Text Field"
msgstr ""

#: dist/translation-strings.php:1663
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:323
msgid "Taxonomy Type"
msgstr ""

#: dist/translation-strings.php:1664
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:324
msgid "Display Option"
msgstr ""

#: dist/translation-strings.php:1665
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:325
msgid "All values"
msgstr ""

#: dist/translation-strings.php:1666
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:326
msgid "%s value"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1667
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:59
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:102
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:230
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:327
msgid "1st"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1668
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:107
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:231
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:328
msgid "2nd"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1669
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:112
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:232
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:329
msgid "3rd"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1670
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:117
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:233
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:330
msgid "4th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1671
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:122
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:234
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:331
msgid "5th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1672
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:127
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:235
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:332
msgid "6th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1673
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:132
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:236
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:333
msgid "7th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1674
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:137
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:237
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:334
msgid "8th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1675
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:142
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:238
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:335
msgid "9th"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1676
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:147
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:239
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:336
msgid "10th"
msgstr ""

#: dist/translation-strings.php:1677
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:337
msgid "Delimiter"
msgstr ""

#: dist/translation-strings.php:1678
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:338
msgid "Strip HTML tags"
msgstr ""

#: dist/translation-strings.php:1679
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:339
msgid "If this option is false, the content rendered in the editor will still be stripped to prevent an error from occuring"
msgstr ""

#: dist/translation-strings.php:1680
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:340
msgid "Display text when true"
msgstr ""

#: dist/translation-strings.php:1681
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:341
msgid "Display text when false"
msgstr ""

#: dist/translation-strings.php:1682
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:89
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:342
msgid "Fields"
msgstr ""

#: dist/translation-strings.php:1683
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:343
msgid "Attributes"
msgstr ""

#: dist/translation-strings.php:1684
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:364
msgid "Default Block Colors"
msgstr ""

#: dist/translation-strings.php:1685
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:365
msgid "Base Color Scheme"
msgstr ""

#: dist/translation-strings.php:1686
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:366
msgid "Default color scheme to use for all blocks when no special options are enabled."
msgstr ""

#: dist/translation-strings.php:1687
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:367
msgid "Background Mode Color Scheme"
msgstr ""

#: dist/translation-strings.php:1688
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:368
msgid "Colors applied when the background option is enabled for a block."
msgstr ""

#: dist/translation-strings.php:1689
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:369
msgid "Container Mode Color Scheme"
msgstr ""

#: dist/translation-strings.php:1690
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:370
msgid "Colors applied when the container option is enabled for a block."
msgstr ""

#: dist/translation-strings.php:1691
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:371
msgid "Color Scheme %s"
msgstr ""

#: dist/translation-strings.php:1692
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:372
msgid "You have duplicated the color scheme. You are now editing the new one."
msgstr ""

#: dist/translation-strings.php:1693
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:373
msgid "Deleting this color scheme would remove all colors linked to it. Any blocks that use this color scheme will revert to the default scheme. Delete this color scheme?"
msgstr ""

#: dist/translation-strings.php:1694
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:352
msgid "Style copied successfully!"
msgstr ""

#: dist/translation-strings.php:1695
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:353
msgid "Style pasted successfully!"
msgstr ""

#: dist/translation-strings.php:1696
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:354
msgid "Used to copy core/stackable block styles"
msgstr ""

#: dist/translation-strings.php:1697
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:355
msgid "Used to paste core/stackable block styles"
msgstr ""

#: dist/translation-strings.php:1698
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:356
msgid "Adv Copy Styles"
msgstr ""

#: dist/translation-strings.php:1699
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:357
msgid "Adv Paste Styles"
msgstr ""

#: dist/translation-strings.php:1700
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:358
msgid "Copy & paste styles"
msgstr ""

#: dist/translation-strings.php:1701
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:359
msgid "Remove the selected block(s)."
msgstr ""

#: dist/translation-strings.php:1702
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:360
msgid "Content Editing mode is enabled"
msgstr ""

#: dist/translation-strings.php:1703
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:361
msgid "To unlock Full Editing mode, please contact your administrator."
msgstr ""

#: dist/translation-strings.php:1704
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:362
msgid "Are you sure you want to delete this font pair preset?"
msgstr ""

#: dist/translation-strings.php:1705
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:363
msgid "Editing Font Pair"
msgstr ""

#: dist/translation-strings.php:1706
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:374
msgid "Delete this icon?"
msgstr ""

#: dist/translation-strings.php:1707
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:377
msgid "Upload SVG icons to your library to use them in your blocks."
msgstr ""

#: dist/translation-strings.php:1708
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:379
msgid "Do you also want to add this icon to your icon library for future use?"
msgstr ""

#: dist/translation-strings.php:1709
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:346
msgid "Learn more how multicolor works"
msgstr ""

#: dist/translation-strings.php:1710
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:347
msgid "here"
msgstr ""

#: dist/translation-strings.php:1711
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:349
msgid "Outline Color"
msgstr ""

#: dist/translation-strings.php:1712
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:350
msgid "Icon Shape Color"
msgstr ""

#: dist/translation-strings.php:1713
#: src/welcome/index.php:204
#: dist/editor_blocks__premium_only.js:2
#: dist/translation-strings.js:351
msgid "Dynamic Content"
msgstr ""

#: dist/translation-strings.php:1714
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:380
msgid "Field cannot be empty"
msgstr ""

#: dist/translation-strings.php:1715
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:381
msgid "Slug contains invalid characters"
msgstr ""

#: dist/translation-strings.php:1716
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:382
msgid "Slug must be unique"
msgstr ""

#: dist/translation-strings.php:1717
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:383
msgid "https://"
msgstr ""

#: dist/translation-strings.php:1718
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:384
msgid "Link title"
msgstr ""

#: dist/translation-strings.php:1719
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:385
msgid "Do you want to delete this field?"
msgstr ""

#: dist/translation-strings.php:1720
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:386
msgid "Add New Field"
msgstr ""

#: dist/translation-strings.php:1721
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:387
msgid "Field Type"
msgstr ""

#: dist/translation-strings.php:1722
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:390
msgid "Date"
msgstr ""

#: dist/translation-strings.php:1723
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:391
msgid "Time"
msgstr ""

#: dist/translation-strings.php:1724
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:392
msgid "Url"
msgstr ""

#: dist/translation-strings.php:1725
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:393
msgid "Field Name"
msgstr ""

#: dist/translation-strings.php:1726
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:394
msgid "Field Slug"
msgstr ""

#: dist/translation-strings.php:1727
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:395
msgid "A unique string that will be used to identify this field. Must contain only letters, numbers, underscores and dashes."
msgstr ""

#: dist/translation-strings.php:1728
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:397
msgid "Save field"
msgstr ""

#: dist/translation-strings.php:1729
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:398
msgid "Add a custom field now to start exploring the possibilities."
msgstr ""

#: dist/translation-strings.php:1730
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:399
msgid "Add field"
msgstr ""

#: dist/translation-strings.php:1731
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:400
msgid "You don't have any custom fields."
msgstr ""

#: dist/translation-strings.php:1732
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:401
msgid "Save changes"
msgstr ""

#: dist/translation-strings.php:1733
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:402
msgid "You have unsaved changes"
msgstr ""

#: dist/translation-strings.php:1734
#: dist/admin_custom_fields__premium_only.js:2
#: dist/translation-strings.js:403
msgid "Error in saving content"
msgstr ""

#: dist/translation-strings.php:1735
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:415
msgid "Offset your posts by a specific number of items"
msgstr ""

#: dist/translation-strings.php:1736
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:419
msgid "Show previous and next buttons"
msgstr ""

#: dist/translation-strings.php:1737
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:420
msgid "Previous label"
msgstr ""

#: dist/translation-strings.php:1738
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:421
msgid "Next label"
msgstr ""

#: dist/translation-strings.php:1739
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:422
msgid "Hover & Active Opacity"
msgstr ""

#: dist/translation-strings.php:1740
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:423
msgid "Hover & Active Colors"
msgstr ""

#: dist/translation-strings.php:1741
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:427
msgid "Reverse columns"
msgstr ""

#: dist/translation-strings.php:1742
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:428
msgid "Collapsed Row Gap"
msgstr ""

#: dist/translation-strings.php:1743
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:429
msgid "Collapsed Col. Arrangement"
msgstr ""

#: dist/translation-strings.php:1744
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:435
msgid "Collapse image on Mobile"
msgstr ""

#: dist/translation-strings.php:1745
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:436
msgid "Collapse image height"
msgstr ""

#: dist/translation-strings.php:1746
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:438
msgid "Override settings for column %d"
msgstr ""

#: dist/translation-strings.php:1747
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:439
msgid "Column Background"
msgstr ""

#: dist/translation-strings.php:1748
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:441
msgid "Tilt"
msgstr ""

#: dist/translation-strings.php:1749
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:442
msgid "Zoom & Tilt"
msgstr ""

#: dist/translation-strings.php:1750
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:443
msgid "Up"
msgstr ""

#: dist/translation-strings.php:1751
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:444
msgid "Down"
msgstr ""

#: dist/translation-strings.php:1752
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:447
msgid "Blur In"
msgstr ""

#: dist/translation-strings.php:1753
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:448
msgid "Blur Out"
msgstr ""

#: dist/translation-strings.php:1754
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:449
msgid "Grayscale In"
msgstr ""

#: dist/translation-strings.php:1755
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:450
msgid "Grayscale Out"
msgstr ""

#: dist/translation-strings.php:1756
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:451
msgid "Box Hover Effect"
msgstr ""

#: dist/translation-strings.php:1757
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:452
msgid "Number Background"
msgstr ""

#: dist/translation-strings.php:1758
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:457
msgid "Layer Color"
msgstr ""

#: dist/translation-strings.php:1759
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:462
msgid "Bubble Background"
msgstr ""

#: dist/translation-strings.php:1760
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:458
msgid "Color on Hover"
msgstr ""

#: dist/translation-strings.php:1761
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:406
msgid "%s More"
msgstr ""

#: dist/translation-strings.php:1762
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:407
msgid "Lift w/ shadow"
msgstr ""

#: dist/translation-strings.php:1763
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:408
msgid "Staggered lift"
msgstr ""

#: dist/translation-strings.php:1764
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:409
msgid "Staggered lift w/ shadow"
msgstr ""

#: dist/translation-strings.php:1765
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:410
msgid "Scale w/ shadow"
msgstr ""

#: dist/translation-strings.php:1766
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:411
msgid "Lower"
msgstr ""

#: dist/translation-strings.php:1767
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:412
msgid "Grayscale Hover Effect"
msgstr ""

#: dist/translation-strings.php:1768
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:463
msgid "Column / Container Spacing"
msgstr ""

#: dist/translation-strings.php:1769
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:465
msgid "Min. Column Height"
msgstr ""

#: dist/translation-strings.php:1770
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:453
msgid "Column Header"
msgstr ""

#: dist/translation-strings.php:1771
#: dist/deprecated/editor_blocks_deprecated_v2__premium_only.js:2
#: dist/translation-strings.js:454
msgctxt "Nth Title"
msgid "%s #%d"
msgstr ""

#: dist/translation-strings.php:1772
#: pro__premium_only/src/custom-fields.php:88
msgid "Settings that control custom fields functionality and permissions."
msgstr ""

#: dist/translation-strings.php:1773
#: pro__premium_only/src/editor-mode.php:68
msgid "Roles which only allow content only editing."
msgstr ""

#: dist/translation-strings.php:1774
#: pro__premium_only/src/icons.php:169
msgid "Hold on! We noticed that you're using the Font Awesome plugin and that you're using a free Kit. If you have a FontAwesome Pro subscription, you can just set your Kit to use Pro Icons, and you should be able to use your Pro Icons inside your Stackable blocks. %sLearn more about this here.%s"
msgstr ""

#: dist/translation-strings.php:1775
#: pro__premium_only/src/icons.php:173
msgid "Hold on! We noticed that you're using the Font Awesome plugin and that you're using the free CDN. If you have a FontAwesome Pro subscription, you can just set your CDN to use Pro Icons, and you should be able to use your Pro Icons inside your Stackable blocks. %sLearn more about this here.%s"
msgstr ""

#: dist/translation-strings.php:1776
#: pro__premium_only/src/icons.php:176
#: pro__premium_only/src/icons.php:180
msgid "Good news! We noticed that you're using the Font Awesome plugin. Your Font Awesome Pro icons are already available inside your Stackable blocks."
msgstr ""

#: dist/translation-strings.php:1777
#: pro__premium_only/src/icons.php:177
msgid "Make sure you need to add your WordPress site to the %sallowed domains for your CDN%s."
msgstr ""

#: dist/translation-strings.php:1778
#: pro__premium_only/src/stk-block-types.php:16
#: dist/translation-strings.js:468
msgid "Load more button for your Stackable Posts block"
msgstr ""

#: dist/translation-strings.php:1779
#: pro__premium_only/src/stk-block-types.php:44
#: dist/translation-strings.js:469
msgid "Pagination for your Stackable Posts block"
msgstr ""

#: dist/translation-strings.php:1780
#: pro__premium_only/src/dynamic-content/init.php:304
msgid "Invalid parameters. Please try again."
msgstr ""

#: dist/translation-strings.php:1781
#: pro__premium_only/src/welcome/icons.php:63
msgid "Don't show Font Awesome plugin settings error"
msgstr ""

#: dist/translation-strings.php:1782
#: pro__premium_only/src/welcome/icons.php:75
msgid "Font Awesome Kit ID"
msgstr ""

#: dist/translation-strings.php:1783
#: pro__premium_only/src/welcome/icons.php:87
msgid "Font Awesome icon version to server"
msgstr ""

#: dist/translation-strings.php:1784
#: pro__premium_only/src/block/load-more/index.php:49
#: pro__premium_only/src/deprecated/v2/block/blog-posts/pagination.php:28
#: pro__premium_only/src/plugins/icon-library/api.php:83
#: src/custom-block-styles.php:157
#: src/design-library/init.php:36
msgid "%s must be a string."
msgstr ""

#: dist/translation-strings.php:1785
#: pro__premium_only/src/components/panel-design-user-library/ajax.php:21
#: pro__premium_only/src/components/panel-design-user-library/ajax.php:47
#: src/deprecated/v2/disabled-blocks.php:62
#: src/welcome/news.php:136
msgid "Security error, please refresh the page and try again."
msgstr ""

#: dist/translation-strings.php:1786
#: pro__premium_only/src/components/panel-design-user-library/ajax.php:28
#: pro__premium_only/src/components/panel-design-user-library/ajax.php:53
msgid "Invalid arguments."
msgstr ""

#: dist/translation-strings.php:1787
#: pro__premium_only/src/dynamic-content/sources/acf.php:57
msgid "ACF"
msgstr ""

#: dist/translation-strings.php:1788
#: pro__premium_only/src/dynamic-content/sources/acf.php:201
#: pro__premium_only/src/dynamic-content/sources/acf.php:229
#: pro__premium_only/src/dynamic-content/sources/custom-fields.php:97
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:183
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:580
#: pro__premium_only/src/dynamic-content/sources/metabox.php:308
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:100
#: pro__premium_only/src/dynamic-content/sources/site.php:77
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:67
msgid "The field type provided is not valid."
msgstr ""

#: dist/translation-strings.php:1789
#: pro__premium_only/src/dynamic-content/sources/acf.php:359
#: pro__premium_only/src/dynamic-content/sources/acf.php:555
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:364
#: pro__premium_only/src/dynamic-content/sources/metabox.php:409
#: pro__premium_only/src/dynamic-content/sources/metabox.php:595
#: pro__premium_only/src/dynamic-content/sources/metabox.php:655
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:364
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:659
#: pro__premium_only/src/dynamic-content/sources/site.php:134
msgid "Text input is empty"
msgstr ""

#: dist/translation-strings.php:1790
#: pro__premium_only/src/dynamic-content/sources/acf.php:417
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:390
#: pro__premium_only/src/dynamic-content/sources/metabox.php:472
msgid "`whenTrueText` and `whenFalseText` arguments are required."
msgstr ""

#: dist/translation-strings.php:1791
#: pro__premium_only/src/dynamic-content/sources/custom-fields.php:41
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:88
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:137
msgid "Stackable Custom Fields"
msgstr ""

#: dist/translation-strings.php:1792
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:62
#: pro__premium_only/src/dynamic-content/sources/jetengine.php:86
msgid "JetEngine"
msgstr ""

#: dist/translation-strings.php:1793
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:53
msgid "Latest Post"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1794
#: dist/translation-strings.php:1797
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:57
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:59
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:102
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:107
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:112
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:117
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:122
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:127
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:132
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:137
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:142
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:147
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:230
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:231
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:232
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:233
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:234
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:235
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:236
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:237
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:238
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:239
msgid "%s Latest %s"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1795
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:57
msgid "Nth"
msgstr ""

#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#. translators: first %s is an ordinal number (e.g. 1st, 2nd), second %s is the name of the entity (e.g. Post, Page)
#: dist/translation-strings.php:1796
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:57
#: pro__premium_only/src/dynamic-content/sources/latest-post.php:59
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:219
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:223
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:228
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:232
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:236
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:240
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:244
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:248
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:252
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:256
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:260
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:264
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:837
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:871
msgid "Post"
msgstr ""

#: dist/translation-strings.php:1798
#: pro__premium_only/src/dynamic-content/sources/metabox.php:74
msgid "Meta Box"
msgstr ""

#: dist/translation-strings.php:1799
#: pro__premium_only/src/dynamic-content/sources/metabox.php:196
msgid "Meta Box | "
msgstr ""

#: dist/translation-strings.php:1800
#: pro__premium_only/src/dynamic-content/sources/metabox.php:249
msgid "Multiple select not supported in this field."
msgstr ""

#: dist/translation-strings.php:1801
#: pro__premium_only/src/dynamic-content/sources/metabox.php:643
msgid "You have not selected an image."
msgstr ""

#: dist/translation-strings.php:1802
#: pro__premium_only/src/dynamic-content/sources/metabox.php:674
msgid "Text fields are empty"
msgstr ""

#: dist/translation-strings.php:1803
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:32
msgid "Other Posts"
msgstr ""

#: dist/translation-strings.php:1804
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:35
msgid "Posts/Pages"
msgstr ""

#: dist/translation-strings.php:1805
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:36
msgid "Search for posts/pages"
msgstr ""

#: dist/translation-strings.php:1806
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:118
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:180
msgid "Detected Custom Fields"
msgstr ""

#: dist/translation-strings.php:1807
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:218
msgid "Post Title"
msgstr ""

#: dist/translation-strings.php:1808
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:222
msgid "Post URL"
msgstr ""

#: dist/translation-strings.php:1809
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:227
msgid "Post ID"
msgstr ""

#: dist/translation-strings.php:1810
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:231
msgid "Post Slug"
msgstr ""

#: dist/translation-strings.php:1811
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:239
msgid "Post Excerpt"
msgstr ""

#: dist/translation-strings.php:1812
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:243
msgid "Post Date"
msgstr ""

#: dist/translation-strings.php:1813
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:247
msgid "Post Date GMT"
msgstr ""

#: dist/translation-strings.php:1814
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:251
msgid "Post Modified"
msgstr ""

#: dist/translation-strings.php:1815
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:255
msgid "Post Modified GMT"
msgstr ""

#: dist/translation-strings.php:1816
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:263
msgid "Post Status"
msgstr ""

#: dist/translation-strings.php:1817
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:268
msgid "Author Name"
msgstr ""

#: dist/translation-strings.php:1818
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:272
msgid "Author ID"
msgstr ""

#: dist/translation-strings.php:1819
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:276
msgid "Author Posts URL"
msgstr ""

#: dist/translation-strings.php:1820
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:281
msgid "Author Profile Picture URL"
msgstr ""

#: dist/translation-strings.php:1821
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:286
msgid "Author Posts"
msgstr ""

#: dist/translation-strings.php:1822
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:290
msgid "Author First Name"
msgstr ""

#: dist/translation-strings.php:1823
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:294
msgid "Author Last Name"
msgstr ""

#: dist/translation-strings.php:1824
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:299
msgid "Comment Number"
msgstr ""

#: dist/translation-strings.php:1825
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:300
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:304
msgid "Comment"
msgstr ""

#: dist/translation-strings.php:1826
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:303
msgid "Comment Status"
msgstr ""

#: dist/translation-strings.php:1827
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:308
msgid "Featured Image URL"
msgstr ""

#: dist/translation-strings.php:1828
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:309
msgid "Media"
msgstr ""

#: dist/translation-strings.php:1829
#: pro__premium_only/src/dynamic-content/sources/other-posts.php:491
msgid "Post not found."
msgstr ""

#: dist/translation-strings.php:1830
#: pro__premium_only/src/dynamic-content/sources/site.php:27
#: pro__premium_only/src/dynamic-content/sources/site.php:45
#: pro__premium_only/src/dynamic-content/sources/site.php:49
#: pro__premium_only/src/dynamic-content/sources/site.php:53
msgid "Site"
msgstr ""

#: dist/translation-strings.php:1831
#: pro__premium_only/src/dynamic-content/sources/site.php:44
msgid "Site Tagline"
msgstr ""

#: dist/translation-strings.php:1832
#: pro__premium_only/src/dynamic-content/sources/site.php:48
msgid "Site Title"
msgstr ""

#: dist/translation-strings.php:1833
#: pro__premium_only/src/dynamic-content/sources/site.php:52
msgid "Site URL"
msgstr ""

#: dist/translation-strings.php:1834
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:91
msgid "Product Url"
msgstr ""

#: dist/translation-strings.php:1835
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:101
msgid "Product Name"
msgstr ""

#: dist/translation-strings.php:1836
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:108
msgid "Product Description"
msgstr ""

#: dist/translation-strings.php:1837
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:115
msgid "Product Short Description"
msgstr ""

#: dist/translation-strings.php:1838
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:122
msgid "Product Purchase Note"
msgstr ""

#: dist/translation-strings.php:1839
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:129
msgid "Product Image"
msgstr ""

#: dist/translation-strings.php:1840
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:136
msgid "Product Price"
msgstr ""

#: dist/translation-strings.php:1841
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:143
msgid "Product Price (Regular)"
msgstr ""

#: dist/translation-strings.php:1842
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:150
msgid "Product Price (No Tax)"
msgstr ""

#: dist/translation-strings.php:1843
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:157
msgid "Produce Price (Sale)"
msgstr ""

#: dist/translation-strings.php:1844
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:164
msgid "Product Date Created"
msgstr ""

#: dist/translation-strings.php:1845
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:171
msgid "Product Sale Date From"
msgstr ""

#: dist/translation-strings.php:1846
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:178
msgid "Product Sale Date To"
msgstr ""

#: dist/translation-strings.php:1847
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:185
msgid "Product Add to Cart URL"
msgstr ""

#: dist/translation-strings.php:1848
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:193
msgid "Product SKU"
msgstr ""

#: dist/translation-strings.php:1849
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:200
msgid "Product Total Sales"
msgstr ""

#: dist/translation-strings.php:1850
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:207
msgid "Product Total Stock"
msgstr ""

#: dist/translation-strings.php:1851
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:214
msgid "Product Low Stock"
msgstr ""

#: dist/translation-strings.php:1852
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:221
msgid "Product Weight"
msgstr ""

#: dist/translation-strings.php:1853
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:228
msgid "Product Width"
msgstr ""

#: dist/translation-strings.php:1854
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:235
msgid "Product Length"
msgstr ""

#: dist/translation-strings.php:1855
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:242
msgid "Product Height"
msgstr ""

#: dist/translation-strings.php:1856
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:249
msgid "Product Review Count"
msgstr ""

#: dist/translation-strings.php:1857
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:256
msgid "Product Tax Status"
msgstr ""

#: dist/translation-strings.php:1858
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:263
msgid "Product Tax Class"
msgstr ""

#: dist/translation-strings.php:1859
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:270
msgid "Product Tags"
msgstr ""

#: dist/translation-strings.php:1860
#: pro__premium_only/src/dynamic-content/sources/woocommerce.php:277
msgid "Product Attributes"
msgstr ""

#: dist/translation-strings.php:1861
#: pro__premium_only/src/plugins/color-schemes/index.php:19
#: src/plugins/global-settings/color-schemes/index.php:54
msgid "Stackable Global Color Schemes"
msgstr ""

#: dist/translation-strings.php:1862
#: pro__premium_only/src/plugins/icon-library/api.php:90
msgid "%s must be an integer."
msgstr ""

#: dist/translation-strings.php:1863
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:48
msgid "Data from Stackable custom fields"
msgstr ""

#: dist/translation-strings.php:1864
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:146
msgid "📋 Custom Fields"
msgstr ""

#: dist/translation-strings.php:1865
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:151
msgid "You can add small pieces of content here which you can use across your website - things like your contact email or the number of customers you've served. You can find these fields under the \"Site source\" area when placing \"Dynamic Content\" in your blocks. %sLearn more%s."
msgstr ""

#: dist/translation-strings.php:1866
#: pro__premium_only/src/welcome/custom-fields/custom-fields.php:160
msgid "This is only available in Stackable Premium."
msgstr ""

#: plugin.php:49
msgid "%s\"Stackable\" can not be activated. %s It requires PHP version 7.3.0 or higher, but PHP version %s is used on the site. Please upgrade your PHP version first ✌️ %s Back %s"
msgstr ""

#: plugin.php:73
msgid "\"Stackable\" requires PHP version 7.3.0 or higher, but PHP version %s is used on the site."
msgstr ""

#: plugin.php:134
msgid "%sStackable Notice%s: We noticed that the Gutenberg plugin is active! Please be aware the Gutenberg plugin is used to try out the new Block Editor features, and Stackable might not be compatible with it. Click the close button on the side to dismiss this notice."
msgstr ""

#: src/block/map/index.php:20
msgid "This map block uses settings that require a Google Maps API key, but it is missing. Please enter your Google Maps API key in the Stackable settings, or edit this map block."
msgstr ""

#: src/block/posts/index.php:110
#: src/block/posts/index.php:474
#: src/deprecated/v2/block/blog-posts/index.php:289
#: src/deprecated/v2/block/blog-posts/index.php:543
msgid "%d comment"
msgid_plural "%d comments"
msgstr[0] ""
msgstr[1] ""

#: src/block/posts/index.php:438
#: src/deprecated/v2/block/blog-posts/index.php:265
#: src/deprecated/v2/block/blog-posts/index.php:554
msgid ", "
msgstr ""

#: src/custom-block-styles.php:69
msgid "Stackable User Block Styles"
msgstr ""

#: src/custom-block-styles.php:164
#: src/design-library/init.php:43
msgid "%s must be a boolean."
msgstr ""

#: src/custom-block-styles.php:283
msgid "Default Blocks"
msgstr ""

#: src/custom-block-styles.php:284
msgid "Default Block"
msgstr ""

#: src/custom-block-styles.php:285
msgid "Saving, please wait..."
msgstr ""

#: src/custom-block-styles.php:431
msgid "Default %s Block"
msgstr ""

#: src/deprecated/v2/block/blog-posts/index.php:418
msgid "Different sized featured images"
msgstr ""

#: src/deprecated/v2/block/blog-posts/index.php:430
msgid "Post excerpt for Stackable"
msgstr ""

#: src/deprecated/v2/block/blog-posts/index.php:442
msgid "Category list links"
msgstr ""

#: src/deprecated/v2/block/blog-posts/index.php:454
msgid "Author information"
msgstr ""

#: src/deprecated/v2/block/blog-posts/index.php:466
msgid "Number of comments"
msgstr ""

#: src/deprecated/v2/init.php:57
msgid "Load version 2 frontend styles and scripts"
msgstr ""

#: src/deprecated/v2/init.php:86
msgid "Load version 2 blocks when old blocks are used"
msgstr ""

#: src/deprecated/v2/init.php:100
msgid "This disables the v2 block detected in the editor"
msgstr ""

#: src/deprecated/v2/init.php:114
msgid "This disables the v2 block detected in the frontend"
msgstr ""

#: src/deprecated/v2/optimization-settings.php:50
msgid "Stackable optimization setting, only load scripts when there are Stackable blocks in the page"
msgstr ""

#: src/dynamic-breakpoints.php:111
msgid "Custom dynamic breakpoints"
msgstr ""

#: src/editor-settings.php:40
msgid "Blocks that should be hidden in the block editor"
msgstr ""

#: src/editor-settings.php:59
msgid "Enables additional customization options for the Map Block."
msgstr ""

#: src/editor-settings.php:71
msgid "Hides the Stackable Design Library button on the top of the editor"
msgstr ""

#: src/editor-settings.php:83
msgid "Optimizes inlined CSS styles, combines together similar selectors"
msgstr ""

#: src/editor-settings.php:95
msgid "The width used when a Columns block has its Content Width set to center."
msgstr ""

#: src/editor-settings.php:107
msgid "The width used when a Columns block has its Content Width set to wide."
msgstr ""

#: src/editor-settings.php:131
msgid "Allow the configuration of global settings such as color palette, typography, and block defaults"
msgstr ""

#: src/editor-settings.php:166
msgid "Disables image lazy loading when using images inside carousel-type blocks to prevent space or layout issues ."
msgstr ""

#: src/editor-settings.php:190
msgid "Adds a toolbar button for inserting dynamic content"
msgstr ""

#: src/editor-settings.php:202
msgid "Adds a toolbar button for copying and pasting block styles"
msgstr ""

#: src/editor-settings.php:214
msgid "Adds a toolbar button for resetting the layout of a block"
msgstr ""

#: src/editor-settings.php:226
msgid "Adds a toolbar button for saving the current block variation as the default block"
msgstr ""

#: src/editor-settings.php:238
msgid "If this is enabled, the default block when adding a new block will be the Stackable Text block."
msgstr ""

#: src/global-settings.php:185
msgid "Stackable global color palette"
msgstr ""

#: src/global-settings.php:220
msgid "Hide theme colors in the Stackable color picker"
msgstr ""

#: src/global-settings.php:232
msgid "Hide default colors in the Stackable color picker"
msgstr ""

#: src/global-settings.php:244
msgid "Hide Site Editor colors in the Stackable color picker"
msgstr ""

#: src/global-settings.php:256
msgid "Stackable global typography apply to setting"
msgstr ""

#: src/global-settings.php:268
msgid "Stackable global typography add important to global styles"
msgstr ""

#: src/global-settings.php:340
msgid "Stackable global typography settings"
msgstr ""

#: src/global-settings.php:370
msgid "Stackable currently selected global font pair"
msgstr ""

#: src/global-settings.php:382
msgid "Stackable added custom font pairs"
msgstr ""

#: src/global-settings.php:420
msgid "Stackable Icon Library"
msgstr ""

#: src/icons.php:40
msgid "Select FontAwesome version"
msgstr ""

#: src/plugins/global-settings/buttons-and-icons/index.php:50
msgid "Stackable global buttons and icons"
msgstr ""

#: src/plugins/global-settings/color-schemes/index.php:81
msgid "Hide color scheme colors in the Stackable color picker"
msgstr ""

#: src/plugins/global-settings/color-schemes/index.php:93
msgid "Stackable Global Base Color Scheme"
msgstr ""

#: src/plugins/global-settings/color-schemes/index.php:105
msgid "Stackable Global Background Mode Color Scheme"
msgstr ""

#: src/plugins/global-settings/color-schemes/index.php:117
msgid "Stackable Global Container Mode Color Scheme"
msgstr ""

#: src/plugins/global-settings/color-schemes/index.php:129
msgid "Stackable Global Color Scheme Generated CSS"
msgstr ""

#: src/plugins/global-settings/spacing-and-borders/index.php:50
msgid "Stackable global spacing and borders"
msgstr ""

#: src/pro.php:49
msgid "Hide \"Go Premium\" notices"
msgstr ""

#: src/pro.php:133
msgid "We hope you're enjoying Stackable. If you want more, you may want to check out %sStackable Premium%s. Ready to upgrade and do more? %sGo premium now%s"
msgstr ""

#: src/stk-block-types.php:15
#: dist/translation-strings.js:473
msgid "Accordion"
msgstr ""

#: src/stk-block-types.php:16
#: dist/translation-strings.js:474
msgid "A title that your visitors can toggle to view more text. Use as FAQs or multiple ones for an Accordion."
msgstr ""

#: src/stk-block-types.php:25
#: dist/translation-strings.js:475
msgid "Toggle"
msgstr ""

#: src/stk-block-types.php:26
#: dist/translation-strings.js:476
msgid "Faq"
msgstr ""

#: src/stk-block-types.php:43
#: dist/translation-strings.js:477
msgid "Blockquote"
msgstr ""

#: src/stk-block-types.php:44
#: dist/translation-strings.js:478
msgid "Display a quote in style"
msgstr ""

#: src/stk-block-types.php:66
#: src/stk-block-types.php:89
#: src/stk-block-types.php:104
#: src/stk-block-types.php:112
#: src/stk-block-types.php:538
#: dist/translation-strings.js:480
msgid "Add a customizable button."
msgstr ""

#: src/stk-block-types.php:88
#: dist/translation-strings.js:479
msgid "Button Group"
msgstr ""

#: src/stk-block-types.php:135
#: dist/translation-strings.js:486
msgid "Call to Action"
msgstr ""

#: src/stk-block-types.php:136
#: dist/translation-strings.js:487
msgid "A small section you can use to call the attention of your visitors. Great for calling attention to your products or deals."
msgstr ""

#: src/stk-block-types.php:148
#: dist/translation-strings.js:488
msgid "CTA"
msgstr ""

#: src/stk-block-types.php:163
#: dist/translation-strings.js:489
msgid "Card"
msgstr ""

#: src/stk-block-types.php:164
#: dist/translation-strings.js:490
msgid "Describe a single subject in a small card. You can use this to describe your product, service or a person."
msgstr ""

#: src/stk-block-types.php:189
#: src/stk-block-types.php:485
#: dist/translation-strings.js:491
msgid "Carousel"
msgstr ""

#: src/stk-block-types.php:190
#: dist/translation-strings.js:492
msgid "A carousel slider."
msgstr ""

#: src/stk-block-types.php:199
#: src/stk-block-types.php:484
#: dist/translation-strings.js:493
msgid "Slider"
msgstr ""

#: src/stk-block-types.php:208
#: dist/translation-strings.js:494
msgid "Inner Column"
msgstr ""

#: src/stk-block-types.php:209
#: dist/translation-strings.js:495
msgid "A single column with advanced layout options."
msgstr ""

#: src/stk-block-types.php:222
#: src/stk-block-types.php:251
#: dist/translation-strings.js:496
msgid "Section rows"
msgstr ""

#: src/stk-block-types.php:243
#: dist/translation-strings.js:498
msgid "Multiple columns with advanced layout options."
msgstr ""

#: src/stk-block-types.php:269
#: dist/translation-strings.js:499
msgid "Count Up"
msgstr ""

#: src/stk-block-types.php:270
#: dist/translation-strings.js:500
msgid "Showcase your stats. Display how many customers you have or the number of downloads of your app."
msgstr ""

#: src/stk-block-types.php:288
#: dist/translation-strings.js:501
msgid "Countdown"
msgstr ""

#: src/stk-block-types.php:289
#: dist/translation-strings.js:502
msgid "Display a countdown timer on your website."
msgstr ""

#: src/stk-block-types.php:298
#: dist/translation-strings.js:503
msgid "Timer"
msgstr ""

#: src/stk-block-types.php:308
#: dist/translation-strings.js:505
msgid "Choose a layout or block from the Stackable Design Library."
msgstr ""

#: src/stk-block-types.php:317
#: dist/translation-strings.js:506
msgid "Template"
msgstr ""

#: src/stk-block-types.php:330
#: dist/translation-strings.js:507
msgid "Divider"
msgstr ""

#: src/stk-block-types.php:331
#: dist/translation-strings.js:508
msgid "Add a pause between your content."
msgstr ""

#: src/stk-block-types.php:340
#: dist/translation-strings.js:509
msgid "Horizontal Rule"
msgstr ""

#: src/stk-block-types.php:341
#: dist/translation-strings.js:510
msgid "HR"
msgstr ""

#: src/stk-block-types.php:349
#: dist/translation-strings.js:511
msgid "Expand / Show More"
msgstr ""

#: src/stk-block-types.php:350
#: dist/translation-strings.js:512
msgid "Display a small snippet of text. Your readers can toggle it to show more information."
msgstr ""

#: src/stk-block-types.php:359
#: dist/translation-strings.js:513
msgid "Hide"
msgstr ""

#: src/stk-block-types.php:360
#: dist/translation-strings.js:514
msgid "Less"
msgstr ""

#: src/stk-block-types.php:373
#: dist/translation-strings.js:517
msgid "Feature"
msgstr ""

#: src/stk-block-types.php:374
#: dist/translation-strings.js:518
msgid "Display a product feature or a service in a large area."
msgstr ""

#: src/stk-block-types.php:401
#: dist/translation-strings.js:515
msgid "Feature Grid"
msgstr ""

#: src/stk-block-types.php:402
#: dist/translation-strings.js:516
msgid "Display multiple product features or services. You can use Feature Grids one after another."
msgstr ""

#: src/stk-block-types.php:428
#: dist/translation-strings.js:520
msgid "Introduce new sections of your content in style."
msgstr ""

#: src/stk-block-types.php:446
#: dist/translation-strings.js:521
msgid "Hero"
msgstr ""

#: src/stk-block-types.php:447
#: dist/translation-strings.js:522
msgid "A large hero area. Typically used at the very top of a page."
msgstr ""

#: src/stk-block-types.php:459
#: dist/translation-strings.js:523
msgid "Header"
msgstr ""

#: src/stk-block-types.php:474
#: dist/translation-strings.js:524
msgid "Horizontal Scroller"
msgstr ""

#: src/stk-block-types.php:475
#: dist/translation-strings.js:525
msgid "A slider that scrolls horizontally."
msgstr ""

#: src/stk-block-types.php:498
#: dist/translation-strings.js:538
msgid "Pick an icon or upload your own SVG icon to decorate your content."
msgstr ""

#: src/stk-block-types.php:507
#: src/stk-block-types.php:570
#: dist/translation-strings.js:530
msgid "SVG"
msgstr ""

#: src/stk-block-types.php:517
#: dist/translation-strings.js:527
msgid "A small text area with an icon that can be used to summarize features or services"
msgstr ""

#: src/stk-block-types.php:561
#: dist/translation-strings.js:529
msgid "An Icon and Heading paired together."
msgstr ""

#: src/stk-block-types.php:584
#: dist/translation-strings.js:534
msgid "An unordered list with icons. You can use this as a list of features or benefits."
msgstr ""

#: src/stk-block-types.php:593
#: dist/translation-strings.js:535
msgid "Checklist"
msgstr ""

#: src/stk-block-types.php:594
#: dist/translation-strings.js:536
msgid "Bullets"
msgstr ""

#: src/stk-block-types.php:595
#: dist/translation-strings.js:537
msgid "Number list"
msgstr ""

#: src/stk-block-types.php:608
#: dist/translation-strings.js:531
msgid "Icon List Item"
msgstr ""

#: src/stk-block-types.php:609
#: dist/translation-strings.js:532
msgid "A single list entry in the Icon List block"
msgstr ""

#: src/stk-block-types.php:633
#: dist/translation-strings.js:541
msgid "An image with advanced controls to make a visual statement."
msgstr ""

#: src/stk-block-types.php:648
#: dist/translation-strings.js:539
msgid "Image Box"
msgstr ""

#: src/stk-block-types.php:649
#: dist/translation-strings.js:540
msgid "Display an image that shows more information when hovered on. Can be used as a fancy link to other pages."
msgstr ""

#: src/stk-block-types.php:674
#: dist/translation-strings.js:543
msgid "Embedded Google Map with advanced controls."
msgstr ""

#: src/stk-block-types.php:684
#: dist/translation-strings.js:544
msgid "location"
msgstr ""

#: src/stk-block-types.php:685
#: dist/translation-strings.js:545
msgid "address"
msgstr ""

#: src/stk-block-types.php:693
#: dist/translation-strings.js:546
msgid "Notification"
msgstr ""

#: src/stk-block-types.php:694
#: dist/translation-strings.js:547
msgid "Show a notice to your readers. People can dismiss the notice to permanently hide it."
msgstr ""

#: src/stk-block-types.php:706
#: dist/translation-strings.js:548
msgid "Notice"
msgstr ""

#: src/stk-block-types.php:707
#: dist/translation-strings.js:549
msgid "Alert"
msgstr ""

#: src/stk-block-types.php:725
#: dist/translation-strings.js:550
msgid "Number Box"
msgstr ""

#: src/stk-block-types.php:726
#: dist/translation-strings.js:551
msgid "Display steps or methods that your users will do in your service."
msgstr ""

#: src/stk-block-types.php:735
#: dist/translation-strings.js:552
msgid "Steps"
msgstr ""

#: src/stk-block-types.php:745
#: dist/translation-strings.js:554
msgid "Your latest blog posts. Use this to showcase a few of your posts in your landing pages."
msgstr ""

#: src/stk-block-types.php:754
#: dist/translation-strings.js:555
msgid "Blog Posts"
msgstr ""

#: src/stk-block-types.php:755
#: dist/translation-strings.js:556
msgid "Lastest Posts"
msgstr ""

#: src/stk-block-types.php:756
#: dist/translation-strings.js:557
msgid "Query Loop"
msgstr ""

#: src/stk-block-types.php:779
#: dist/translation-strings.js:559
msgid "Show a price of a product or service with currency and a suffix styled with different weights"
msgstr ""

#: src/stk-block-types.php:788
#: src/stk-block-types.php:815
#: dist/translation-strings.js:560
msgid "Currency"
msgstr ""

#: src/stk-block-types.php:789
#: dist/translation-strings.js:561
msgid "Pricing"
msgstr ""

#: src/stk-block-types.php:802
#: dist/translation-strings.js:562
msgid "Pricing Box"
msgstr ""

#: src/stk-block-types.php:803
#: dist/translation-strings.js:563
msgid "Display the different pricing tiers of your business."
msgstr ""

#: src/stk-block-types.php:817
#: dist/translation-strings.js:564
msgid "Pricing Table"
msgstr ""

#: src/stk-block-types.php:838
#: dist/translation-strings.js:566
msgid "Visualize a progress value or percentage in a bar."
msgstr ""

#: src/stk-block-types.php:847
#: src/stk-block-types.php:866
#: dist/translation-strings.js:567
msgid "percentage status"
msgstr ""

#: src/stk-block-types.php:857
#: dist/translation-strings.js:569
msgid "Visualize a progress value or percentage in a circle."
msgstr ""

#: src/stk-block-types.php:876
#: dist/translation-strings.js:571
msgid "A fancy separator to be placed between content."
msgstr ""

#: src/stk-block-types.php:885
#: dist/translation-strings.js:572
msgid "Svg Divider"
msgstr ""

#: src/stk-block-types.php:895
#: dist/translation-strings.js:574
msgid "Sometimes you just need some space."
msgstr ""

#: src/stk-block-types.php:910
#: dist/translation-strings.js:576
msgid "Subtitle text that you can add custom styling to from the global settings."
msgstr ""

#: src/stk-block-types.php:925
#: dist/translation-strings.js:577
msgid "Tab Content"
msgstr ""

#: src/stk-block-types.php:926
#: dist/translation-strings.js:578
msgid "A wrapper for tab panels."
msgstr ""

#: src/stk-block-types.php:948
#: dist/translation-strings.js:579
msgid "Tab Labels"
msgstr ""

#: src/stk-block-types.php:949
#: dist/translation-strings.js:580
msgid "Create interactive navigation within tabs."
msgstr ""

#: src/stk-block-types.php:972
#: dist/translation-strings.js:582
msgid "Automatically generated table of contents based on Heading blocks."
msgstr ""

#: src/stk-block-types.php:981
#: dist/translation-strings.js:583
msgid "ToC"
msgstr ""

#: src/stk-block-types.php:982
#: dist/translation-strings.js:584
msgid "Index"
msgstr ""

#: src/stk-block-types.php:993
#: dist/translation-strings.js:587
msgid "Organize and display content in multiple tabs."
msgstr ""

#: src/stk-block-types.php:1002
#: dist/translation-strings.js:588
msgid "toggle"
msgstr ""

#: src/stk-block-types.php:1017
#: dist/translation-strings.js:589
msgid "Team Member"
msgstr ""

#: src/stk-block-types.php:1018
#: dist/translation-strings.js:590
msgid "Display members of your team or your office. Use multiple Team Member blocks if you have a large team."
msgstr ""

#: src/stk-block-types.php:1045
#: dist/translation-strings.js:592
msgid "Showcase what your users say about your product or service."
msgstr ""

#: src/stk-block-types.php:1073
#: dist/translation-strings.js:593
msgid "Start with the building block of all page layouts."
msgstr ""

#: src/stk-block-types.php:1092
#: dist/translation-strings.js:596
msgid "Show events in chronological order"
msgstr ""

#: src/stk-block-types.php:1101
#: dist/translation-strings.js:597
msgid "history"
msgstr ""

#: src/stk-block-types.php:1102
#: dist/translation-strings.js:598
msgid "milestone"
msgstr ""

#: src/stk-block-types.php:1111
#: dist/translation-strings.js:599
msgid "Video Popup"
msgstr ""

#: src/stk-block-types.php:1112
#: dist/translation-strings.js:600
msgid "Display a large thumbnail that your users can click to play a video full-screen. Great for introductory or tutorial videos."
msgstr ""

#: src/stk-block-types.php:1122
#: dist/translation-strings.js:602
msgid "Vimeo"
msgstr ""

#: src/stk-block-types.php:1123
#: dist/translation-strings.js:603
msgid "Embed Mp4"
msgstr ""

#: src/welcome/index.php:40
#: src/welcome/index.php:41
msgid "Get Started"
msgstr ""

#: src/welcome/index.php:125
msgid "Account"
msgstr ""

#: src/welcome/index.php:132
msgid "Affiliation"
msgstr ""

#: src/welcome/index.php:142
msgid "Contact Us"
msgstr ""

#: src/welcome/index.php:170
#: src/welcome/index.php:216
msgid "Get Stackable Premium"
msgstr ""

#: src/welcome/index.php:170
msgid "Upgrade to Premium"
msgstr ""

#: src/welcome/index.php:199
msgid "🚀 Stackable Premium"
msgstr ""

#: src/welcome/index.php:200
msgid "If you are ready for even more, upgrade to Premium and get:"
msgstr ""

#: src/welcome/index.php:202
msgid "54+ Additional Block Layouts"
msgstr ""

#: src/welcome/index.php:203
msgid "230+ Additional Library Designs"
msgstr ""

#: src/welcome/index.php:206
msgid "Conditionally Display Blocks"
msgstr ""

#: src/welcome/index.php:207
msgid "User Role Manager"
msgstr ""

#: src/welcome/index.php:208
msgid "Font Awesome Pro Integration"
msgstr ""

#: src/welcome/index.php:209
msgid "Custom Block CSS"
msgstr ""

#: src/welcome/index.php:210
msgid "1 Year of Updates"
msgstr ""

#: src/welcome/index.php:211
msgid "1 Year of Support"
msgstr ""

#: src/welcome/index.php:212
msgid "No Ads"
msgstr ""

#: src/welcome/index.php:224
msgid "🎉 Join the Community"
msgstr ""

#: src/welcome/index.php:225
msgid "Join the very active Stackable Community in Facebook, join thousands of like-minded people who are also building their websites and crafting beautiful and impactful web pages."
msgstr ""

#: src/welcome/index.php:226
msgid "Join Facebook Community"
msgstr ""

#: src/welcome/index.php:229
msgid "🗞 Stackable Blog"
msgstr ""

#: src/welcome/index.php:231
msgid "Keep up to date by subscribing to our newsletter."
msgstr ""

#: src/welcome/index.php:232
msgid "Subscribe"
msgstr ""

#: src/welcome/notification-rate.php:41
msgid "If Stackable has been valuable to you, consider %sgiving us a 5-star rating on WordPress.org%s. It would not only make our day but also help others discover us too."
msgstr ""

#: src/welcome/notification.php:77
msgid "%s notification"
msgid_plural "%s notifications"
msgstr[0] ""
msgstr[1] ""

#: src/welcome/notification.php:132
msgid "👉 Notifications"
msgstr ""

#: src/welcome/notification.php:138
msgid "Don't show me this anymore"
msgstr ""

#: dist/admin_welcome.js:2
msgctxt "Indicates this palette comes from the theme."
msgid "Theme"
msgstr ""

#: dist/admin_welcome.js:2
msgctxt "Indicates this palette comes from WordPress."
msgid "Default"
msgstr ""

#: dist/admin_welcome.js:2
msgctxt "Indicates this palette comes from the theme."
msgid "Custom"
msgstr ""

#: dist/admin_welcome.js:2
msgid "(%d) Selected"
msgstr ""

#: dist/admin_welcome.js:2
msgid "You can customize some of the features and behavior of Stackable in the editor here."
msgstr ""

#: dist/admin_welcome.js:2
msgid "You can customize some of the features and behavior of Stackable in the inspector here."
msgstr ""

#: dist/admin_welcome.js:2
msgid "Adding a Google API Key enables additional features of the Stackable Map Block."
msgstr ""

#: dist/admin_welcome.js:2
msgid "When enabled, extra frontend CSS is generated to support Stackable global colors used in native blocks. If you don't use Stackable global colors in native blocks, simply toggle this OFF. Please note that Stackable global colors are no longer available for native blocks. To ensure your styles always look perfect, our auto-detect feature will activate this option whenever needed."
msgstr ""

#: dist/editor_blocks.js:2
msgid "Start adding Heading blocks to create a table of contents. Supported heading blocks will be linked here."
msgstr ""

#: dist/editor_blocks.js:2
msgid "You have one or more headings without an anchor id. Anchor ids are required for the Table of Contents block to work."
msgstr ""

#: dist/editor_blocks.js:2
msgid "To apply this typography style, just add `%s` in your block's Additional CSS classes. Also make sure that `%s` tag is set to avoid conflict with other typography styles"
msgstr ""

#: dist/editor_blocks.js:2
msgid "Preset Font Pairs"
msgstr ""

#: dist/editor_blocks.js:2
msgid "Typography Settings"
msgstr ""

#: dist/translation-strings.js:470
msgid "New Block"
msgstr ""

#: dist/translation-strings.js:471
msgid "A new block."
msgstr ""

#: dist/translation-strings.js:472
msgid "Keywords that are not in the title"
msgstr ""

#: pro__premium_only/src/block/load-more/block.json
msgctxt "block title"
msgid "Load More Button"
msgstr ""

#: pro__premium_only/src/block/load-more/block.json
msgctxt "block description"
msgid "Load more button for your Stackable Posts block"
msgstr ""

#: pro__premium_only/src/block/pagination/block.json
msgctxt "block title"
msgid "Pagination"
msgstr ""

#: pro__premium_only/src/block/pagination/block.json
msgctxt "block description"
msgid "Pagination for your Stackable Posts block"
msgstr ""

#: src/block/accordion/block.json
#: src/deprecated/v2/block/accordion/block.json
msgctxt "block description"
msgid "A title that your visitors can toggle to view more text. Use as FAQs or multiple ones for an Accordion."
msgstr ""

#: src/block/accordion/block.json
#: src/deprecated/v2/block/accordion/block.json
msgctxt "block keyword"
msgid "Toggle"
msgstr ""

#: src/block/accordion/block.json
#: src/deprecated/v2/block/accordion/block.json
msgctxt "block keyword"
msgid "Faq"
msgstr ""

#: src/block/blockquote/block.json
#: src/deprecated/v2/block/blockquote/block.json
msgctxt "block description"
msgid "Display a quote in style"
msgstr ""

#: src/block/button-group/block.json
msgctxt "block title"
msgid "Button Group"
msgstr ""

#: src/block/button-group/block.json
#: src/block/button/block.json
#: src/block/icon-button/block.json
#: src/deprecated/v2/block/button/block.json
msgctxt "block description"
msgid "Add a customizable button."
msgstr ""

#: src/block/button-group/block.json
#: src/block/button/block.json
#: src/block/icon-button/block.json
#: src/deprecated/v2/block/button/block.json
msgctxt "block keyword"
msgid "Link"
msgstr ""

#: src/block/call-to-action/block.json
#: src/deprecated/v2/block/call-to-action/block.json
msgctxt "block description"
msgid "A small section you can use to call the attention of your visitors. Great for calling attention to your products or deals."
msgstr ""

#: src/block/call-to-action/block.json
msgctxt "block keyword"
msgid "CTA"
msgstr ""

#: src/block/card/block.json
#: src/deprecated/v2/block/card/block.json
msgctxt "block description"
msgid "Describe a single subject in a small card. You can use this to describe your product, service or a person."
msgstr ""

#: src/block/carousel/block.json
msgctxt "block title"
msgid "Carousel"
msgstr ""

#: src/block/carousel/block.json
msgctxt "block description"
msgid "A carousel slider."
msgstr ""

#: src/block/carousel/block.json
#: src/block/horizontal-scroller/block.json
msgctxt "block keyword"
msgid "Slider"
msgstr ""

#: src/block/column/block.json
msgctxt "block title"
msgid "Inner Column"
msgstr ""

#: src/block/column/block.json
msgctxt "block description"
msgid "A single column with advanced layout options."
msgstr ""

#: src/block/column/block.json
#: src/block/columns/block.json
#: src/deprecated/v2/block/column/block.json
#: src/deprecated/v2/block/columns/block.json
msgctxt "block keyword"
msgid "Section rows"
msgstr ""

#: src/block/columns/block.json
msgctxt "block description"
msgid "Multiple columns with advanced layout options."
msgstr ""

#: src/block/columns/block.json
msgctxt "block keyword"
msgid "Container"
msgstr ""

#: src/block/count-up/block.json
#: src/deprecated/v2/block/count-up/block.json
msgctxt "block description"
msgid "Showcase your stats. Display how many customers you have or the number of downloads of your app."
msgstr ""

#: src/block/count-up/block.json
#: src/block/price/block.json
msgctxt "block keyword"
msgid "Number"
msgstr ""

#: src/block/countdown/block.json
msgctxt "block title"
msgid "Countdown"
msgstr ""

#: src/block/countdown/block.json
msgctxt "block description"
msgid "Display a countdown timer on your website."
msgstr ""

#: src/block/countdown/block.json
msgctxt "block keyword"
msgid "Timer"
msgstr ""

#: src/block/design-library/block.json
msgctxt "block description"
msgid "Choose a layout or block from the Stackable Design Library."
msgstr ""

#: src/block/design-library/block.json
msgctxt "block keyword"
msgid "Template"
msgstr ""

#: src/block/divider/block.json
#: src/deprecated/v2/block/divider/block.json
msgctxt "block description"
msgid "Add a pause between your content."
msgstr ""

#: src/block/divider/block.json
msgctxt "block keyword"
msgid "Horizontal Rule"
msgstr ""

#: src/block/divider/block.json
msgctxt "block keyword"
msgid "HR"
msgstr ""

#: src/block/expand/block.json
#: src/deprecated/v2/block/expand/block.json
msgctxt "block description"
msgid "Display a small snippet of text. Your readers can toggle it to show more information."
msgstr ""

#: src/block/expand/block.json
#: src/deprecated/v2/block/expand/block.json
msgctxt "block keyword"
msgid "Hide"
msgstr ""

#: src/block/expand/block.json
#: src/deprecated/v2/block/expand/block.json
msgctxt "block keyword"
msgid "Less"
msgstr ""

#: src/block/feature-grid/block.json
#: src/deprecated/v2/block/feature-grid/block.json
msgctxt "block description"
msgid "Display multiple product features or services. You can use Feature Grids one after another."
msgstr ""

#: src/block/feature/block.json
#: src/deprecated/v2/block/feature/block.json
msgctxt "block description"
msgid "Display a product feature or a service in a large area."
msgstr ""

#: src/block/heading/block.json
msgctxt "block title"
msgid "Heading"
msgstr ""

#: src/block/heading/block.json
#: src/deprecated/v2/block/heading/block.json
msgctxt "block description"
msgid "Introduce new sections of your content in style."
msgstr ""

#: src/block/heading/block.json
#: src/deprecated/v2/block/heading/block.json
msgctxt "block keyword"
msgid "Title"
msgstr ""

#: src/block/hero/block.json
msgctxt "block title"
msgid "Hero"
msgstr ""

#: src/block/hero/block.json
msgctxt "block description"
msgid "A large hero area. Typically used at the very top of a page."
msgstr ""

#: src/block/hero/block.json
msgctxt "block keyword"
msgid "Header"
msgstr ""

#: src/block/horizontal-scroller/block.json
msgctxt "block title"
msgid "Horizontal Scroller"
msgstr ""

#: src/block/horizontal-scroller/block.json
msgctxt "block description"
msgid "A slider that scrolls horizontally."
msgstr ""

#: src/block/horizontal-scroller/block.json
msgctxt "block keyword"
msgid "Carousel"
msgstr ""

#: src/block/icon-box/block.json
msgctxt "block title"
msgid "Icon Box"
msgstr ""

#: src/block/icon-box/block.json
msgctxt "block description"
msgid "A small text area with an icon that can be used to summarize features or services"
msgstr ""

#: src/block/icon-button/block.json
msgctxt "block title"
msgid "Icon Button"
msgstr ""

#: src/block/icon-label/block.json
msgctxt "block title"
msgid "Icon Label"
msgstr ""

#: src/block/icon-label/block.json
msgctxt "block description"
msgid "An Icon and Heading paired together."
msgstr ""

#: src/block/icon-label/block.json
#: src/block/icon/block.json
#: src/deprecated/v2/block/icon/block.json
msgctxt "block keyword"
msgid "SVG"
msgstr ""

#: src/block/icon-list-item/block.json
msgctxt "block title"
msgid "Icon List Item"
msgstr ""

#: src/block/icon-list-item/block.json
msgctxt "block description"
msgid "A single list entry in the Icon List block"
msgstr ""

#: src/block/icon-list/block.json
#: src/deprecated/v2/block/icon-list/block.json
msgctxt "block description"
msgid "An unordered list with icons. You can use this as a list of features or benefits."
msgstr ""

#: src/block/icon-list/block.json
#: src/deprecated/v2/block/icon-list/block.json
msgctxt "block keyword"
msgid "Checklist"
msgstr ""

#: src/block/icon-list/block.json
#: src/deprecated/v2/block/icon-list/block.json
msgctxt "block keyword"
msgid "Bullets"
msgstr ""

#: src/block/icon-list/block.json
msgctxt "block keyword"
msgid "Number list"
msgstr ""

#: src/block/icon/block.json
#: src/deprecated/v2/block/icon/block.json
msgctxt "block description"
msgid "Pick an icon or upload your own SVG icon to decorate your content."
msgstr ""

#: src/block/image-box/block.json
#: src/deprecated/v2/block/image-box/block.json
msgctxt "block description"
msgid "Display an image that shows more information when hovered on. Can be used as a fancy link to other pages."
msgstr ""

#: src/block/image/block.json
msgctxt "block title"
msgid "Image"
msgstr ""

#: src/block/image/block.json
msgctxt "block description"
msgid "An image with advanced controls to make a visual statement."
msgstr ""

#: src/block/map/block.json
msgctxt "block title"
msgid "Map"
msgstr ""

#: src/block/map/block.json
msgctxt "block description"
msgid "Embedded Google Map with advanced controls."
msgstr ""

#: src/block/map/block.json
msgctxt "block keyword"
msgid "location"
msgstr ""

#: src/block/map/block.json
msgctxt "block keyword"
msgid "address"
msgstr ""

#: src/block/notification/block.json
#: src/deprecated/v2/block/notification/block.json
msgctxt "block description"
msgid "Show a notice to your readers. People can dismiss the notice to permanently hide it."
msgstr ""

#: src/block/notification/block.json
#: src/deprecated/v2/block/notification/block.json
msgctxt "block keyword"
msgid "Notice"
msgstr ""

#: src/block/notification/block.json
#: src/deprecated/v2/block/notification/block.json
msgctxt "block keyword"
msgid "Alert"
msgstr ""

#: src/block/number-box/block.json
#: src/deprecated/v2/block/number-box/block.json
msgctxt "block description"
msgid "Display steps or methods that your users will do in your service."
msgstr ""

#: src/block/number-box/block.json
#: src/deprecated/v2/block/number-box/block.json
msgctxt "block keyword"
msgid "Steps"
msgstr ""

#: src/block/posts/block.json
#: src/deprecated/v2/block/blog-posts/block.json
msgctxt "block title"
msgid "Posts"
msgstr ""

#: src/block/posts/block.json
#: src/deprecated/v2/block/blog-posts/block.json
msgctxt "block description"
msgid "Your latest blog posts. Use this to showcase a few of your posts in your landing pages."
msgstr ""

#: src/block/posts/block.json
#: src/deprecated/v2/block/blog-posts/block.json
msgctxt "block keyword"
msgid "Blog Posts"
msgstr ""

#: src/block/posts/block.json
#: src/deprecated/v2/block/blog-posts/block.json
msgctxt "block keyword"
msgid "Lastest Posts"
msgstr ""

#: src/block/posts/block.json
#: src/deprecated/v2/block/blog-posts/block.json
msgctxt "block keyword"
msgid "Query Loop"
msgstr ""

#: src/block/price/block.json
msgctxt "block title"
msgid "Price"
msgstr ""

#: src/block/price/block.json
msgctxt "block description"
msgid "Show a price of a product or service with currency and a suffix styled with different weights"
msgstr ""

#: src/block/price/block.json
#: src/block/pricing-box/block.json
msgctxt "block keyword"
msgid "Currency"
msgstr ""

#: src/block/price/block.json
msgctxt "block keyword"
msgid "Pricing"
msgstr ""

#: src/block/pricing-box/block.json
#: src/deprecated/v2/block/pricing-box/block.json
msgctxt "block description"
msgid "Display the different pricing tiers of your business."
msgstr ""

#: src/block/pricing-box/block.json
msgctxt "block keyword"
msgid "Price"
msgstr ""

#: src/block/pricing-box/block.json
msgctxt "block keyword"
msgid "Pricing Table"
msgstr ""

#: src/block/progress-bar/block.json
msgctxt "block title"
msgid "Progress Bar"
msgstr ""

#: src/block/progress-bar/block.json
msgctxt "block description"
msgid "Visualize a progress value or percentage in a bar."
msgstr ""

#: src/block/progress-bar/block.json
#: src/block/progress-circle/block.json
msgctxt "block keyword"
msgid "percentage status"
msgstr ""

#: src/block/progress-circle/block.json
msgctxt "block title"
msgid "Progress Circle"
msgstr ""

#: src/block/progress-circle/block.json
msgctxt "block description"
msgid "Visualize a progress value or percentage in a circle."
msgstr ""

#: src/block/separator/block.json
#: src/deprecated/v2/block/separator/block.json
msgctxt "block description"
msgid "A fancy separator to be placed between content."
msgstr ""

#: src/block/separator/block.json
#: src/deprecated/v2/block/separator/block.json
msgctxt "block keyword"
msgid "Svg Divider"
msgstr ""

#: src/block/spacer/block.json
#: src/deprecated/v2/block/spacer/block.json
msgctxt "block description"
msgid "Sometimes you just need some space."
msgstr ""

#: src/block/subtitle/block.json
msgctxt "block title"
msgid "Subtitle"
msgstr ""

#: src/block/subtitle/block.json
msgctxt "block description"
msgid "Subtitle text that you can add custom styling to from the global settings."
msgstr ""

#: src/block/tab-content/block.json
msgctxt "block title"
msgid "Tab Content"
msgstr ""

#: src/block/tab-content/block.json
msgctxt "block description"
msgid "A wrapper for tab panels."
msgstr ""

#: src/block/tab-labels/block.json
msgctxt "block title"
msgid "Tab Labels"
msgstr ""

#: src/block/tab-labels/block.json
msgctxt "block description"
msgid "Create interactive navigation within tabs."
msgstr ""

#: src/block/table-of-contents/block.json
msgctxt "block title"
msgid "Table of Contents"
msgstr ""

#: src/block/table-of-contents/block.json
msgctxt "block description"
msgid "Automatically generated table of contents based on Heading blocks."
msgstr ""

#: src/block/table-of-contents/block.json
msgctxt "block keyword"
msgid "ToC"
msgstr ""

#: src/block/table-of-contents/block.json
msgctxt "block keyword"
msgid "Index"
msgstr ""

#: src/block/table-of-contents/block.json
msgctxt "block keyword"
msgid "Outline"
msgstr ""

#: src/block/tabs/block.json
msgctxt "block title"
msgid "Tabs"
msgstr ""

#: src/block/tabs/block.json
msgctxt "block description"
msgid "Organize and display content in multiple tabs."
msgstr ""

#: src/block/tabs/block.json
msgctxt "block keyword"
msgid "toggle"
msgstr ""

#: src/block/team-member/block.json
#: src/deprecated/v2/block/team-member/block.json
msgctxt "block description"
msgid "Display members of your team or your office. Use multiple Team Member blocks if you have a large team."
msgstr ""

#: src/block/testimonial/block.json
#: src/deprecated/v2/block/testimonial/block.json
msgctxt "block description"
msgid "Showcase what your users say about your product or service."
msgstr ""

#: src/block/text/block.json
msgctxt "block title"
msgid "Text"
msgstr ""

#: src/block/text/block.json
#: src/deprecated/v2/block/text/block.json
msgctxt "block description"
msgid "Start with the building block of all page layouts."
msgstr ""

#: src/block/text/block.json
#: src/deprecated/v2/block/text/block.json
msgctxt "block keyword"
msgid "Paragraph"
msgstr ""

#: src/block/timeline/block.json
msgctxt "block title"
msgid "Timeline"
msgstr ""

#: src/block/timeline/block.json
msgctxt "block description"
msgid "Show events in chronological order"
msgstr ""

#: src/block/timeline/block.json
msgctxt "block keyword"
msgid "history"
msgstr ""

#: src/block/timeline/block.json
msgctxt "block keyword"
msgid "milestone"
msgstr ""

#: src/block/video-popup/block.json
#: src/deprecated/v2/block/video-popup/block.json
msgctxt "block description"
msgid "Display a large thumbnail that your users can click to play a video full-screen. Great for introductory or tutorial videos."
msgstr ""

#: src/block/video-popup/block.json
#: src/deprecated/v2/block/video-popup/block.json
msgctxt "block keyword"
msgid "YouTube"
msgstr ""

#: src/block/video-popup/block.json
#: src/deprecated/v2/block/video-popup/block.json
msgctxt "block keyword"
msgid "Vimeo"
msgstr ""

#: src/block/video-popup/block.json
#: src/deprecated/v2/block/video-popup/block.json
msgctxt "block keyword"
msgid "Embed Mp4"
msgstr ""

#: src/deprecated/v2/block/call-to-action/block.json
msgctxt "block keyword"
msgid "Call to Action"
msgstr ""

#: src/deprecated/v2/block/card/block.json
msgctxt "block keyword"
msgid "Card"
msgstr ""

#: src/deprecated/v2/block/column/block.json
msgctxt "block title"
msgid "Advanced Column"
msgstr ""

#: src/deprecated/v2/block/column/block.json
msgctxt "block description"
msgid "A single column within an advanced columns block. Get advanced options on how you want your column to look."
msgstr ""

#: src/deprecated/v2/block/columns/block.json
msgctxt "block title"
msgid "Advanced Columns & Grid"
msgstr ""

#: src/deprecated/v2/block/columns/block.json
msgctxt "block description"
msgid "Add a block that displays content in multiple columns. Get advanced options on how you want your columns to look."
msgstr ""

#: src/deprecated/v2/block/container/block.json
msgctxt "block description"
msgid "A styled container that you can add other blocks inside. Use this to create unique layouts."
msgstr ""

#: src/deprecated/v2/block/container/block.json
msgctxt "block keyword"
msgid "Row layout"
msgstr ""

#: src/deprecated/v2/block/count-up/block.json
msgctxt "block keyword"
msgid "Statistics"
msgstr ""

#: src/deprecated/v2/block/header/block.json
msgctxt "block description"
msgid "A large header title area. Typically used at the very top of a page."
msgstr ""

#: src/deprecated/v2/block/header/block.json
msgctxt "block keyword"
msgid "Hero"
msgstr ""
