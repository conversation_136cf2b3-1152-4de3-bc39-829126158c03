<?php
/**
 * SCD Configuration Class
 * 
 * Centralizes configuration data and constants used across the application.
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

class SCD_Config {
    
    /**
     * The 7 specific odbor (department) category slugs
     * 
     * @var array
     */
    const ODBOR_TERMS = array(
        'animacie-a-video',
        'digital',
        'dizajn-s-pridanou-hodnotou',
        'dizajn-v-pohybe',
        'domov-verejny-priestor',
        'experimentalny-komunikacny-dizajn',
        'experimentalny-produktovy-dizajn'
    );
    
    /**
     * Get odbor terms
     * 
     * @return array The odbor terms
     */
    public static function get_odbor_terms() {
        return self::ODBOR_TERMS;
    }
    
    /**
     * Check if a term slug is an odbor term
     * 
     * @param string $slug The term slug to check
     * @return bool True if the slug is an odbor term
     */
    public static function is_odbor_term( $slug ) {
        return in_array( $slug, self::ODBOR_TERMS );
    }
    
    /**
     * Filter terms to get only odbor terms
     * 
     * @param array $terms Array of term objects
     * @return array Filtered array containing only odbor terms
     */
    public static function filter_odbor_terms( $terms ) {
        return array_filter( $terms, function( $term ) {
            return self::is_odbor_term( $term->slug );
        });
    }
    
    /**
     * Filter terms to exclude odbor terms
     * 
     * @param array $terms Array of term objects
     * @return array Filtered array excluding odbor terms
     */
    public static function filter_non_odbor_terms( $terms ) {
        return array_filter( $terms, function( $term ) {
            return ! self::is_odbor_term( $term->slug );
        });
    }
}
