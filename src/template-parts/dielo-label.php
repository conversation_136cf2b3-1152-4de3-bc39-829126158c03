<?php
/**
 * Template part for displaying dielo labels based on "nove_hodnotenie" field
 * This template handles different variations of labels
 */

$nove_hodnotenie = get_field('nove_hodnotenie');

// Only render if nove_hodnotenie field exists and has a value
if ($nove_hodnotenie && !empty($nove_hodnotenie['value'])) {
    
    // Check if it's nominacia - render the current label
    if ($nove_hodnotenie['value'] === 'nominacia') {
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-[#EFEFEF] !text-[#3F3F3F] flex justify-between items-center p-3 z-10">
            <strong><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
            <strong><?php echo get_field('hodnotenie_rok'); ?></strong>
        </div>
        <?php
    }
    
    // Check if it's hlavna-cena - render label with trophy icon and category
    elseif ($nove_hodnotenie['value'] === 'hlavna-cena') {
        $kategoria = get_field('nove_hodnotenie_kategoria');
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-black text-white p-3 z-10">
            <!-- First line: icon, label name, and year -->
            <div class="flex justify-between items-center mb-2">
                <div class="flex">
                    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" 
                         class="mr-2 mt-px">
                        <path d="M15.2 1.3H12.8V0.5H3.2V1.3H0.8C0.32 1.3 0 1.62 0 2.1V4.02C0 5.86 1.36 7.38 3.2 7.62V7.7C3.2 10.02 4.8 11.94 6.96 12.42L6.4 14.1H4.56C4.24 14.1 3.92 14.34 3.84 14.66L3.2 16.5H12.8L12.16 14.66C12.08 14.34 11.76 14.1 11.44 14.1H9.6L9.04 12.42C11.2 11.94 12.8 10.02 12.8 7.7V7.62C14.64 7.38 16 5.86 16 4.02V2.1C16 1.62 15.68 1.3 15.2 1.3ZM3.2 6.02C2.32 5.78 1.6 4.98 1.6 4.02V2.9H3.2V6.02ZM9.6 8.5L8 7.62L6.4 8.5L6.8 6.9L5.6 5.3H7.28L8 3.7L8.72 5.3H10.4L9.2 6.9L9.6 8.5ZM14.4 4.02C14.4 4.98 13.68 5.86 12.8 6.02V2.9H14.4V4.02Z" fill="white"/>
                    </svg>
                    <strong><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
                </div>
                <strong><?php echo get_field('hodnotenie_rok'); ?></strong>
            </div>
            <!-- Second line: category -->
            <?php if ($kategoria): ?>
            <ul>
                <li class="tag-list__item mb-0">
                    <div class="tag-list__link !text-white !border-white/40"><?php
                      echo esc_html($kategoria->name); ?></div>
                </li>
            </ul>
            <?php endif; ?>
        </div>
        <?php
    }
    
    // Check if it's specialna-cena - render label with star icon, white background, and black border
    elseif ($nove_hodnotenie['value'] === 'specialna-cena') {
        $kategoria = get_field('nove_hodnotenie_kategoria');
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-white border border-black p-3 z-10">
            <!-- First line: icon, label name, and year -->
            <div class="flex justify-between items-center mb-2">
                <div class="flex">
                    <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-2">
                        <path d="M7.09375 9.63753L6 9.0625L4.90625 9.63753V12.89H7.09375V9.63753Z" fill="black"/>
                        <path d="M10.7568 3.95637L7.46959 3.47872L5.9995 0.5L4.52941 3.47872L1.24219 3.95637L3.62084 6.27503L3.05931 9.54897L5.9995 8.00322L8.93969 9.54897L8.37816 6.27503L10.7568 3.95637Z" fill="black"/>
                        <path d="M9.29759 13.8271H2.70241L2.00294 15.5623H0.53125V16.4998H11.4688V15.5623H9.99706L9.29759 13.8271Z" fill="black"/>
                    </svg>
                    <strong class="!text-[#3F3F3F]"><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
                </div>
                <strong class="!text-[#3F3F3F]"><?php echo get_field('hodnotenie_rok'); ?></strong>
            </div>
            <!-- Second line: category -->
            <?php if ($kategoria): ?>
            <ul>
                <li class="tag-list__item mb-0">
                    <div class="tag-list__link !text-black !border-black/40 hover:!text-white"><?php
                      echo esc_html($kategoria->name); ?></div>
                </li>
            </ul>
            <?php endif; ?>
        </div>
        <?php
    }
    
    // Check if it's cena-verejnosti - render label with people icon and vote count
    elseif ($nove_hodnotenie['value'] === 'cena-verejnosti') {
        $pocet_hlasov = get_field('pocet_hlasov');
        
        // Slovak pluralization for votes
        $hlasov_text = '';
        if ($pocet_hlasov == 1) {
            $hlasov_text = 'Hlas';
        } elseif ($pocet_hlasov >= 2 && $pocet_hlasov <= 4) {
            $hlasov_text = 'Hlasy';
        } else {
            $hlasov_text = 'Hlasov';
        }
        ?>
        <div class="absolute bottom-0 left-0 w-full bg-[#EFEFEF] border border-black p-3 z-10">
            <!-- First line: icon, label name, and year -->
            <div class="flex justify-between items-center mb-2">
                <div class="flex items-center">
                    <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg" 
                         class="mr-2 -mt-1">
                        <path d="M7.5 3C7.5 1.6195 8.6195 0.5 10 0.5C11.3805 0.5 12.5 1.6195 12.5 3C12.5 4.3805 11.3805 5.5 10 5.5C8.6195 5.5 7.5 4.3805 7.5 3Z" fill="#3F3F3F"/>
                        <path d="M4 5.5C2.6195 5.5 1.5 4.3805 1.5 3C1.5 1.6195 2.6195 0.5 4 0.5C5.3805 0.5 6.5 1.6195 6.5 3C6.5 4.3805 5.3805 5.5 4 5.5Z" fill="#3F3F3F"/>
                        <path d="M14 10.149V11.75C14 11.888 13.888 12 13.75 12H9C8.862 12 8.75 11.888 8.75 11.75V10C8.75 8.79 8.2915 7.6875 7.5435 6.8475C8.2675 6.2825 9.188 5.958 10.1875 6.004C12.3475 6.104 14 7.9865 14 10.149Z" fill="#3F3F3F"/>
                        <path d="M8 10V11.75C8 11.888 7.888 12 7.75 12H0.25C0.112 12 0 11.888 0 11.75V10C0 7.791 1.791 6 4 6C6.209 6 8 7.791 8 10Z" fill="#3F3F3F"/>
                    </svg>
                    <strong class="!text-[#3F3F3F]"><?php echo esc_html($nove_hodnotenie['label']); ?></strong>
                </div>
                <strong class="!text-[#3F3F3F]"><?php echo get_field('hodnotenie_rok'); ?></strong>
            </div>
            <!-- Second line: vote count -->
            <?php if ($pocet_hlasov): ?>
            <div class="bg-white border border-black/15 px-2 py-1 inline-block rounded">
                <?php echo esc_html($pocet_hlasov . ' ' . $hlasov_text); ?>
            </div>
            <?php endif; ?>
        </div>
        <?php
    }
    
    // Here will be added other variations in the future
}
?>
