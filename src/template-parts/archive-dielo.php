<div class="card__item card__item--big card__item--mobile-row col-12 col-sm-6 col-md-4">
	<div class="card__content relative">
		<?php 
		// Získanie termínov taxonomie scd_ncd_rocnik pre aktuálny post
		$ncd_rocnik_terms = get_the_terms(get_the_ID(), 'scd_ncd_rocnik');
		$show_voting = false;
        $voting_link = '';

		if ($ncd_rocnik_terms && !is_wp_error($ncd_rocnik_terms)) {
			foreach ($ncd_rocnik_terms as $term) {
				if (get_field('hlasovanie_zapnute', 'scd_ncd_rocnik_' . $term->term_id)) {
					$show_voting = true;
                    $voting_link = get_field('link_na_hlasovania', 'scd_ncd_rocnik_' . $term->term_id);
					break;
				}
			}
		}

		if ($show_voting): ?>
		<!-- Voting button -->
		<a href="<?php echo $voting_link; ?>" class="absolute top-2.5 right-2.5 
        bg-black !text-white
        text-xs px-2 py-[5px] 
		font-medium uppercase tracking-wide z-10 transition-colors duration-200">
			<?php _e('HLASOVAŤ', 'scd'); ?>
		</a>
		<?php endif; ?>
		<a class="card__image-block aspect-ratio-xs-1-1 relative" href="<?php the_permalink(); ?>">
			<?php
			if (is_array(get_field( 'obrazky'))) {
				echo wp_get_attachment_image( current( get_field( 'obrazky') ), 'medium_large', false, array( 
					'class' => 'img--responsive img--full img--cover', 
					'alt' => the_title_attribute( array( 'echo' => false ) ) 
				) ); 
				
			}
			?>
            <!-- Label from "nove_hodnotenie" custom field -->
            <?php get_template_part('template-parts/dielo-label'); ?>
		</a>
		<div class="card__text-block card__text-block--top">
			<div class="card__top-block">
				<h2 class="beta card__title"><a 
					href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
			<?php if ( ! empty( $dizajner = get_field( 'dizajner_rola' ) ) ): ?>
				<div class="card-info">
					<div class="card-info__item"><?php
						echo scd_get_linked_title( $dizajner[0]['dizajner'], 'card-info__link' ); ?></div>
				</div>
			<?php endif; ?>
			</div>
			<?php get_template_part( 'template-parts/tags', null, array( 'class' => 'tag-list--no-margin' ) ); ?>
		</div>
	</div>
</div>
