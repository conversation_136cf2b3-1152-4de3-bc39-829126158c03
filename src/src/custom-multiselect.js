document.addEventListener("DOMContentLoaded", function () {
  initializeCustomMultiselect();
});

// Make function globally available for mobile modal
window.initializeCustomMultiselect = function(container) {
  const targetContainer = container ? container[0] || container : document;
  const multiselect = targetContainer.querySelector ?
    targetContainer.querySelector("#odbor-multiselect") :
    document.getElementById("odbor-multiselect");
  const display = multiselect.querySelector(".custom-multiselect__display");
  const dropdown = multiselect.querySelector(".custom-multiselect__dropdown");
  const placeholder = multiselect.querySelector(
    ".custom-multiselect__placeholder",
  );
  const selectedItemsContainer = multiselect.querySelector(
    ".custom-multiselect__selected-items",
  );
  if (!multiselect) return; // Exit if multiselect not found

  const options = multiselect.querySelectorAll(".custom-multiselect__option");
  const hiddenCheckboxes = targetContainer.querySelectorAll ?
    targetContainer.querySelectorAll('input[name="odbor[]"]') :
    document.querySelectorAll('input[name="odbor[]"]');

  // Toggle dropdown
  display.addEventListener("click", function () {
    multiselect.classList.toggle("open");
    if (multiselect.classList.contains("open")) {
      dropdown.style.display = "block";
    } else {
      dropdown.style.display = "none";
    }
  });

  // Close dropdown when clicking outside
  document.addEventListener("click", function (e) {
    if (!multiselect.contains(e.target)) {
      multiselect.classList.remove("open");
      dropdown.style.display = "none";
    }
  });

  // Handle option selection
  options.forEach(function (option) {
    option.addEventListener("click", function (e) {
      e.stopPropagation();
      const value = this.dataset.value;
      const checkbox = document.getElementById("odbor-" + value);

      if (this.classList.contains("selected")) {
        // Deselect
        this.classList.remove("selected");
        checkbox.checked = false;
      } else {
        // Select
        this.classList.add("selected");
        checkbox.checked = true;
      }

      updateSelectedItems();
      triggerFilterChange();
    });
  });

  // Update selected items display
  function updateSelectedItems() {
    const selectedOptions = multiselect.querySelectorAll(
      ".custom-multiselect__option.selected",
    );
    const counter = multiselect.querySelector(".custom-multiselect__counter");

    if (selectedOptions.length === 0) {
      placeholder.style.display = "block";
      selectedItemsContainer.textContent = "";
      counter.style.display = "none";
    } else {
      placeholder.style.display = "none";

      // Create comma-separated text
      const selectedTexts = Array.from(selectedOptions).map(function (option) {
        return option.querySelector(".custom-multiselect__text").textContent;
      });
      selectedItemsContainer.textContent = selectedTexts.join(", ");

      // Show and update counter
      counter.textContent = "(" + selectedOptions.length + ")";
      counter.style.display = "inline";
    }
  }

  // Trigger filter change event (for existing filter system integration)
  function triggerFilterChange() {
    // Trigger change event on hidden checkboxes for existing JS to pick up
    hiddenCheckboxes.forEach(function (checkbox) {
      if (checkbox.checked) {
        checkbox.dispatchEvent(new Event("change", { bubbles: true }));
      }
    });
  }

  // Initialize selected state based on pre-selected checkboxes
  function initializeSelectedState() {
    hiddenCheckboxes.forEach(function (checkbox) {
      if (checkbox.checked) {
        const value = checkbox.value;
        const option = multiselect.querySelector(
          '[data-value="' + value + '"]',
        );
        if (option) {
          option.classList.add("selected");
        }
      }
    });
    updateSelectedItems();
  }

  // Initialize on page load
  initializeSelectedState();
};
