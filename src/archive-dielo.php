<?php
/**
 * The template for displaying work archive
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package scd
 */

$tags = scd_get_terms( array( 
	'taxonomy' => 'post_tag', 
	'post_types' => array( 'scd_dielo_smd', 'scd_dielo_ncd' ),
	'hide_empty' => true, 
	'orderby' => 'name', 
	'order' => 'ASC', 
) );
$the_tag = get_query_var( 'tag' );

$the_typ = get_query_var( 'post_type' );

$dizajneri = get_posts( array(
	'numberposts' => -1,
	'post_status' => 'publish',
	'post_type' => 'scd_osobnost_dizajnu',
	'tax_query' => array( array(
		'taxonomy' => 'scd_osobnost_typ',
		'field' => 'slug',
		'terms' => array( 'osobnosti-dizajnu', 'design-personalities' ), // TODO translate term with pll_get_term()
	) ),
	'orderby' => 'name',
	'order' => 'ASC',
) );
$the_dizajner = get_query_var( 'dizajner' );


$vyrobcovia = get_posts( array(
	'numberposts' => -1,
	'post_status' => 'publish',
	'post_type' => 'scd_osobnost_dizajnu',
	'tax_query' => array( array(
		'taxonomy' => 'scd_osobnost_typ',
		'field' => 'slug',
		'terms' => array( 'vyrobcovia', 'producers' ), // TODO translate term with pll_get_term()
	) ),
	'orderby' => 'name',
	'order' => 'ASC',
) );
$the_vyrobca = get_query_var( 'vyrobca' );


$rocniky = get_terms( array( 
	'taxonomy' => 'scd_ncd_rocnik', 
	'hide_empty' => true, 
	'orderby' => 'name', 
	'order' => 'DESC', 
) );
$the_rocnik = get_query_var( 'rocnik' );

$kolekcie = get_terms( array(
	'taxonomy' => 'scd_kolekcia',
	'hide_empty' => true,
	'orderby' => 'term_id',
	'order' => 'DESC',
) );
$the_kolekcia = get_query_var( 'kolekcia' );

// Get all taxonomy terms for splitting between "Odbor" and "Kategórie"
$all_kategorie = get_terms( array(
	'taxonomy' => 'scd_ncd_kategoria',
	'hide_empty' => true,
	'orderby' => 'name',
	'order' => 'ASC',
) );

// Filter terms for "Odbor" (only the 7 specified terms)
$odbor_kategorie = SCD_Config::filter_odbor_terms( $all_kategorie );

// Filter terms for "Kategórie" (all remaining terms excluding the 7 in "Odbor")
$kategorie = SCD_Config::filter_non_odbor_terms( $all_kategorie );

$the_kategoria = get_query_var( 'kategoria' );
$the_odbor = get_query_var( 'odbor' );
if (!$the_odbor) {
	$the_odbor = [];
} else {
	// Ensure $the_odbor is always an array (handle string values from URL parameters)
	$the_odbor = is_array( $the_odbor ) ? $the_odbor : array( $the_odbor );
}

$the_hodnotenie = get_query_var( 'hodnotenie' );
if (!$the_hodnotenie)
	$the_hodnotenie = [];

$the_sorting = get_query_var( 'sorting' );


// Change work archive page title if displaying both CPTs, NCD and SMD works, at once
add_filter( 'post_type_archive_title', 'scd_work_archive_page_title' );
function scd_work_archive_page_title( $name ) {
	if ( is_array( get_query_var( 'post_type' ) ) ) {
		$name = 'Diela';
	}
	return $name;
}

get_header();
?>
<!-- Begin Main content-->
<main id="primary">
	<section class="padding-style-6-3 primary">
		<div class="container-large">
			<!-- Mobile Filters Button -->
			<div class="mobile-filters-button d-lg-none margin-bottom-3">
				<button type="button" class="btn btn--black-fill btn--responsive js-mobile-filters-trigger" data-modal="mobile-filters">
					<svg class="icon-svg icon-k margin-right-1">
						<use xlink:href="<?php scd_asset( 'sprites.svg' ); ?>#icon-k"></use>
					</svg>
					<?php esc_html_e( 'Filtre', 'scd' ); ?> <span class="js-filter-count"></span>
				</button>
			</div>
			<div class="row no-gutters">
				<div class="side-panel side-panel--filter col-12 col-lg-4 margin-bottom-4 margin-bottom-xs-6 margin-bottom-lg-0">
					<!-- Begin Section filter-->
					<div class="box box--border box--size-2-5-5 border-0-bottom js-clear-filter-block">
						<div class="letter__item">
									<svg class="icon-svg icon-k">
										<use xlink:href="<?php scd_asset( 'sprites.svg' ); ?>#icon-k"></use>
									</svg>
						</div>
						<div class="margin-bottom-1-5 h--margin-0">
							<h2 class="alfa"><?php esc_html_e( 'Filter', 'scd' ); ?></h2>
						</div>
						<div class="form__field form__field--big">
							<label class="form__label" for="odbor"><?php esc_html_e( 'Odbor', 'scd' ); ?></label>
							
							<!-- Hidden checkboxes for form submission -->
							<div style="display: none;">
								<?php foreach ( $odbor_kategorie as $odbor_kategoria ): ?>
									<input 
										type="checkbox" 
										name="odbor[]" 
										id="odbor-<?php echo esc_attr( $odbor_kategoria->slug ); ?>"
										value="<?php echo esc_attr( $odbor_kategoria->slug ); ?>" 
										data-qvar="odbor"
										<?php if ( in_array( $odbor_kategoria->slug, $the_odbor ) ) echo 'checked'; ?>
									/>
								<?php endforeach; ?>
							</div>
							
							<!-- Custom multi-select visual interface -->
							<div class="custom-multiselect" id="odbor-multiselect" tabindex="0">
								<div class="custom-multiselect__display">
									<span class="custom-multiselect__placeholder"><?php _e('Všetky', 'scd'); ?></span>
									<div class="custom-multiselect__selected-items"></div>
									<span class="custom-multiselect__counter" style="display: none;"></span>
								</div>
								<div class="custom-multiselect__dropdown" style="display: none;">
									<?php foreach ( $odbor_kategorie as $odbor_kategoria ): ?>
										<div class="custom-multiselect__option" data-value="<?php echo esc_attr( $odbor_kategoria->slug ); ?>">
											<span class="custom-multiselect__checkbox"></span>
											<span class="custom-multiselect__text"><?php echo esc_html( $odbor_kategoria->name ); ?></span>
										</div>
									<?php endforeach; ?>
								</div>
							</div>
						</div>
                        <div class="form__field form__field--big">
                            <label class="form__label" for="kategoria"><?php esc_html_e( 'Kategórie', 'scd' ); ?></label>
                            <select class="js-select2 form__select" style="width:100%" id="kategoria" name="kategoria" data-qvar="kategoria">
                                <option value="" <?php if ( empty( $the_kategoria ) ) echo 'selected'; ?>><?php esc_html_e( 'Všetky', 'scd' ); ?></option>
                              <?php foreach ( $kategorie as $kategoria ): ?>
                                  <option
                                          value="<?php echo esc_attr( $kategoria->slug ); ?>"
                                    <?php if ( $the_kategoria === $kategoria->slug ) echo 'selected'; ?>><?php echo esc_html( $kategoria->name ); ?></option>
                              <?php endforeach; ?>
                            </select>
                        </div>
						<div class="form__field form__field--big">
							<label class="form__label" for="vyrobca"><?php esc_html_e( 'Výrobca', 'scd' ); ?></label>
							<select class="js-select2 form__select" style="width:100%" data-placeholder="" id="vyrobca" data-qvar="vyrobca">
								<option value="0" <?php if ( empty( $the_vyrobca ) ) echo 'selected'; ?>><?php esc_html_e( 'Všetci', 'scd' ); ?></option>
							<?php foreach ( $vyrobcovia as $vyrobca ): ?>
								<option 
									value="<?php echo esc_attr( $vyrobca->ID ); ?>" 
									<?php if ( $the_vyrobca == $vyrobca->ID ) echo 'selected'; ?>><?php 
									echo esc_html( get_the_title( $vyrobca ) ); ?></option>
							<?php endforeach; ?>
							</select>
						</div>
						<div class="form__field form__field--big">
							<label class="form__label" for="kolekcia"><?php esc_html_e( 'Výstava / kolekcia', 'scd' ); ?></label>
							<select class="js-select2 form__select" style="width:100%" id="kolekcia" name="kolekcia" data-qvar="kolekcia">
							<?php scd_hierarchical_select( $kolekcie, $the_kolekcia ); ?>
							</select>
						</div>
                        <div class="form__field form__field--big">
                            <label class="form__label" for="rocnik"><?php esc_html_e( 'Rok ( Ročník )', 'scd' ); ?></label>
                            <select class="js-select2 form__select" style="width:100%" id="rocnik" name="rocnik" data-qvar="rocnik">
                                <option value="" <?php if ( empty( $the_rocnik ) ) echo 'selected'; ?>><?php esc_html_e( 'Všetky', 'scd' ); ?></option>
                              <?php foreach ( $rocniky as $rocnik ): ?>
                                  <option
                                          value="<?php echo esc_attr( $rocnik->slug ); ?>"
                                    <?php if ( $the_rocnik === $rocnik->slug ) echo 'selected'; ?>><?php echo esc_html( $rocnik->name ); ?></option>
                              <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form__field form__field--big">
                            <label class="form__label" for="dizajner"><?php esc_html_e( 'Autor', 'scd' ); ?></label>
                            <select class="js-select2 form__select" style="width:100%" data-placeholder="" id="dizajner" data-qvar="dizajner">
                                <option value="0" <?php if ( empty( $the_dizajner ) ) echo 'selected'; ?>><?php esc_html_e( 'Všetci', 'scd' ); ?></option>
                              <?php foreach ( $dizajneri as $dizajner ): ?>
                                  <option
                                          value="<?php echo esc_attr( $dizajner->ID ); ?>"
                                    <?php if ( $the_dizajner == $dizajner->ID ) echo 'selected'; ?>><?php
                                    echo esc_html( get_the_title( $dizajner ) ); ?></option>
                              <?php endforeach; ?>
                            </select>
                        </div>
						<div class="margin-top-2 h--margin-0 h--font-regular">
							<label class="form__label"><?php esc_html_e( 'Typ', 'scd' ); ?></label>
							<div class="checkbox">
								<input class="checkbox__input-hidden js-checkbox js-checkbox-input-hidden" type="checkbox" 
									name="hodnotenie[]" id="hodnotenie-nominovane"
									value="nominovane" data-qvar="hodnotenie" <?php 
									if ( in_array( 'nominovane', $the_hodnotenie ) ) echo 'checked'; ?>>
								<label class="checkbox__label" for="hodnotenie-nominovane"><span 
									class="checkbox__box"></span><span class="checkbox__text"><?php 
									esc_html_e( 'Nominované', 'scd' ); ?></span></label>
							</div>
							<div class="checkbox">
								<input class="checkbox__input-hidden js-checkbox js-checkbox-input-hidden" type="checkbox" 
									name="hodnotenie[]" id="hodnotenie-ocenene"
									value="ocenene" data-qvar="hodnotenie" <?php 
									if ( in_array( 'ocenene', $the_hodnotenie ) ) echo 'checked'; ?>>
								<label class="checkbox__label" for="hodnotenie-ocenene"><span 
									class="checkbox__box"></span><span class="checkbox__text"><?php 
									esc_html_e( 'Ocenené', 'scd' ); ?></span></label>
							</div>
							<div class="margin-top-2">
								<button type="button" class="btn btn--black-fill btn--responsive js-clear-filter-trigger">
									<?php esc_html_e( 'Resetovať filtre', 'scd' ); ?>
								</button>
							</div>
						</div>
					</div>
					<!-- End Section filter-->
					<!-- Begin Section tags-->
					<div class="box box--border box--size-2-5-2">
						<div class="d-none d-lg-block">
							<div class="margin-bottom-1-5 h--margin-0">
								<h2 class="alfa"><?php esc_html_e( 'Tagy', 'scd' ); ?></h2>
							</div>
							<ul class="tag-list tag-list--column js-select-tag-block" data-qvar="tag">
								<li class="tag-list__item"><a 
									class="tag-list__link js-select-tag-trigger <?php if ( empty( $the_tag ) ) echo 'is-selected'; ?>" 
									href="#"><?php esc_html_e( 'Všetky', 'scd' ); ?></a></li>
							<?php foreach ( $tags as $tag ): ?>
								<li class="tag-list__item"><a 
									class="tag-list__link js-select-tag-trigger <?php if ( $the_tag === $tag->slug ) echo 'is-selected'; ?>" 
									href="<?php echo esc_url( get_category_link( $tag ) ); ?>"
									data-id="<?php echo esc_attr( $tag->slug ); ?>"><?php echo esc_html( $tag->name ); ?></a></li>
							<?php endforeach; ?>
							</ul>
						</div>
						<div class="d-lg-none margin-bottom-1">
							<form class="js-form" id="form-tags" role="form">
								<label class="form__label" for="tags_number"><?php esc_html_e( 'Tagy', 'scd' ); ?></label>
								<select class="js-select form__select" style="width:100%" data-placeholder="" id="tags_number" data-qvar="tag">
									<option value="0" <?php if ( empty( $the_tag ) ) echo 'selected'; ?>><?php esc_html_e( 'Všetky', 'scd' ); ?></option>
								<?php foreach ( $tags as $tag ): ?>
									<option 
										value="<?php echo esc_attr( $tag->slug ); ?>" 
										<?php if ( $the_tag === $tag->slug ) echo 'selected'; ?>><?php echo esc_html( $tag->name ); ?></option>
								<?php endforeach; ?>
								</select>
							</form>
						</div>
					</div>
					<!-- End Section tags-->
				</div>
				<div id="main-content" class="main-content main-content-classic col-12 col-lg-5 load-more-block js-load-more-block">
					<!-- Begin Section people-->
					<div class="box box--border box--size-0-5-2">
						<div class="d-lg-none">
							<div class="letter__item">
										<svg class="icon-svg icon-c">
											<use xlink:href="<?php scd_asset( 'sprites.svg' ); ?>#icon-c"></use>
										</svg>
							</div>
						</div>
						<div class="d-none d-lg-flex margin-bottom-08">
							<ul class="tag-list tag-list--offset-top js-select-tag-block" data-qvar="post_type">
								<li class="tag-list__item"><a 
									class="tag-list__link js-select-tag-trigger <?php 
									if ( empty( $the_typ ) || array( 'scd_dielo_smd', 'scd_dielo_ncd' ) == $the_typ ) 
										echo 'is-selected'; ?>" 
									href="<?php scd_work_link(); ?>"
									data-id='["scd_dielo_smd","scd_dielo_ncd"]'><?php esc_html_e( 'Všetky', 'scd' ); ?></a></li>
								<li class="tag-list__item"><a 
									class="tag-list__link js-select-tag-trigger <?php 
									if ( $the_typ === 'scd_dielo_smd' ) echo 'is-selected'; ?>" 
									href="<?php scd_work_link( 'scd_dielo_smd' ); ?>"
									data-id="scd_dielo_smd"><?php esc_html_e( 'Zbierky', 'scd' ); ?></a></li>
								<li class="tag-list__item"><a 
									class="tag-list__link js-select-tag-trigger <?php 
									if ( $the_typ === 'scd_dielo_ncd' ) echo 'is-selected'; ?>" 
									href="<?php scd_work_link( 'scd_dielo_ncd' ); ?>"
									data-id="scd_dielo_ncd"><?php esc_html_e( 'NCD', 'scd' ); ?></a></li>
							</ul>
						</div>

                        <div class="sorting absolute -top-px right-8">
                            <div class="form__field form__field--big 
                            [&_.select2-selection--single]:!h-[30px] 
                            [&_.select2-selection--single_*]:!leading-[30px] 
">
                                <select class="js-select form__select" style="width:100%" data-placeholder="" id="sorting" data-qvar="sorting">
                                    <option value="normal"><?php esc_html_e( 'Zoradiť podľa', 'scd' ); ?></option>
                                    <option value="relevance"><?php esc_html_e( 'Podľa relevancie', 'scd' ); ?></option>
                                    <option value="newest"><?php esc_html_e( 'Najnovšie', 'scd' ); ?></option>
                                    <option value="oldest"><?php esc_html_e( 'Najstaršie', 'scd' ); ?></option>
                                </select>
                            </div>
                        </div>

						<div class="margin-bottom-1-5 h--margin-0">
							<h2 class="alfa"><?php esc_html_e( 'Diela', 'scd' ); ?></h2>
						</div>
						<div class="row gutter-lg-60 p--margin-0 h--margin-05 js-load-more-grid">
						<?php
							// Check if relevance sorting is being used (which implements hierarchical sorting by categories)
							if ( $the_sorting === 'relevance' ) {
								// Use relevance display logic for relevance sorting
								$relevance_sorter = SCD_Relevance_Sorter::get_instance();

								// Build args for relevance query with pagination support
								// Start with basic parameters
								$relevance_args = array(
									'post_type' => array( 'scd_dielo_smd', 'scd_dielo_ncd' ),
									'post_status' => 'publish',
									'sorting' => 'relevance',
									'posts_per_page' => 12, // Use the same limit as standard queries
									'paged' => get_query_var( 'paged' ) ? get_query_var( 'paged' ) : 1
								);

								// Add all available filter parameters to match AJAX behavior
								// Add tag filter
								if ( ! empty( $the_tag ) ) {
									$relevance_args['tag'] = $the_tag;
								}

								// Add post type filter (if specific type is selected)
								if ( ! empty( $the_typ ) && is_string( $the_typ ) ) {
									$relevance_args['post_type'] = $the_typ;
								}

								// Add kolekcia (collection) filter
								if ( ! empty( $the_kolekcia ) ) {
									$relevance_args['kolekcia'] = $the_kolekcia;
								}

								// Add hodnotenie (evaluation) filter
								if ( ! empty( $the_hodnotenie ) ) {
									$relevance_args['hodnotenie'] = $the_hodnotenie;
								}

								// Add taxonomy queries if they exist
								$tax_query = array();
								if ( ! empty( $the_kategoria ) ) {
									$tax_query[] = array(
										'taxonomy' => 'scd_ncd_kategoria',
										'field' => 'slug',
										'terms' => $the_kategoria
									);
								}
								if ( ! empty( $the_odbor ) ) {
									$tax_query[] = array(
										'taxonomy' => 'scd_ncd_kategoria',
										'field' => 'slug',
										'terms' => $the_odbor
									);
								}
								if ( ! empty( $the_rocnik ) ) {
									$tax_query[] = array(
										'taxonomy' => 'scd_ncd_rocnik',
										'field' => 'slug',
										'terms' => $the_rocnik
									);
								}
								if ( ! empty( $the_kolekcia ) ) {
									$tax_query[] = array(
										'taxonomy' => 'scd_kolekcia',
										'field' => 'slug',
										'terms' => $the_kolekcia
									);
								}

								// Add special hodnotenie (evaluation) taxonomy handling
								if ( ! empty( $the_hodnotenie ) ) {
									if ( in_array( 'ocenene', $the_hodnotenie ) ) {
										$ocenene = array_diff( get_terms( array(
											'taxonomy' => 'scd_ocenenie',
											'fields' => 'id=>name',
										) ), array( 'nominované', 'vystavené', 'nepostupujúce do druhého kola', 'postupujúce do druhého kola' ) ); // TODO translate terms
										$tax_query[] = array(
											'taxonomy' => 'scd_ocenenie',
											'terms' => array_keys( $ocenene ),
											'operator' => 'IN',
										);
										$hodnotenie_remaining = array_diff( $the_hodnotenie, array( 'ocenene' ) );
									} else {
										$hodnotenie_remaining = $the_hodnotenie;
									}
									if ( ! empty( $hodnotenie_remaining ) ) {
										$tax_query[] = array( 'taxonomy' => 'scd_ocenenie', 'field' => 'slug', 'terms' => $hodnotenie_remaining ); // TODO translate terms
									}
								}

								if ( ! empty( $tax_query ) ) {
									$relevance_args['tax_query'] = $tax_query;
									if ( count( $tax_query ) > 1 ) {
										$relevance_args['tax_query']['relation'] = 'AND';
									}
								}

								// Add meta queries if they exist
								$meta_query = array();
								if ( ! empty( $the_dizajner ) ) {
									$meta_query[] = array( 'key' => 'dizajner_rola_%_dizajner', 'value' => $the_dizajner );
								}
								if ( ! empty( $the_vyrobca ) ) {
									$meta_query[] = array( 'key' => 'vyrobca_rola_%_vyrobca', 'value' => $the_vyrobca );
								}

								if ( ! empty( $meta_query ) ) {
									$relevance_args['meta_query'] = $meta_query;
									if ( count( $meta_query ) > 1 ) {
										$relevance_args['meta_query']['relation'] = 'AND';
									}
								}
								
								// Get and display relevance data
								$relevance_data = $relevance_sorter->get_relevance_works( $relevance_args );

								// Update global $wp_query for proper pagination support
								global $wp_query;
								$total_works = $relevance_sorter->get_relevance_works_count( $relevance_args );
								$wp_query->found_posts = $total_works;
								$wp_query->max_num_pages = ceil( $total_works / 12 );

								if ( ! empty( $relevance_data ) ) {
									foreach ( $relevance_data as $group ) {
										// Only show category header if this is a new category (indicated by show_header flag)
										// For initial page load (non-AJAX), always show headers
										if ( ! isset( $group['show_header'] ) || $group['show_header'] ) {
											echo '<div class="hierarchical-category-group col-12 margin-bottom-2">';
											echo '<h3 class="hierarchical-category-title">' . esc_html( $group['category']->name ) . '</h3>';
											echo '</div>';
										}

										// Work items as direct children of the main grid
										foreach ( $group['works'] as $work ) {
											// Set up global post data
											$GLOBALS['post'] = $work;
											setup_postdata( $work );

											// Use the existing template part (it has its own col-* classes)
											get_template_part( 'template-parts/archive', 'dielo' );
										}
									}

									wp_reset_postdata();
								} else {
									echo '<p>'; esc_html_e( 'Ľutujeme, ale nenašli sme žiadne diela.', 'scd' ); echo '</p>';
								}
							} else {
								// Use standard display logic
								if ( have_posts() ) {
									while ( have_posts() ) {
										the_post();
										get_template_part( 'template-parts/archive', 'dielo' );
									}
								} else {
									echo '<p>'; esc_html_e( 'Ľutujeme, ale nenašli sme žiadne diela.', 'scd' ); echo '</p>';
								} 
							}
						?>
						</div>
					</div>
					<?php scd_load_more_button(); ?>
					<!-- End Section people-->
				</div>
			</div>
		</div>
	</section>
</main>
<!-- End Main content-->

<!-- Mobile Filters Modal -->
<div class="modal__overlay js-modal-overlay"></div>
<div class="modal js-modal" data-modal="mobile-filters">
	<div class="modal__scroll">
		<div class="modal__box">
			<div class="modal__close js-close-modal">
				<svg class="icon-svg">
					<use xlink:href="<?php scd_asset( 'sprites.svg' ); ?>#icon-close"></use>
				</svg>
			</div>
			<div class="modal__body js-modal-body">
				<div class="mobile-filters-content">
					<!-- Filter content will be moved here via JavaScript -->
				</div>
			</div>
		</div>
	</div>
</div>

<?php
get_footer();
